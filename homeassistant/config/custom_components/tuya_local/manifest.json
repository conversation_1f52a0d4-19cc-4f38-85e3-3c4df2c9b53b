{"domain": "tuya_local", "name": "Tuya Local", "codeowners": ["@make-all"], "config_flow": true, "dependencies": [], "documentation": "https://github.com/make-all/tuya-local", "integration_type": "device", "iot_class": "local_push", "issue_tracker": "https://github.com/make-all/tuya-local/issues", "loggers": ["tuya_local", "<PERSON><PERSON><PERSON>"], "requirements": ["tinytuya==1.17.3", "tuya-device-sharing-sdk~=0.2.1"], "version": "2025.8.0"}