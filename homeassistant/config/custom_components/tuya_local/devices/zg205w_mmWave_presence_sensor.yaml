name: mmWave presence sensor
products:
  - id: qbapy7r4deyp6zbx
    model: ZG-205W
  - id: uykwakpl6rbxvjdd
    model: ZG-205W/A
entities:
  - entity: binary_sensor
    class: occupancy
    dps:
      - id: 1
        type: string
        name: sensor
        mapping:
          - dps_val: presence
            value: true
          - dps_val: none
            value: false
            # - id: 111
            #   name: breathingselftest
  - entity: number
    name: Motion sensitivity
    category: config
    icon: mdi:motion-sensor
    dps:
      - id: 2
        type: integer
        name: value
        optional: true
        range:
          min: 0
          max: 10
  - entity: number
    name: Motion detection distance
    category: config
    icon: mdi:signal-distance-variant
    dps:
      - id: 3
        type: integer
        name: value
        optional: true
        unit: m
        range:
          min: 0
          max: 1000
        mapping:
          - scale: 100
            step: 10
  - entity: number
    name: Stationary detection distance
    category: config
    icon: mdi:signal-distance-variant
    dps:
      - id: 4
        type: integer
        name: value
        unit: m
        optional: true
        range:
          min: 0
          max: 600
        mapping:
          - scale: 100
            step: 10
  - entity: sensor
    class: illuminance
    dps:
      - id: 101
        type: integer
        name: sensor
        unit: lx
        class: measurement
  - entity: number
    name: Stationary sensitivity
    category: config
    icon: mdi:motion-sensor
    dps:
      - id: 102
        type: integer
        name: value
        optional: true
        range:
          min: 0
          max: 10
  - entity: light
    translation_key: indicator
    category: config
    dps:
      - id: 103
        type: boolean
        name: switch
        optional: true
  - entity: select
    name: Alarm mode
    class: enum
    icon: mdi:shield-home
    category: config
    dps:
      - id: 104
        type: string
        name: option
        optional: true
        mapping:
          - dps_val: armed
            value: Armed
          - dps_val: disarm
            value: Disarm
          - dps_val: sos
            value: Alarm
  - entity: number
    name: Small motion detection distance
    category: config
    icon: mdi:signal-distance-variant
    dps:
      - id: 105
        type: integer
        name: value
        optional: true
        unit: m
        range:
          min: 0
          max: 600
        mapping:
          - scale: 100
            step: 10
  - entity: number
    name: Small motion sensitivity
    category: config
    icon: mdi:motion-sensor
    dps:
      - id: 106
        type: integer
        name: value
        optional: true
        range:
          min: 0
          max: 10
  - entity: select
    name: Alarm volume
    class: enum
    icon: mdi:volume-high
    category: config
    dps:
      - id: 107
        type: string
        name: option
        optional: true
        mapping:
          - dps_val: high
            value: High
          - dps_val: middle
            value: Middle
          - dps_val: low
            value: Low
          - dps_val: mute
            value: Mute
  - entity: number
    name: Exit delay
    category: config
    class: duration
    icon: mdi:timer-sand
    dps:
      - id: 108
        type: integer
        name: value
        optional: true
        unit: s
        range:
          min: 0
          max: 28800
  - entity: number
    name: Alarm duration
    category: config
    class: duration
    icon: mdi:timer
    dps:
      - id: 109
        type: integer
        name: value
        unit: min
        optional: true
        range:
          min: 1
          max: 30
  - entity: number
    name: Motion minimum detection distance
    category: config
    icon: mdi:signal-distance-variant
    dps:
      - id: 110
        type: integer
        name: value
        unit: m
        optional: true
        range:
          min: 0
          max: 1000
        mapping:
          - scale: 100
            step: 10
  - entity: number
    name: Small motion minimum detection distance
    category: config
    icon: mdi:signal-distance-variant
    dps:
      - id: 111
        type: integer
        name: value
        unit: m
        optional: true
        range:
          min: 0
          max: 1000
        mapping:
          - scale: 100
            step: 10
  - entity: number
    name: Breathing minimum detection distance
    category: config
    icon: mdi:signal-distance-variant
    dps:
      - id: 112
        type: integer
        name: value
        optional: true
        unit: m
        range:
          min: 0
          max: 1000
        mapping:
          - scale: 100
            step: 10
  - entity: number
    name: Adaptive testing time
    category: config
    class: duration
    icon: mdi:timer
    dps:
      - id: 113
        type: integer
        name: value
        optional: true
        unit: s
        range:
          min: 0
          max: 300
  - entity: switch
    name: Adaptive test
    category: config
    icon: mdi:test-tube
    dps:
      - id: 114
        name: switch
        type: boolean
        optional: true
  - entity: button
    translation_key: factory_reset
    category: config
    dps:
      - id: 115
        type: boolean
        name: button
        optional: true
  - entity: sensor
    name: Movement state
    class: enum
    icon: mdi:cursor-move
    dps:
      - id: 116
        type: string
        name: sensor
        mapping:
          - dps_val: large_move
            value: Large
          - dps_val: small_move
            value: Small
          - dps_val: breathe
            value: Breathe
          - dps_val: none
            value: None
  - entity: switch
    name: Small motion self-test
    category: config
    icon: "mdi:test-tube"
    dps:
      - id: 117
        name: switch
        type: boolean
        optional: true
  - entity: switch
    name: Breathing self-test
    category: config
    icon: "mdi:test-tube"
    dps:
      - id: 118
        name: switch
        type: boolean
        optional: true
  - entity: number
    name: Motion false detection
    category: config
    icon: mdi:alpha-x-circle
    dps:
      - id: 119
        type: integer
        name: value
        optional: true
        range:
          min: 0
          max: 10
  - entity: switch
    name: Breathing false detection
    category: config
    icon: mdi:lungs
    dps:
      - id: 120
        name: switch
        type: boolean
        optional: true
