name: 3 outlet + USB powerstrip
legacy_type: tellur_usb_power_strip
entities:
  - entity: switch
    translation_key: outlet_x
    translation_placeholders:
      x: "1"
    class: outlet
    dps:
      - id: 1
        type: boolean
        name: switch
  - entity: switch
    translation_key: outlet_x
    translation_placeholders:
      x: "2"
    class: outlet
    dps:
      - id: 2
        type: boolean
        name: switch
  - entity: switch
    translation_key: outlet_x
    translation_placeholders:
      x: "3"
    class: outlet
    dps:
      - id: 3
        type: boolean
        name: switch
  - entity: switch
    name: USB switch
    class: switch
    dps:
      - id: 7
        type: boolean
        name: switch
  - entity: time
    category: config
    translation_key: timer_x
    translation_placeholders:
      x: "1"
    dps:
      - id: 101
        name: second
        type: integer
        range:
          min: 0
          max: 86400
  - entity: time
    category: config
    translation_key: timer_x
    translation_placeholders:
      x: "2"
    dps:
      - id: 102
        name: second
        type: integer
        range:
          min: 0
          max: 86400
  - entity: time
    category: config
    translation_key: timer_x
    translation_placeholders:
      x: "3"
    dps:
      - id: 103
        name: second
        type: integer
        range:
          min: 0
          max: 86400
  - entity: time
    category: config
    translation_key: timer_x
    translation_placeholders:
      x: USB
    dps:
      - id: 105
        name: second
        type: integer
        range:
          min: 0
          max: 86400
  - entity: number
    category: config
    class: duration
    translation_key: timer_x
    translation_placeholders:
      x: "1"
    # 2025-07-20
    deprecated: time.timer_1
    dps:
      - id: 101
        name: value
        type: integer
        unit: min
        range:
          min: 0
          max: 86400
        mapping:
          - scale: 60
            step: 60
  - entity: number
    category: config
    class: duration
    translation_key: timer_x
    translation_placeholders:
      x: "2"
    # 2025-07-20
    deprecated: time.timer_2
    dps:
      - id: 102
        name: value
        type: integer
        unit: min
        range:
          min: 0
          max: 86400
        mapping:
          - scale: 60
            step: 60
  - entity: number
    category: config
    class: duration
    translation_key: timer_x
    translation_placeholders:
      x: "3"
    # 2025-07-20
    deprecated: time.timer_3
    dps:
      - id: 103
        name: value
        type: integer
        unit: min
        range:
          min: 0
          max: 86400
        mapping:
          - scale: 60
            step: 60
  - entity: number
    category: config
    name: USB timer
    # 2025-07-20
    deprecated: time.timer_usb
    class: duration
    translation_key: timer
    dps:
      - id: 105
        name: value
        type: integer
        unit: min
        range:
          min: 0
          max: 86400
        mapping:
          - scale: 60
            step: 60
