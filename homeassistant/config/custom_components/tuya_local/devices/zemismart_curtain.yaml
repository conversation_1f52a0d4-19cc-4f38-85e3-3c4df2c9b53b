name: Zemismart curtain
entities:
  - entity: cover
    class: curtain
    dps:
      - id: 1
        name: control
        type: string
        mapping:
          - dps_val: open
            value: open
          - dps_val: close
            value: close
          - dps_val: stop
            value: stop
      - id: 2
        name: position
        type: integer
        range:
          min: 0
          max: 100
        mapping:
          - invert: true
      - id: 3
        name: current_position
        type: integer
        range:
          min: 0
          max: 100
        mapping:
          - invert: true
      - id: 7
        name: work_state
        type: string
        optional: true
        # seems intended to match action, but doesn't change in observation
  - entity: switch
    name: Reverse
    category: config
    icon: "mdi:arrow-u-down-left"
    dps:
      - id: 5
        type: boolean
        name: switch
  - entity: binary_sensor
    class: problem
    category: diagnostic
    dps:
      - id: 12
        type: bitfield
        name: sensor
        optional: true
        persist: false
        mapping:
          - dps_val: 0
            value: false
          - dps_val: null
            value: false
          - value: true
      - id: 12
        name: fault_code
        type: bitfield
        optional: true
