name: Humidifier
products:
  - id: 7naqdm8whbezn28v
    manufacturer: Wilfa
    model: Haze
entities:
  - entity: humidifier
    class: humidifier
    dps:
      - id: 1
        name: switch
        type: boolean
        mapping:
          - dps_val: true
            icon: "mdi:air-humidifier"
          - dps_val: false
            icon: "mdi:air-humidifier-off"
      - id: 13
        name: humidity
        type: integer
        range:
          min: 30
          max: 90
        mapping:
          - step: 5
            constraint: mode
            conditions:
              - dps_val: auto
                invalid: true
      - id: 14
        type: integer
        name: current_humidity
      - id: 24
        type: string
        name: mode
        mapping:
          - dps_val: auto
            value: auto
          - dps_val: humidity
            value: normal
      - id: 20
        type: integer
        name: unknown_20
  - entity: fan
    dps:
      - id: 1
        name: switch
        type: boolean
      - id: 23
        type: string
        name: speed
        mapping:
          - dps_val: level_0
            value: 0
          - dps_val: level_1
            value: 20
          - dps_val: level_2
            value: 40
          - dps_val: level_3
            value: 60
          - dps_val: level_4
            value: 80
          - dps_val: level_5
            value: 100
  - entity: light
    name: Mood
    category: config
    dps:
      - id: 5
        type: boolean
        name: switch
        mapping:
          - dps_val: true
            icon: "mdi:led-on"
          - dps_val: false
            icon: "mdi:led-off"
  - entity: switch
    name: Sound
    category: config
    dps:
      - id: 8
        name: switch
        type: boolean
        mapping:
          - dps_val: true
            icon: "mdi:volume-high"
          - dps_val: false
            icon: "mdi:volume-off"
  - entity: sensor
    class: temperature
    dps:
      - id: 10
        name: sensor
        type: integer
        class: measurement
      - id: 18
        name: unit
        type: string
        mapping:
          - dps_val: c
            value: C
          - dps_val: f
            value: F
  - entity: light
    translation_key: display
    category: config
    dps:
      - id: 16
        type: boolean
        name: switch
        mapping:
          - dps_val: true
            value: false
          - dps_val: false
            value: true
  - entity: select
    category: config
    translation_key: temperature_unit
    dps:
      - id: 18
        name: option
        type: string
        mapping:
          - dps_val: c
            value: celsius
          - dps_val: f
            value: fahrenheit
  - entity: select
    translation_key: timer
    category: config
    dps:
      - id: 19
        name: option
        type: string
        mapping:
          - dps_val: cancel
            value: cancel
          - dps_val: "1"
            value: "1h"
          - dps_val: "2"
            value: "2h"
          - dps_val: "3"
            value: "3h"
          - dps_val: "4"
            value: "4h"
          - dps_val: "5"
            value: "5h"
          - dps_val: "6"
            value: "6h"
          - dps_val: "7"
            value: "7h"
          - dps_val: "8"
            value: "8h"
          - dps_val: "9"
            value: "9h"
          - dps_val: "10"
            value: "10h"
          - dps_val: "11"
            value: "11h"
          - dps_val: "12"
            value: "12h"
  - entity: switch
    name: Heat
    category: config
    icon: "mdi:air-purifier"
    dps:
      - id: 26
        name: switch
        type: boolean
  - entity: switch
    translation_key: ionizer
    category: config
    dps:
      - id: 35
        name: switch
        type: boolean
  - entity: binary_sensor
    translation_key: tank_empty
    category: diagnostic
    dps:
      - id: 22
        type: bitfield
        name: sensor
        mapping:
          - dps_val: 1
            value: true
          - value: false
  - entity: binary_sensor
    class: problem
    category: diagnostic
    dps:
      - id: 22
        type: bitfield
        name: sensor
        mapping:
          - dps_val: 0
            value: false
          - dps_val: 1
            value: false
          - value: true
      - id: 22
        type: bitfield
        name: fault_code
