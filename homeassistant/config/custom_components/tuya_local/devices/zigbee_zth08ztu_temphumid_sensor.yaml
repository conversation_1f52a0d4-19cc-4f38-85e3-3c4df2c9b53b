name: Temperature humidity sensor
products:
  - id: cirvgep4
    model: ZTH08ZTU
entities:
  - entity: sensor
    class: temperature
    dps:
      - id: 1
        type: integer
        name: sensor
        unit: C
        class: measurement
        mapping:
          - scale: 10
  - entity: sensor
    class: humidity
    dps:
      - id: 2
        type: integer
        name: sensor
        unit: "%"
        class: measurement
  - entity: sensor
    class: battery
    category: diagnostic
    dps:
      - id: 3
        type: integer
        name: sensor
        optional: true
        unit: "%"
        mapping:
          - dps_val: low
            value: 20
          - dps_val: middle
            value: 50
          - dps_val: high
            value: 90
