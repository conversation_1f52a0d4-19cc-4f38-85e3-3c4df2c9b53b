# DPS sample:  { "1": true, "2": true, "3": "MID", "7": true, "8": 1000,
#                "9": "colourful1", "10": "", "11": 0 }
name: Aroma diffuser with lamp
products:
  - id: arkdxfhlkpdyof9x
    model: YX316WIFI
entities:
  - entity: fan
    translation_key: aroma_diffuser
    dps:
      - id: 2
        name: switch
        type: boolean
      - id: 3
        type: string
        name: preset_mode
        mapping:
          - dps_val: "MID"
            value: "medium"
          - dps_val: "2H"
            value: "low"
          - dps_val: "4H"
            value: "high"
          - dps_val: "10SEC"
            value: "intermittent"
  # "master" switch - turns on and off both light and diffuser
  - entity: switch
    name: Power
    dps:
      - id: 1
        name: switch
        type: boolean
  - entity: light
    icon: "mdi:light-recessed"
    dps:
      - id: 7
        name: switch
        type: boolean
      - id: 9
        name: color_mode
        type: string
        mapping:
          - dps_val: white
            value: white
          - dps_val: colour
            value: hs
          - dps_val: colourful1
            value: Colorful
      - id: 8
        name: brightness
        type: integer
        range:
          min: 10
          max: 1000
      - id: 10
        name: rgbhsv
        type: hex
        format:
          - name: r
            bytes: 1
          - name: g
            bytes: 1
          - name: b
            bytes: 1
          - name: h
            bytes: 2
            range:
              min: 0
              max: 360
          - name: s
            bytes: 1
            range:
              min: 0
              max: 255
          - name: v
            bytes: 1
            range:
              min: 0
              max: 255
  - entity: switch
    name: Sound
    category: config
    dps:
      - id: 6
        type: boolean
        name: switch
        optional: true
        mapping:
          - dps_val: true
            icon: "mdi:speaker"
          - dps_val: false
            icon: "mdi:speaker-off"
          - dps_val: null
            icon: "mdi:speaker-off"
            value: false
            hidden: true
  - entity: binary_sensor
    class: problem
    category: diagnostic
    dps:
      - id: 11
        type: bitfield
        optional: true
        name: sensor
        mapping:
          - dps_val: 0
            value: false
          - dps_val: null
            value: false
          - value: true
      - id: 11
        type: bitfield
        optional: true
        name: fault_code
