name: Energy meter
products:
  - id: fygozcnralhwbauo
    manufacturer: Yagusmart/Zemismart
    model: 3PN 63A
entities:
  - entity: sensor
    class: energy
    dps:
      - id: 1
        type: integer
        name: sensor
        unit: kWh
        class: total_increasing
        mapping:
          - scale: 100
      - id: 17
        type: string
        name: alarm_set_1
        optional: true
      - id: 18
        type: string
        name: alarm_set_2
        optional: true
      - id: 19
        type: string
        name: breaker_id
        optional: true
  - entity: sensor
    translation_key: energy_produced
    class: energy
    category: diagnostic
    dps:
      - id: 2
        type: integer
        name: sensor
        unit: kWh
        class: measurement
        mapping:
          - scale: 100
  - entity: binary_sensor
    class: problem
    category: diagnostic
    dps:
      - id: 9
        type: bitfield
        name: sensor
        mapping:
          - dps_val: 0
            value: false
          - value: true
      - id: 9
        type: bitfield
        name: fault_code
  - entity: switch
    name: Prepay
    category: config
    icon: "mdi:hand-coin"
    dps:
      - id: 11
        type: boolean
        name: switch
  - entity: button
    name: Balance reset
    category: config
    dps:
      - id: 12
        type: boolean
        name: button
  - entity: sensor
    name: Energy balance
    class: energy_storage
    category: diagnostic
    dps:
      - id: 13
        type: integer
        name: sensor
        unit: kWh
        class: measurement
        mapping:
          - scale: 100
  - entity: number
    name: Charge energy
    class: energy_storage
    category: config
    icon: "mdi:cash"
    dps:
      - id: 14
        type: integer
        name: value
        optional: true
        unit: kWh
        range:
          min: 0
          max: 999999
        mapping:
          - scale: 100
  - entity: sensor
    translation_key: voltage_x
    translation_placeholders:
      x: A
    class: voltage
    category: diagnostic
    dps:
      - id: 6
        type: base64
        name: sensor
        optional: true
        unit: V
        class: measurement
        mask: "FFFF000000000000"
        mapping:
          - scale: 10
  - entity: sensor
    translation_key: current_x
    translation_placeholders:
      x: A
    class: current
    category: diagnostic
    dps:
      - id: 6
        type: base64
        optional: true
        name: sensor
        unit: A
        class: measurement
        mask: "0000FFFFFF000000"
        mapping:
          - scale: 1000
  - entity: sensor
    translation_key: power_x
    translation_placeholders:
      x: A
    class: power
    category: diagnostic
    dps:
      - id: 6
        type: base64
        optional: true
        name: sensor
        unit: kW
        class: measurement
        mask: "0000000000FFFFFF"
        mapping:
          - scale: 1000
  - entity: sensor
    translation_key: voltage_x
    translation_placeholders:
      x: B
    class: voltage
    category: diagnostic
    dps:
      - id: 7
        type: base64
        name: sensor
        optional: true
        unit: V
        class: measurement
        mask: "FFFF000000000000"
        mapping:
          - scale: 10
  - entity: sensor
    translation_key: current_x
    translation_placeholders:
      x: B
    class: current
    category: diagnostic
    dps:
      - id: 7
        type: base64
        optional: true
        name: sensor
        unit: A
        class: measurement
        mask: "0000FFFFFF000000"
        mapping:
          - scale: 1000
  - entity: sensor
    translation_key: power_x
    translation_placeholders:
      x: B
    class: power
    category: diagnostic
    dps:
      - id: 7
        type: base64
        optional: true
        name: sensor
        unit: kW
        class: measurement
        mask: "0000000000FFFFFF"
        mapping:
          - scale: 1000
  - entity: sensor
    translation_key: voltage_x
    translation_placeholders:
      x: C
    class: voltage
    category: diagnostic
    dps:
      - id: 8
        type: base64
        name: sensor
        optional: true
        unit: V
        class: measurement
        mask: "FFFF000000000000"
        mapping:
          - scale: 10
  - entity: sensor
    translation_key: current_x
    translation_placeholders:
      x: C
    class: current
    category: diagnostic
    dps:
      - id: 8
        type: base64
        optional: true
        name: sensor
        unit: A
        class: measurement
        mask: "0000FFFFFF000000"
        mapping:
          - scale: 1000
  - entity: sensor
    translation_key: power_x
    translation_placeholders:
      x: C
    class: power
    category: diagnostic
    dps:
      - id: 8
        type: base64
        optional: true
        name: sensor
        unit: kW
        class: measurement
        mask: "0000000000FFFFFF"
        mapping:
          - scale: 1000
