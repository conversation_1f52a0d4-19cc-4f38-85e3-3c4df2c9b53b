name: Windcalm Ceiling Fan with Light
entities:
  - entity: fan
    dps:
      - id: 60
        type: boolean
        name: switch
      - id: 62
        type: integer
        name: speed
        range:
          min: 1
          max: 6
      - id: 63
        type: string
        name: direction
  - entity: light
    dps:
      - id: 20
        type: boolean
        name: switch
      - id: 22
        name: brightness
        type: integer
        optional: true
        range:
          min: 10
          max: 1000
      - id: 23
        name: color_temp
        type: integer
        range:
          min: 0
          max: 1000
        mapping:
          - target_range:
              min: 3000
              max: 6500
  - entity: number
    class: duration
    translation_key: timer
    category: config
    dps:
      - id: 64
        type: integer
        name: value
        unit: min
        range:
          min: 0
          max: 540
  - entity: switch
    name: Beep
    icon: "mdi:bell"
    category: config
    dps:
      - id: 66
        type: boolean
        name: switch
        optional: true
