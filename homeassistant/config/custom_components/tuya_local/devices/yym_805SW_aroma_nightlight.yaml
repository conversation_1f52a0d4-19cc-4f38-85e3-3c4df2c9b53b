# DPS sample: {'updated_at': 1674433099.9169242, '1': True, '2': 'large',
#              '3': '1', '5': True, '6': 'colourful1', '8': '001fff00e8ffff'}
name: Aroma diffuser
products:
  - id: c0nh3LmEk0NDebrq
    model: YYM-805SW
  - id: kgczidn0awas0prr
    manufacturer: Tellur
entities:
  - entity: fan
    translation_key: aroma_diffuser
    dps:
      - id: 1
        name: switch
        type: boolean
      - id: 2
        name: speed
        type: string
        mapping:
          - conditions:
              - dps_val:
                  - large
                  - small
                mapping:
                  - dps_val: large
                    value: 100
                  - dps_val: small
                    value: 50
              - dps_val:
                  - high
                  - low
                mapping:
                  - dps_val: high
                    value: 100
                  - dps_val: low
                    value: 50
  - entity: light
    translation_key: nightlight
    dps:
      - id: 5
        name: switch
        type: boolean
      - id: 6
        name: color_mode
        type: string
        mapping:
          - dps_val: colour
            value: hs
          - dps_val: colourful1
            value: Colorful
      - id: 8
        name: rgbhsv
        type: hex
        format:
          - name: r
            bytes: 1
          - name: g
            bytes: 1
          - name: b
            bytes: 1
          - name: h
            bytes: 2
            range:
              min: 0
              max: 360
          - name: s
            bytes: 1
            range:
              min: 0
              max: 255
          - name: v
            bytes: 1
            range:
              min: 0
              max: 255
  - entity: select
    translation_key: timer
    category: config
    dps:
      - id: 3
        name: option
        type: string
        mapping:
          - dps_val: "cancel"
            value: cancel
          - dps_val: "1"
            value: "1h"
          - dps_val: "3"
            value: "3h"
          - dps_val: "6"
            value: "6h"
      - id: 4
        name: remaining
        optional: true
        type: integer
        range:
          min: 0
          max: 360
  - entity: binary_sensor
    class: problem
    category: diagnostic
    dps:
      - id: 9
        type: bitfield
        optional: true
        name: sensor
        mapping:
          - dps_val: 0
            value: false
          - dps_val: null
            value: false
          - value: true
      - id: 9
        type: bitfield
        name: fault_code
        optional: true
