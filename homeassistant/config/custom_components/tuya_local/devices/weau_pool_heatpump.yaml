name: W'Eau pool heatpump
entities:
  - entity: climate
    translation_only_key: pool_heatpump
    dps:
      - id: 1
        type: boolean
        name: hvac_mode
        mapping:
          - dps_val: false
            value: "off"
          - dps_val: true
            constraint: preset_mode
            conditions:
              - dps_val: eco
                value: heat
              - dps_val: hot
                value: heat
              - dps_val: cold
                value: cool
              - dps_val: auto
                value: heat_cool
      - id: 2
        type: integer
        name: temperature
        unit: C
        range:
          min: 7
          max: 60
      - id: 3
        type: integer
        name: current_temperature
        mapping:
          - scale: 10
      - id: 4
        type: string
        name: preset_mode
        mapping:
          - dps_val: hot
            value: quick_heat
          - dps_val: eco
            value: quiet_heat
          - dps_val: cold
            value: quiet_cool
          - dps_val: auto
            value: auto
  - entity: binary_sensor
    class: problem
    category: diagnostic
    dps:
      - id: 6
        type: bitfield
        name: sensor
        mapping:
          - dps_val: 0
            value: false
          - value: true
      - id: 6
        name: fault_code
        type: bitfield
