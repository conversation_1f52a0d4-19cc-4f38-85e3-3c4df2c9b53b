name: Wetair WCH-750 AL heater
entities:
  - entity: climate
    translation_only_key: heater
    dps:
      - id: 1
        type: boolean
        mapping:
          - dps_val: false
            value: "off"
          - dps_val: true
            value: heat
        name: hvac_mode
      - id: 2
        type: integer
        range:
          min: 10
          max: 35
        unit: C
        mapping:
          - constraint: preset_mode
            conditions:
              - dps_val: mod_antiforst
                invalid: true
        name: temperature
      - id: 4
        type: string
        name: preset_mode
        mapping:
          - dps_val: mod_free
            value: comfort
          - dps_val: mod_max12h
            value: boost
          - dps_val: mod_antiforst
            value: away
      - id: 11
        type: string
        name: hvac_action
        mapping:
          - dps_val: heating
            value: heating
          - dps_val: warm
            value: idle
      - id: 21
        type: integer
        name: unknown_21
  - entity: light
    category: config
    translation_key: display
    dps:
      - id: 101
        type: string
        name: brightness
        mapping:
          - dps_val: level0
            value: 0
          - dps_val: level1
            value: 85
          - dps_val: level2
            value: 170
          - dps_val: level3
            value: 255
  - entity: select
    translation_key: timer
    class: duration
    category: config
    dps:
      - id: 19
        type: string
        name: option
        mapping:
          - dps_val: "0h"
            value: cancel
          - dps_val: "1h"
            value: "1h"
          - dps_val: "2h"
            value: "2h"
          - dps_val: "3h"
            value: "3h"
          - dps_val: "4h"
            value: "4h"
          - dps_val: "5h"
            value: "5h"
          - dps_val: "6h"
            value: "6h"
          - dps_val: "7h"
            value: "7h"
          - dps_val: "8h"
            value: "8h"
          - dps_val: "9h"
            value: "9h"
          - dps_val: "10h"
            value: "10h"
          - dps_val: "11h"
            value: "11h"
          - dps_val: "12h"
            value: "12h"
          - dps_val: "13h"
            value: "13h"
          - dps_val: "14h"
            value: "14h"
          - dps_val: "15h"
            value: "15h"
          - dps_val: "16h"
            value: "16h"
          - dps_val: "17h"
            value: "17h"
          - dps_val: "18h"
            value: "18h"
          - dps_val: "19h"
            value: "19h"
          - dps_val: "20h"
            value: "20h"
          - dps_val: "21h"
            value: "21h"
          - dps_val: "22h"
            value: "22h"
          - dps_val: "23h"
            value: "23h"
          - dps_val: "24h"
            value: "24h"
  - entity: sensor
    category: diagnostic
    class: duration
    translation_key: time_remaining
    dps:
      - id: 20
        type: integer
        name: sensor
        unit: min
