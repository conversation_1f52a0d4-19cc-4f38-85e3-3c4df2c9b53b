name: Aroma diffuser
products:
  - id: yishet3p12fvohmd
    manufacturer: Liplasting
    model: YX-025WB
entities:
  - entity: fan
    translation_key: aroma_diffuser
    dps:
      - id: 2
        name: switch
        type: boolean
      - id: 3
        type: string
        name: preset_mode
        mapping:
          - dps_val: small
            value: low
          - dps_val: large
            value: high
          - dps_val: interval
            value: intermittent
  - entity: light
    icon: "mdi:light-recessed"
    dps:
      - id: 7
        name: switch
        type: boolean
      - id: 9
        name: color_mode
        type: string
        mapping:
          - dps_val: colour
            value: hs
          - dps_val: colourful1
            value: Colorful
      - id: 8
        name: brightness
        optional: true
        type: integer
        range:
          min: 0
          max: 255
      - id: 10
        name: rgbhsv
        optional: true
        type: hex
        format:
          - name: r
            bytes: 1
          - name: g
            bytes: 1
          - name: b
            bytes: 1
          - name: h
            bytes: 2
            range:
              min: 0
              max: 360
          - name: s
            bytes: 1
            range:
              min: 0
              max: 255
          - name: v
            bytes: 1
            range:
              min: 0
              max: 255
  - entity: binary_sensor
    class: problem
    category: diagnostic
    dps:
      - id: 11
        type: bitfield
        name: sensor
        mapping:
          - dps_val: 0
            value: false
          - value: true
      - id: 11
        type: bitfield
        name: fault_code
  - entity: select
    translation_key: timer
    category: config
    dps:
      - id: 4
        type: string
        name: option
        mapping:
          - dps_val: 2h
            value: "2h"
          - dps_val: 4h
            value: "4h"
          - dps_val: cancel
            value: cancel
  - entity: sensor
    translation_key: time_remaining
    class: duration
    category: diagnostic
    dps:
      - id: 5
        type: integer
        name: sensor
        unit: min
        range:
          min: 0
          max: 360
