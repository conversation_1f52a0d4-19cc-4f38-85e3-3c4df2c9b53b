name: Carbon monoxide detector
products:
  - id: s3xbz2blagibrod5
    manufacturer: Xtreme
    model: TC20
entities:
  - entity: binary_sensor
    class: gas
    dps:
      - id: 1
        type: string
        name: sensor
        mapping:
          - dps_val: alarm
            value: true
          - dps_val: normal
            value: false
  - entity: button
    name: Self check
    category: config
    dps:
      - id: 8
        type: boolean
        name: button
  - entity: sensor
    name: Self check
    class: enum
    category: diagnostic
    dps:
      - id: 9
        type: string
        name: sensor
        mapping:
          - dps_val: checking
            value: checking
          - dps_val: check_success
            value: ok
          - dps_val: check_failure
            value: failed
          - dps_val: others
            value: unknown
  - entity: binary_sensor
    class: problem
    category: diagnostic
    dps:
      - id: 11
        type: bitfield
        name: sensor
        mapping:
          - dps_val: 0
            value: false
          - value: true
      - id: 11
        type: bitfield
        name: fault_code
