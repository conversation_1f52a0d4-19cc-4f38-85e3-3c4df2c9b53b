name: Energy monitoring circuit breaker
entities:
  - entity: switch
    dps:
      - id: 1
        type: boolean
        name: switch
      - id: 115
        type: string
        name: serial_number
      - id: 118
        type: integer
        name: RkWh
      - id: 119
        type: base64
        name: historical_voltage
        optional: true
      - id: 120
        type: base64
        name: historical_current
        optional: true
  - entity: time
    translation_key: timer
    category: config
    dps:
      - id: 9
        type: integer
        name: second
        range:
          min: 0
          max: 86400
  - entity: number
    class: duration
    translation_key: timer
    # 2025-07-20
    deprecated: time.timer
    category: config
    dps:
      - id: 9
        type: integer
        name: value
        unit: min
        range:
          min: 0
          max: 86400
        mapping:
          - scale: 60
            step: 60
  - entity: binary_sensor
    class: problem
    category: diagnostic
    dps:
      - id: 26
        type: bitfield
        name: sensor
        mapping:
          - dps_val: 0
            value: false
          - value: true
      - id: 26
        type: bitfield
        name: fault_code
  - entity: select
    translation_key: initial_state
    category: config
    dps:
      - id: 38
        type: string
        name: option
        mapping:
          - dps_val: "0"
            value: "off"
          - dps_val: "1"
            value: "on"
          - dps_val: "2"
            value: memory
  - entity: lock
    translation_key: child_lock
    category: config
    dps:
      - id: 40
        type: boolean
        name: lock
  - entity: sensor
    translation_key: voltage_x
    translation_placeholders:
      x: A
    class: voltage
    category: diagnostic
    dps:
      - id: 101
        type: base64
        name: sensor
        unit: V
        class: measurement
        optional: true
        force: true
        mask: "FFFF00000000"
        mapping:
          - scale: 10
  - entity: sensor
    translation_key: voltage_x
    translation_placeholders:
      x: B
    class: voltage
    category: diagnostic
    dps:
      - id: 101
        type: base64
        name: sensor
        unit: V
        class: measurement
        optional: true
        force: true
        mask: "0000FFFF0000"
        mapping:
          - scale: 10
  - entity: sensor
    translation_key: voltage_x
    translation_placeholders:
      x: C
    class: voltage
    category: diagnostic
    dps:
      - id: 101
        type: base64
        name: sensor
        unit: V
        class: measurement
        optional: true
        force: true
        mask: "00000000FFFF"
        mapping:
          - scale: 10
  - entity: sensor
    translation_key: current_x
    translation_placeholders:
      x: A
    class: current
    category: diagnostic
    dps:
      - id: 102
        type: base64
        name: sensor
        unit: A
        class: measurement
        optional: true
        force: true
        mask: "FFFFFF000000000000"
        mapping:
          - scale: 1000
  - entity: sensor
    translation_key: current_x
    translation_placeholders:
      x: B
    class: current
    category: diagnostic
    dps:
      - id: 102
        type: base64
        name: sensor
        unit: A
        class: measurement
        optional: true
        force: true
        mask: "000000FFFFFF000000"
        mapping:
          - scale: 1000
  - entity: sensor
    translation_key: current_x
    translation_placeholders:
      x: C
    class: current
    category: diagnostic
    dps:
      - id: 102
        type: base64
        name: sensor
        unit: A
        class: measurement
        optional: true
        force: true
        mask: "000000000000FFFFFF"
        mapping:
          - scale: 1000
  - entity: sensor
    name: Total power
    class: power
    category: diagnostic
    dps:
      - id: 103
        type: base64
        name: sensor
        unit: kW
        class: measurement
        optional: true
        force: true
        mask: "FFFFFF000000000000000000"
        mapping:
          - scale: 10000
  - entity: sensor
    translation_key: power_x
    translation_placeholders:
      x: A
    class: power
    category: diagnostic
    dps:
      - id: 103
        type: base64
        name: sensor
        unit: kW
        class: measurement
        optional: true
        force: true
        mask: "000000FFFFFF000000000000"
        mapping:
          - scale: 10000
  - entity: sensor
    translation_key: power_x
    translation_placeholders:
      x: B
    class: power
    category: diagnostic
    dps:
      - id: 103
        type: base64
        name: sensor
        unit: kW
        class: measurement
        optional: true
        force: true
        mask: "000000000000FFFFFF000000"
        mapping:
          - scale: 10000
  - entity: sensor
    translation_key: power_x
    translation_placeholders:
      x: C
    class: power
    category: diagnostic
    dps:
      - id: 103
        type: base64
        name: sensor
        unit: kW
        class: measurement
        optional: true
        force: true
        mask: "000000000000000000FFFFFF"
        mapping:
          - scale: 10000
  - entity: sensor
    name: Leakage current
    class: current
    category: diagnostic
    dps:
      - id: 104
        type: integer
        name: sensor
        unit: mA
        class: measurement
  - entity: sensor
    class: temperature
    category: diagnostic
    dps:
      - id: 105
        type: integer
        name: sensor
        unit: C
        class: measurement
  - entity: sensor
    name: Remaining energy
    category: diagnostic
    dps:
      - id: 107
        type: integer
        name: sensor
        unit: kWh
        class: measurement
        mapping:
          - scale: 100
  - entity: sensor
    class: energy
    dps:
      - id: 114
        type: integer
        name: sensor
        unit: kWh
        class: total_increasing
        mapping:
          - scale: 100
  - entity: binary_sensor
    name: Locking
    class: lock
    category: diagnostic
    dps:
      - id: 117
        type: boolean
        name: sensor
