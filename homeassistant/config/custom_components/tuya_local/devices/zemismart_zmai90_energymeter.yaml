name: Energy meter
products:
  - id: a6djabzmpbbp62gw
    manufacturer: Zemismart
    model: ZMAi-90
entities:
  - entity: switch
    class: switch
    category: config
    dps:
      - id: 1
        type: boolean
        name: switch
  - entity: sensor
    class: energy
    category: diagnostic
    dps:
      - id: 17
        type: integer
        name: sensor
        unit: kWh
        class: total_increasing
        mapping:
          - scale: 1000
  - entity: sensor
    class: current
    category: diagnostic
    dps:
      - id: 18
        type: integer
        name: sensor
        unit: A
        class: measurement
        mapping:
          - scale: 1000
  - entity: sensor
    class: power
    category: diagnostic
    dps:
      - id: 19
        type: integer
        name: sensor
        unit: W
        class: measurement
        mapping:
          - scale: 10
  - entity: sensor
    class: voltage
    category: diagnostic
    dps:
      - id: 20
        type: integer
        name: sensor
        unit: V
        class: measurement
        mapping:
          - scale: 10
  - entity: binary_sensor
    class: problem
    category: diagnostic
    dps:
      - id: 26
        type: bitfield
        optional: true
        name: sensor
        mapping:
          - dps_val: 0
            value: false
          - dps_val: null
            value: false
          - value: true
      - id: 26
        type: bitfield
        optional: true
        name: fault_code
