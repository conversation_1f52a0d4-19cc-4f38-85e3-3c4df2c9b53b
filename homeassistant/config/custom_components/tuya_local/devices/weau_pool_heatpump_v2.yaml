name: Pool heat pump
products:
  - id: zqwet1zcvj2wcxzw
    manufacturer: "W'Eau"
entities:
  - entity: climate
    translation_only_key: pool_heatpump
    dps:
      - id: 1
        type: boolean
        name: hvac_mode
        mapping:
          - dps_val: false
            value: "off"
          - dps_val: true
            constraint: preset_mode
            conditions:
              - dps_val: eheat
                value: heat
              - dps_val: ecool
                value: cool
              - dps_val: auto
                value: heat_cool
              - dps_val: [bheat, sheat]
                value: heat
              - dps_val: [bcool, scool]
                value: cool
      - id: 9
        type: integer
        name: temperature
        unit: C
        range:
          min: 7
          max: 60
      - id: 10
        type: integer
        name: current_temperature
        mapping:
          - scale: 10
      - id: 2
        type: string
        name: preset_mode
        mapping:
          - dps_val: sheat
            value: quiet_heat
          - dps_val: bheat
            value: quick_heat
          - dps_val: eheat
            value: smart_heat
          - dps_val: scool
            value: quiet_cool
          - dps_val: bcool
            value: quick_cool
          - dps_val: ecool
            value: smart_cool
          - dps_val: auto
            value: auto
      - id: 101
        type: integer
        name: unknown_101
      - id: 102
        type: integer
        name: unknown_102
      - id: 103
        type: integer
        name: unknown_103
      - id: 104
        type: boolean
        name: unknown_104
  - entity: binary_sensor
    class: problem
    category: diagnostic
    dps:
      - id: 20
        type: bitfield
        name: sensor
        mapping:
          - dps_val: 0
            value: false
          - value: true
      - id: 20
        type: bitfield
        name: fault_code
      - id: 20
        type: bitfield
        name: description
        mapping:
          - dps_val: 0
            value: OK
          - dps_val: 1
            value: sys_high_fault
          - dps_val: 2
            value: sys_low_fault
          - dps_val: 4
            value: flow_fault
          - dps_val: 8
            value: power_fault
          - dps_val: 16
            value: cooling_fault
          - dps_val: 32
            value: heating_fault
          - dps_val: 64
            value: temp_dif_fault
          - dps_val: 128
            value: in_temp_fault
          - dps_val: 256
            value: eff_temp_fault
          - dps_val: 512
            value: coil_temp_fault
          - dps_val: 1024
            value: ret_temp_fault
          - dps_val: 2048
            value: news_fault
          - dps_val: 4096
            value: amb_temp_fault
          - dps_val: 8192
            value: lack_water
          - dps_val: 16384
            value: serious_fault
          - dps_val: 32768
            value: sensor_fault
          - dps_val: 65536
            value: motor_fault
