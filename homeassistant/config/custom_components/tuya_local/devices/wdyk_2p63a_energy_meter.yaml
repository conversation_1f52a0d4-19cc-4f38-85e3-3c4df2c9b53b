name: Energy meter
products:
  - id: r9kg2g1uhhyicycb
    manufacturer: WDYK
    model: 2P63A
entities:
  - entity: sensor
    class: energy
    dps:
      - id: 1
        type: integer
        name: sensor
        unit: kWh
        class: total_increasing
        optional: true
        mapping:
          - scale: 100
      - id: 6
        name: phase_a
        type: base64
        optional: true
        force: true
      - id: 7
        name: phase_b
        type: base64
        optional: true
        force: true
      - id: 8
        name: phase_c
        type: base64
        optional: true
        force: true
      - id: 19
        type: string
        name: breaker_number
      - id: 116
        type: string
        name: card_id
        optional: true
  - entity: binary_sensor
    class: problem
    category: diagnostic
    dps:
      - id: 9
        type: bitfield
        name: sensor
        mapping:
          - dps_val: 0
            value: false
          - value: true
      - id: 9
        type: bitfield
        name: fault_code
      - id: 9
        type: bitfield
        name: description
        mapping:
          - dps_val: 0
            value: ok
          - dps_val: 1
            value: Short circuit
          - dps_val: 2
            value: Surge
          - dps_val: 4
            value: Overload
          - dps_val: 8
            value: Earth leak
          - dps_val: 16
            value: High temperature
          - dps_val: 32
            value: Fire
          - dps_val: 64
            value: High power
          - dps_val: 128
            value: Self test
          - dps_val: 256
            value: Over current
          - dps_val: 512
            value: Unbalanced
          - dps_val: 1024
            value: Over voltage
          - dps_val: 2048
            value: Under voltage
          - dps_val: 4096
            value: Phase fault
          - dps_val: 8192
            value: Outage
          - dps_val: 16384
            value: Magnetism
          - dps_val: 32768
            value: Credit low
          - dps_val: 65536
            value: Credit expired
  - entity: switch
    name: Prepay
    category: config
    icon: "mdi:cash"
    dps:
      - id: 11
        type: boolean
        name: switch
  - entity: button
    name: Reset balance energy
    category: config
    class: restart
    dps:
      - id: 12
        type: boolean
        optional: true
        name: button
  - entity: sensor
    name: Energy balance
    class: energy_storage
    category: diagnostic
    dps:
      - id: 13
        type: integer
        name: sensor
        unit: kWh
        class: measurement
        mapping:
          - scale: 100
  - entity: number
    name: Charge energy
    category: config
    class: energy_storage
    icon: "mdi:cash"
    dps:
      - id: 14
        type: integer
        name: value
        unit: kWh
        optional: true
        range:
          min: 0
          max: 999999
        mapping:
          - scale: 100
  - entity: switch
    name: Circuit breaker
    category: config
    dps:
      - id: 16
        type: boolean
        name: switch
        optional: true
  - entity: sensor
    translation_key: voltage_x
    translation_placeholders:
      x: A
    class: voltage
    category: diagnostic
    dps:
      - id: 6
        type: base64
        name: sensor
        optional: true
        unit: V
        mask: "FFFF0000000000000000"
        mapping:
          - scale: 10
  - entity: sensor
    translation_key: current_x
    translation_placeholders:
      x: A
    class: current
    category: diagnostic
    dps:
      - id: 6
        type: base64
        name: sensor
        optional: true
        unit: A
        mask: "0000FFFFFF0000000000"
        mapping:
          - scale: 1000
  - entity: sensor
    translation_key: power_x
    translation_placeholders:
      x: A
    class: power
    category: diagnostic
    dps:
      - id: 6
        type: base64
        name: sensor
        optional: true
        unit: kW
        mask: "0000000000FFFFFF0000"
        mapping:
          - scale: 1000
  - entity: sensor
    translation_key: voltage_x
    translation_placeholders:
      x: B
    class: voltage
    category: diagnostic
    dps:
      - id: 7
        type: base64
        name: sensor
        optional: true
        unit: V
        mask: "FFFF000000000000"
        mapping:
          - scale: 10
  - entity: sensor
    translation_key: current_x
    translation_placeholders:
      x: B
    class: current
    category: diagnostic
    dps:
      - id: 7
        type: base64
        name: sensor
        optional: true
        unit: A
        mask: "0000FFFFFF000000"
        mapping:
          - scale: 1000
  - entity: sensor
    translation_key: power_x
    translation_placeholders:
      x: B
    class: power
    category: diagnostic
    dps:
      - id: 7
        type: base64
        name: sensor
        optional: true
        unit: kW
        mask: "0000000000FFFFFF"
        mapping:
          - scale: 1000
  - entity: sensor
    translation_key: voltage_x
    translation_placeholders:
      x: C
    class: voltage
    category: diagnostic
    dps:
      - id: 8
        type: base64
        name: sensor
        optional: true
        unit: V
        mask: "FFFF000000000000"
        mapping:
          - scale: 10
  - entity: sensor
    translation_key: current_x
    translation_placeholders:
      x: C
    class: current
    category: diagnostic
    dps:
      - id: 8
        type: base64
        name: sensor
        optional: true
        unit: A
        mask: "0000FFFFFF000000"
        mapping:
          - scale: 1000
  - entity: sensor
    translation_key: power_x
    translation_placeholders:
      x: C
    class: power
    category: diagnostic
    dps:
      - id: 8
        type: base64
        name: sensor
        optional: true
        unit: kW
        mask: "0000000000FFFFFF"
        mapping:
          - scale: 1000
  - entity: number
    name: Overvoltage threshold
    category: config
    class: voltage
    icon: "mdi:flash-alert"
    dps:
      - id: 101
        type: integer
        name: value
        unit: V
        range:
          min: 230
          max: 300
  - entity: number
    name: Undervoltage threshold
    category: config
    class: voltage
    icon: "mdi:flash-alert"
    dps:
      - id: 102
        type: integer
        name: value
        unit: V
        range:
          min: 90
          max: 210
  - entity: number
    name: Overcurrent threshold
    category: config
    class: current
    icon: "mdi:fuse-alert"
    dps:
      - id: 103
        type: integer
        name: value
        unit: A
        range:
          min: 1
          max: 63
  - entity: number
    name: Leakage current
    category: config
    class: current
    dps:
      - id: 104
        type: integer
        name: value
        unit: mA
        range:
          min: 10
          max: 100
  - entity: switch
    name: Mute alarm
    icon: "mdi:volume-mute"
    category: config
    dps:
      - id: 105
        type: boolean
        name: switch
  - entity: binary_sensor
    name: Earth leak
    class: safety
    category: diagnostic
    dps:
      - id: 106
        type: boolean
        name: sensor
  - entity: button
    name: Reset total energy
    class: restart
    category: config
    dps:
      - id: 115
        type: boolean
        name: button
        optional: true
