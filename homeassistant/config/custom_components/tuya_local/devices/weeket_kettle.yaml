name: Kettle
products:
  - id: 01rtpfyd3amxdsps
    manufacturer: Weekett
    model: KE4071TF
entities:
  - entity: water_heater
    translation_key: kettle
    dps:
      - id: 2
        type: integer
        name: current_temperature
        mapping:
          - constraint: temperature_unit
            conditions:
              - dps_val: f
                value_redirect: temp_current_f
      - id: 3
        type: integer
        name: temp_current_f
        hidden: true
      - id: 8
        type: integer
        name: temperature
        range:
          min: 40
          max: 100
        mapping:
          - constraint: temperature_unit
            conditions:
              - dps_val: f
                range:
                  min: 104
                  max: 212
                value_redirect: temp_set_f
      - id: 9
        type: integer
        name: temp_set_f
        range:
          min: 104
          max: 212
        hidden: true
      - id: 12
        type: string
        name: temperature_unit
        mapping:
          - dps_val: f
            value: F
          - dps_val: c
            value: C
      - id: 1
        type: boolean
        name: operation_mode
        mapping:
          - dps_val: false
            value: "off"
          - dps_val: true
            value: electric
  - entity: select
    translation_key: kettle_mode
    dps:
      - id: 16
        type: string
        name: option
        mapping:
          - dps_val: setting_quick
            value: quick_heat
          - dps_val: boiling_quick
            value: quick_boil
          - dps_val: temp_setting
            value: heat
          - dps_val: temp_boiling
            value: boil
  - entity: switch
    # 2025-06-07
    deprecated: water_heater_kettle.operation_mode
    dps:
      - id: 1
        type: boolean
        name: switch
        mapping:
          - dps_val: false
            icon: "mdi:kettle-off"
          - dps_val: true
            icon: "mdi:kettle-steam"
  - entity: select
    translation_key: temperature_unit
    category: config
    dps:
      - id: 12
        type: string
        name: option
        mapping:
          - dps_val: c
            value: celsius
          - dps_val: f
            value: fahrenheit
  - entity: switch
    name: Keep warm
    icon: "mdi:heat-wave"
    category: config
    dps:
      - id: 13
        type: boolean
        name: switch
  - entity: sensor
    translation_key: status
    class: enum
    category: diagnostic
    dps:
      - id: 15
        type: string
        name: sensor
        mapping:
          - dps_val: standby
            value: standby
          - dps_val: heating
            value: heating
          - dps_val: cooling
            value: cooling
          - dps_val: warm
            value: keeping_warm
          - dps_val: heating_temp
            value: no_water
          - dps_val: boiling_temp
            value: boiling
          - dps_val: reserve_1
            value: reserved_1
          - dps_val: reserve_2
            value: reserved_2
          - dps_val: reserve_3
            value: reserved_3
  - entity: binary_sensor
    class: problem
    category: diagnostic
    dps:
      - id: 19
        type: bitfield
        name: sensor
        mapping:
          - dps_val: 0
            value: false
          - value: true
      - id: 19
        type: bitfield
        name: fault_code
  - entity: switch
    name: Bottle warming
    icon: "mdi:baby-bottle"
    category: config
    dps:
      - id: 101
        type: boolean
        name: switch
