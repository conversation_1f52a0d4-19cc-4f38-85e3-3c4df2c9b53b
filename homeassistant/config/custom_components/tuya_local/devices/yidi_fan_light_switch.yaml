name: Wall switch
products:
  - id: 66urzkokqdufersv
    manufacturer: NHZS
    name: Fan and light switch
entities:
  - entity: fan
    dps:
      - id: 1
        type: boolean
        name: switch
      - id: 4
        type: integer
        name: speed
        range:
          min: 1
          max: 100
  - entity: light
    dps:
      - id: 5
        type: boolean
        name: switch
  - entity: time
    category: config
    translation_key: timer
    dps:
      - id: 2
        type: integer
        name: second
        range:
          min: 0
          max: 86400
  - entity: number
    name: Fan timer
    # 2025-07-20
    deprecated: time.timer
    category: config
    class: duration
    translation_key: timer
    dps:
      - id: 2
        type: integer
        name: value
        unit: min
        range:
          min: 0
          max: 86400
        mapping:
          - scale: 60
            step: 60
  - entity: select
    translation_key: initial_state
    category: config
    dps:
      - id: 11
        type: string
        name: option
        mapping:
          - dps_val: "off"
            value: "off"
          - dps_val: "on"
            value: "on"
          - dps_val: memory
            value: memory
        optional: true
  - entity: light
    translation_key: backlight
    category: config
    dps:
      - id: 13
        type: boolean
        name: switch
