name: Dehumidifier
products:
  - id: kiiuovcdvvgxnj0n
    manufacturer: Woods
    model: WDD90
entities:
  - entity: humidifier
    class: dehumidifier
    dps:
      - id: 1
        type: boolean
        name: switch
      - id: 3
        type: string
        name: humidity
        range:
          min: 35
          max: 80
        mapping:
          - dps_val: "35"
            value: 35
            step: 5
          - dps_val: "40"
            value: 40
            step: 5
          - dps_val: "45"
            value: 45
            step: 5
          - dps_val: "50"
            value: 50
            step: 5
          - dps_val: "55"
            value: 55
            step: 5
          - dps_val: "60"
            value: 60
            step: 5
          - dps_val: "65"
            value: 65
            step: 5
          - dps_val: "70"
            value: 70
            step: 5
          - dps_val: "75"
            value: 75
            step: 5
          - dps_val: "80"
            value: 80
            step: 5
      - id: 6
        type: integer
        name: current_humidity
  - entity: fan
    translation_key: fan_with_presets
    dps:
      - id: 1
        type: boolean
        name: switch
      - id: 4
        type: string
        name: preset_mode
        mapping:
          - dps_val: "1"
            value: sleep
          - dps_val: "2"
            value: normal
          - dps_val: "3"
            value: strong
          - dps_val: "A"
            value: smart
      - id: 8
        type: boolean
        name: oscillate
  - entity: switch
    translation_key: ionizer
    category: config
    dps:
      - id: 10
        type: boolean
        name: switch
  - entity: lock
    translation_key: child_lock
    category: config
    dps:
      - id: 16
        type: boolean
        name: lock
  - entity: select
    translation_key: timer
    category: config
    dps:
      - id: 17
        type: string
        name: option
        mapping:
          - dps_val: cancel
            value: cancel
          - dps_val: "1h"
            value: "1h"
          - dps_val: "2h"
            value: "2h"
          - dps_val: "3h"
            value: "3h"
          - dps_val: "4h"
            value: "4h"
          - dps_val: "5h"
            value: "5h"
          - dps_val: "6h"
            value: "6h"
          - dps_val: "7h"
            value: "7h"
          - dps_val: "8h"
            value: "8h"
  - entity: binary_sensor
    class: problem
    category: diagnostic
    dps:
      - id: 19
        type: bitfield
        name: sensor
        mapping:
          - dps_val: 0
            value: false
          - dps_val: 16
            value: false
          - value: true
      - id: 19
        type: bitfield
        name: fault_code
  - entity: binary_sensor
    translation_key: tank_full
    dps:
      - id: 19
        type: bitfield
        name: sensor
        mapping:
          - dps_val: 16
            value: true
          - value: false
