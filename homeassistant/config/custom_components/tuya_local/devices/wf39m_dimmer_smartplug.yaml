name: <PERSON><PERSON> smartplug
products:
  - id: zbisbtukzuiifmp6
    manufacturer: Newone
    model: WF39M
entities:
  - entity: light
    dps:
      - id: 1
        type: boolean
        name: switch
      - id: 2
        type: integer
        name: brightness
        range:
          min: 10
          max: 1000
  - entity: select
    name: Light type
    icon: "mdi:lightbulb"
    category: config
    dps:
      - id: 4
        type: string
        name: option
        mapping:
          - dps_val: led
            value: LED
          - dps_val: incandescent
            value: Incandescent
          - dps_val: halogen
            value: Halogen
  - entity: number
    name: Minimum brightneess
    category: config
    icon: "mdi:lightbulb-on-20"
    dps:
      - id: 3
        type: integer
        name: value
        unit: "%"
        range:
          min: 10
          max: 1000
        mapping:
          - scale: 10
  - entity: number
    name: Maximum brightness
    category: config
    icon: "mdi:lightbulb-on-90"
    dps:
      - id: 5
        type: integer
        name: value
        unit: "%"
        range:
          min: 10
          max: 1000
        mapping:
          - scale: 10
  - entity: time
    translation_key: timer
    category: config
    dps:
      - id: 6
        type: integer
        optional: true
        name: second
        range:
          min: 0
          max: 86400
  - entity: number
    class: duration
    translation_key: timer
    # 2025-07-20
    deprecated: time.timer
    category: config
    dps:
      - id: 6
        type: integer
        optional: true
        name: value
        unit: min
        range:
          min: 0
          max: 86400
        mapping:
          - scale: 60
            step: 60
  - entity: select
    translation_key: initial_state
    category: config
    dps:
      - id: 14
        type: string
        name: option
        mapping:
          - dps_val: "off"
            value: "off"
          - dps_val: "on"
            value: "on"
          - dps_val: memory
            value: memory
  - entity: select
    translation_key: light_mode
    category: config
    dps:
      - id: 21
        type: string
        name: option
        mapping:
          - dps_val: none
            value: "off"
          - dps_val: relay
            value: state
          - dps_val: pos
            value: locator
