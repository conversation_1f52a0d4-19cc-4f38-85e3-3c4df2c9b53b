name: Energy meter
products:
  - id: cdw24010001
    manufacturer: Zemismart
    model_id: SDM02T-TW
    model: 2-Phase Energy Meter Max
entities:
  - entity: sensor
    translation_key: energy_consumed
    class: energy
    dps:
      - id: 1
        type: integer
        name: sensor
        unit: kWh
        class: total_increasing
        mapping:
          - scale: 100
  - entity: sensor
    translation_key: energy_produced
    class: energy
    dps:
      - id: 9
        type: integer
        name: sensor
        unit: kWh
        class: total_increasing
        mapping:
          - scale: 100
  - entity: sensor
    class: power
    dps:
      - id: 29
        name: sensor
        unit: W
        class: measurement
        type: integer
  - entity: sensor
    translation_key: power_x
    translation_placeholders:
      x: A
    class: power
    dps:
      - id: 105
        name: sensor
        unit: W
        class: measurement
        type: integer
  - entity: sensor
    translation_key: power_x
    translation_placeholders:
      x: B
    class: power
    dps:
      - id: 114
        name: sensor
        unit: W
        class: measurement
        type: integer
  - entity: sensor
    translation_key: voltage_x
    translation_placeholders:
      x: A
    class: voltage
    category: diagnostic
    dps:
      - id: 103
        name: sensor
        unit: V
        class: measurement
        type: integer
        mapping:
          - scale: 10
  - entity: sensor
    translation_key: voltage_x
    translation_placeholders:
      x: B
    class: voltage
    category: diagnostic
    dps:
      - id: 112
        name: sensor
        unit: V
        class: measurement
        type: integer
        mapping:
          - scale: 10
  - entity: sensor
    translation_key: current_x
    translation_placeholders:
      x: A
    class: current
    category: diagnostic
    dps:
      - id: 104
        name: sensor
        unit: A
        class: measurement
        type: integer
        mapping:
          - scale: 1000
  - entity: sensor
    translation_key: current_x
    translation_placeholders:
      x: B
    class: current
    category: diagnostic
    dps:
      - id: 113
        name: sensor
        unit: A
        class: measurement
        type: integer
        mapping:
          - scale: 1000
  - entity: sensor
    class: frequency
    category: diagnostic
    dps:
      - id: 32
        name: sensor
        unit: Hz
        class: measurement
        type: integer
        mapping:
          - scale: 100
  - entity: sensor
    class: power_factor
    category: diagnostic
    dps:
      - id: 117
        name: sensor
        type: integer
        unit: "%"
        class: measurement
  - entity: number
    name: Reporting interval
    category: config
    class: duration
    icon: "mdi:timer-cog"
    dps:
      - id: 102
        type: integer
        name: value
        unit: s
        range:
          min: 5
          max: 3600
