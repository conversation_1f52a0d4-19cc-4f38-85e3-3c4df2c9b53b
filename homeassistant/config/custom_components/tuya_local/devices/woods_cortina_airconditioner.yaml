name: Air Conditioner
products:
  - id: bfc0e42617f4fdfef7t6ra
    manufacturer: Woods
    model: Cortina Silent 12k
entities:
  - entity: climate
    dps:
      - id: 1
        name: hvac_mode
        type: boolean
        mapping:
          - value: "off"
            dps_val: false
          - dps_val: true
            constraint: mode
            conditions:
              - value: cool
                dps_val: "0"
              - value: dry
                dps_val: "2"
              - value: fan_only
                dps_val: "3"
              - value: heat_cool
                dps_val: "4"  # This is Woods ECO mode
      - id: 2
        type: integer
        name: temperature
        unit: C
        range:
          min: 17
          max: 30
      - id: 3
        type: integer
        name: current_temperature
      - id: 4
        type: string
        name: mode
        hidden: true
      - id: 5
        type: string
        name: fan_mode
        mapping:
          - dps_val: "low"
            value: low
          - dps_val: "mid"
            value: medium
          - dps_val: "high"
            value: high
      - id: 104
        name: swing_mode
        type: boolean
        mapping:
          - dps_val: true
            value: "on"
          - dps_val: false
            value: "off"
  - entity: binary_sensor
    class: problem
    category: diagnostic
    dps:
      - id: 22
        type: bitfield
        name: sensor
        mapping:
          - dps_val: 0
            value: false
          - dps_val: 1
            value: false
          - value: true
      - id: 22
        type: bitfield
        name: fault_code
  - entity: binary_sensor
    translation_key: tank_full
    category: diagnostic
    dps:
      - id: 22
        type: bitfield
        name: sensor
        mapping:
          - dps_val: 1
            value: true
          - value: false
  - entity: switch
    translation_key: sleep
    dps:
      - id: 105
        type: boolean
        name: switch
