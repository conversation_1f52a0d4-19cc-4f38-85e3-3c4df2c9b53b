name: Air purifier
products:
  - id: trpege6ebn8at1el
    manufacturer: Vork
    model: VK-6067AW
entities:
  - entity: fan
    icon: "mdi:air-purifier"
    translation_only_key: fan_with_presets
    dps:
      - id: 1
        name: switch
        type: boolean
      - id: 4
        type: string
        name: preset_mode
        mapping:
          - dps_val: low
            value: nature
          - dps_val: mid
            value: fresh
          - dps_val: high
            value: strong
          - dps_val: auto
            value: smart
          - dps_val: sleep
            value: sleep
  - entity: sensor
    name: Filter
    category: diagnostic
    icon: "mdi:air-filter"
    dps:
      - id: 5
        name: sensor
        type: integer
        unit: "%"
  - entity: light
    category: config
    dps:
      - id: 8
        type: boolean
        name: switch
        mapping:
          - dps_val: true
            icon: "mdi:led-on"
          - dps_val: false
            icon: "mdi:led-off"
  - entity: button
    translation_key: filter_reset
    category: config
    dps:
      - id: 11
        type: boolean
        name: button
  - entity: select
    translation_key: timer
    category: config
    dps:
      - id: 18
        type: string
        name: option
        mapping:
          - dps_val: cancel
            value: cancel
          - dps_val: "1h"
            value: "1h"
          - dps_val: "2h"
            value: "2h"
  - entity: sensor
    translation_key: time_remaining
    class: duration
    category: diagnostic
    dps:
      - id: 19
        name: sensor
        type: integer
        unit: min
  - entity: sensor
    translation_key: air_quality
    category: diagnostic
    class: enum
    dps:
      - id: 21
        name: sensor
        type: string
        mapping:
          - dps_val: great
            value: excellent
          - dps_val: good
            value: good
          - dps_val: medium
            value: poor
          - dps_val: severe
            value: severe
  - entity: binary_sensor
    category: diagnostic
    class: problem
    dps:
      - id: 22
        name: sensor
        type: bitfield
        mapping:
          - dps_val: 0
            value: false
          - value: true
      - id: 22
        name: fault_code
        type: bitfield
