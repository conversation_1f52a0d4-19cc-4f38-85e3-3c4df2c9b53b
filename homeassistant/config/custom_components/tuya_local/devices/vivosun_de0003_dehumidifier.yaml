name: Dehumidifier
products:
  - id: qrjffdu2bjrd7v42
    manufacturer: Vivosun
    model: DE0003 10L
entities:
  - entity: humidifier
    class: dehumidifier
    dps:
      - id: 1
        name: switch
        type: boolean
      - id: 2
        type: integer
        name: humidity
        range:
          min: 30
          max: 80
        mapping:
          - step: 5
      - id: 5
        name: mode
        type: string
        mapping:
          - dps_val: Continuities
            value: boost
          - dps_val: Auto
            value: auto
          - dps_val: Sleep
            value: sleep
      - id: 6
        type: integer
        name: current_humidity
      # This doesn't actually have an ionizer, but having the dp
      # will aid in detection
      - id: 10
        type: boolean
        name: ionizer
  - entity: fan
    dps:
      - id: 1
        type: boolean
        name: switch
      - id: 4
        type: string
        name: speed
        mapping:
          - dps_val: "low"
            value: 50
          - dps_val: "high"
            value: 100
  - entity: sensor
    class: temperature
    dps:
      - id: 7
        type: integer
        name: sensor
        unit: F
        class: measurement
  - entity: lock
    translation_key: child_lock
    category: config
    dps:
      - id: 16
        type: boolean
        name: lock
  - entity: select
    translation_key: timer
    category: config
    dps:
      - id: 17
        type: string
        name: option
        mapping:
          - dps_val: cancel
            value: cancel
          - dps_val: 1h
            value: 1h
          - dps_val: 2h
            value: 2h
          - dps_val: 3h
            value: 3h
          - dps_val: 4h
            value: 4h
          - dps_val: 5h
            value: 5h
          - dps_val: 6h
            value: 6h
          - dps_val: 7h
            value: 7h
          - dps_val: 8h
            value: 8h
          - dps_val: 9h
            value: 9h
          - dps_val: 10h
            value: 10h
          - dps_val: 11h
            value: 11h
          - dps_val: 12h
            value: 12h
          - dps_val: 13h
            value: 13h
          - dps_val: 14h
            value: 14h
          - dps_val: 15h
            value: 15h
          - dps_val: 16h
            value: 16h
          - dps_val: 17h
            value: 17h
          - dps_val: 18h
            value: 18h
          - dps_val: 19h
            value: 19h
          - dps_val: 20h
            value: 20h
          - dps_val: 21h
            value: 21h
          - dps_val: 22h
            value: 22h
          - dps_val: 23h
            value: 23h
          - dps_val: 24h
            value: 24h
  - entity: binary_sensor
    translation_key: tank_full
    category: diagnostic
    dps:
      - id: 19
        type: bitfield
        name: sensor
        mapping:
          - dps_val: 1
            value: true
          - value: false
  - entity: binary_sensor
    class: problem
    category: diagnostic
    dps:
      - id: 19
        type: bitfield
        name: sensor
        mapping:
          - dps_val: 0
            value: false
          - dps_val: 1
            value: false
          - dps_val: null
            value: false
          - value: true
      - id: 19
        type: bitfield
        name: fault_code
      - id: 19
        type: bitfield
        name: description
        mapping:
          - dps_val: 0
            value: OK
          - dps_val: 1
            value: Tank full or missing
          - dps_val: 2
            value: "E2: Compressor fault"
