name: mmWave presence sensor
products:
  - id: k2h8vkj98fhvnpiv
    model: ZY-M100-WIFI
  - id: yroelksjtb4ehda8
    model: WZ-M100-W
entities:
  - entity: binary_sensor
    class: occupancy
    dps:
      - id: 1
        type: string
        name: sensor
        mapping:
          - dps_val: presence
            value: true
          - dps_val: none
            value: false
  - entity: number
    name: Sensitivity
    category: config
    icon: "mdi:motion-sensor"
    dps:
      - id: 2
        type: integer
        name: value
        range:
          min: 0
          max: 9
  - entity: number
    name: Minimum distance
    category: config
    icon: "mdi:arrow-collapse-left"
    dps:
      - id: 3
        type: integer
        name: value
        unit: m
        range:
          min: 0
          max: 1000
        mapping:
          - scale: 100
            step: 10
  - entity: number
    name: Maximum distance
    category: config
    icon: "mdi:arrow-collapse-right"
    dps:
      - id: 4
        type: integer
        name: value
        unit: m
        range:
          min: 0
          max: 1000
        mapping:
          - scale: 100
            step: 10
  - entity: binary_sensor
    class: problem
    category: diagnostic
    dps:
      - id: 6
        type: string
        optional: true
        name: sensor
        mapping:
          - dps_val: null
            value: false
          - dps_val: check_success
            value: false
          - dps_val: checking
            value: false
          - value: true
      - id: 6
        type: string
        optional: true
        name: description
  - entity: sensor
    name: Target distance
    class: distance
    icon: "mdi:tape-measure"
    dps:
      - id: 9
        type: integer
        name: sensor
        unit: m
        class: measurement
        mapping:
          - scale: 100
  - entity: number
    name: Detection delay
    category: config
    # mode: box
    class: duration
    icon: "mdi:timer-sand"
    dps:
      - id: 101
        type: integer
        name: value
        unit: s
        range:
          min: 0
          max: 100
        mapping:
          - scale: 10
  - entity: number
    name: Fading time
    category: config
    class: duration
    icon: "mdi:timer-sand-complete"
    dps:
      - id: 102
        type: integer
        name: value
        unit: s
        range:
          min: 0
          max: 15000
        mapping:
          - scale: 10
            step: 50
  - entity: sensor
    class: illuminance
    dps:
      - id: 104
        type: integer
        name: sensor
        unit: lx
        class: measurement
