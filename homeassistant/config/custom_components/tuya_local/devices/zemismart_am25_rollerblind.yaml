name: Roller blind
products:
  - id: 7qcsglvoqkrdduk6
    manufacturer: Zemismart
    model: AM25WIFI
entities:
  - entity: cover
    class: blind
    dps:
      - id: 1
        name: control
        type: string
        mapping:
          - dps_val: open
            value: open
          - dps_val: stop
            value: stop
          - dps_val: close
            value: close
          - dps_val: continue
            value: continue
      - id: 2
        name: position
        type: integer
        range:
          min: 0
          max: 100
        mapping:
          - invert: true
      - id: 3
        name: missing_current_position
        type: integer
        optional: true
        range:
          min: 0
          max: 100
        mapping:
          - invert: true
      - id: 7
        name: work_state
        type: string
        optional: true
      - id: 109
        type: integer
        name: tilt_position
        range:
          min: 1
          max: 10
  - entity: binary_sensor
    class: problem
    category: diagnostic
    dps:
      - id: 12
        type: bitfield
        name: sensor
        optional: true
        mapping:
          - dps_val: null
            value: false
          - dps_val: 0
            value: false
          - value: true
      - id: 12
        type: bitfield
        name: fault_code
        optional: true
  - entity: select
    name: Direction
    icon: "mdi:swap-horizontal"
    category: config
    dps:
      - id: 103
        type: boolean
        name: option
        optional: true
        mapping:
          - dps_val: false
            value: forward
          - dps_val: true
            value: reverse
          - dps_val: null
            value: forward
            hidden: true
  - entity: switch
    name: Limit up
    category: config
    dps:
      - id: 104
        type: boolean
        name: switch
  - entity: switch
    name: Limit down
    category: config
    dps:
      - id: 105
        type: boolean
        name: switch
  - entity: button
    name: Reset limits
    category: config
    dps:
      - id: 107
        type: boolean
        name: button
        optional: true
