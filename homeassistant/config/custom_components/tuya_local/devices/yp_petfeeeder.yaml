name: Pet feeder
products:
  - id: wfnmgiqqch80sg1w
    manufacturer: YP
    model: PAF-40PCS-WBR3
entities:
  - entity: number
    icon: "mdi:paw"
    name: Manual feed
    dps:
      - id: 3
        name: value
        type: integer
        range:
          min: 1
          max: 40
  - entity: text
    name: Meal plan
    category: config
    hidden: true
    dps:
      - id: 1
        type: base64
        name: value
        optional: true
  - entity: sensor
    translation_key: status
    class: enum
    icon: "mdi:paw"
    category: diagnostic
    dps:
      - id: 4
        type: string
        name: sensor
        mapping:
          - dps_val: standby
            value: standby
          - dps_val: feeding
            value: feeding
          - dps_val: done
            value: feeding_complete
  - entity: sensor
    icon: "mdi:paw"
    name: Feed report
    category: diagnostic
    dps:
      - id: 15
        name: sensor
        type: integer
  - entity: button
    translation_key: factory_reset
    category: config
    dps:
      - id: 9
        type: boolean
        optional: true
        name: button
  - entity: binary_sensor
    class: problem
    category: diagnostic
    dps:
      - id: 14
        type: bitfield
        name: sensor
        mapping:
          - dps_val: 0
            value: false
          - value: true
      - id: 14
        type: bitfield
        name: fault_code
      - id: 14
        type: bitfield
        name: description
        mapping:
          - dps_val: 1
            value: pet_food_jammed
          - dps_val: 2
            value: pet_food_low
          - dps_val: 4
            value: pet_food_empty
          - dps_val: 8
            value: desiccant_exhausted
          - dps_val: 16
            value: battery_low
