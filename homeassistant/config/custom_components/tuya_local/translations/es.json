{"title": "Tuya Local", "config": {"step": {"user": {"title": "Configura tu dispositivo Tuya Local", "description": "Los dispositivos se pueden agregar manualmente o con la ayuda de la nube con la ayuda de la aplicación SmartLife.", "data": {"setup_mode": "Elección de configuración:"}}, "cloud": {"title": "Iniciar sesión en Tuya", "description": "Ingresa tu código de usuario de SmartLife o Tuya.\n\nPuedes encontrar este código en la aplicación SmartLife o Tuya en la pantalla **Configuración** > **Cuenta y seguridad** e ingresa el código que se muestra en el ", "data": {"user_code": "Codigo de usuario:"}}, "scan": {"title": "Completa el inicio de sesión", "description": "Utilice la aplicación SmartLife o la aplicación Tuya para escanear el siguiente código QR y completar el inicio de sesión.\n\nContinúe con el siguiente paso una vez que haya completado este paso en la aplicación."}, "choose_device": {"title": "Elija el dispositivo para agregar", "description": "Elija el dispositivo que desea agregar de la primera lista desplegable. ", "data": {"device_id": "Elija dispositivo:", "hub_id": "<PERSON><PERSON> puerta de enlace:"}}, "search": {"title": "Localice la dirección IP del dispositivo", "description": "Tuya Cloud no proporciona direcciones IP locales por lo que ahora debemos buscar en su red local para encontrar el dispositivo. "}, "local": {"title": "Configura tu dispositivo Tuya Local", "description": "[Siga estas instrucciones para encontrar la identificación de su dispositivo y la clave local.](https://github.com/make-all/tuya-local#finding-your-device-id-and-local-key)", "data": {"host": "Dirección IP o nombre de host", "device_id": "Identificación del dispositivo (device_id, o device_id de la puerta de enlace para dispositivos conectados a través de una puerta de enlace)", "local_key": "Clave local (Local key)", "protocol_version": "Versión del protocolo (pruebe automático si no lo sabe)", "poll_only": "Solo encuesta (pruebe esto si su dispositivo no funciona completamente)", "device_cid": "Sub device node_id o uuid (para dispositivos conectados a través de una puerta de enlace)"}}, "select_type": {"title": "Elige el tipo de dispositivo", "description": "Elija el tipo que coincida con su dispositivo", "data": {"type": "Tipo de dispositivo"}}, "choose_entities": {"title": "Detalles del dispositivo", "description": "Elija un nombre para este dispositivo", "data": {"name": "Nombre"}}}, "abort": {"already_configured": "Ya se ha agregado un dispositivo con esa ID.", "not_supported": "Lo sentimos, no hay soporte para este dispositivo.", "no_devices": "No se puede encontrar ningún dispositivo no registrado para la cuenta."}, "error": {"connection": "No se puede conectar a su dispositivo con esos detalles. Podría ser un problema intermitente, o pueden ser incorrectos.", "does_not_need_hub": "El dispositivo no necesita una puerta de enlace y se seleccionó una. ", "needs_hub": "El dispositivo necesita una puerta de enlace y no se seleccionó ninguna."}}, "selector": {"setup_mode": {"options": {"cloud": "Configuración del dispositivo asistida por la nube SmartLife.", "manual": "Proporcione manualmente información de conexión del dispositivo.", "cloud_fresh_login": "Configuración del dispositivo asistida por la nube SmartLife con inicio de sesión fresco."}}}, "options": {"step": {"user": {"title": "Configura tu dispositivo Tuya Local", "description": "[Siga estas instrucciones para encontrar su clave local.](https://github.com/make-all/tuya-local#finding-your-device-id-and-local-key)", "data": {"host": "Dirección IP o nombre de host", "local_key": "Clave local (Local key)", "protocol_version": "Versión del protocolo (pruebe automático si no lo sabe)", "poll_only": "Solo encuesta (pruebe esto si su dispositivo no funciona completamente)"}}}, "error": {"connection": "No se puede conectar a su dispositivo con esos detalles. Podría ser un problema intermitente, o pueden ser incorrectos."}, "abort": {"not_supported": "Lo sentimos, no hay soporte para este dispositivo."}}, "entity": {"binary_sensor": {"direction": {"name": "Dirección", "state": {"off": "<PERSON><PERSON><PERSON>", "on": "Saliente"}}, "defrost": {"name": "<PERSON><PERSON><PERSON><PERSON>", "state": {"off": "Normal", "on": "Deshelamiento"}}, "tank_empty": {"name": "<PERSON><PERSON><PERSON><PERSON>", "state": {"off": "OK", "on": "<PERSON><PERSON>"}}, "tank_full": {"name": "<PERSON><PERSON><PERSON><PERSON>", "state": {"off": "OK", "on": "<PERSON><PERSON><PERSON>"}}, "wake": {"name": "<PERSON><PERSON><PERSON>", "state": {"off": "Dormido", "on": "<PERSON><PERSON><PERSON><PERSON>"}}, "casdon_oven_fault": {"state_attributes": {"description": {"state": {"e1": "E1: Protección contra sobrecalentamiento de la cámara (supera los 290°C)", "e2": "E2: Protección contra sobrecalentamiento de la bandeja de evaporación (supera los 200°C)", "e3": "E3: Protección contra baja temperatura de la cámara (inferior a 35°C)", "e4": "E4: Protección contra baja temperatura de la bandeja de evaporación (inferior a 35°C)", "e5": "E5: <PERSON><PERSON><PERSON> de conexión del sensor (circuito abierto)", "e6": "E6: Mal funcionamiento del sensor (Circuito corto)", "e7": "E7: Error de comunicación de la placa de visualización", "e8": "E8: Tanque de agua abierto (CN7) o Bandeja de evaporación seca (CN3)"}}}}}, "button": {"factory_reset": {"name": "Restablecimiento de fábrica"}, "filter_reset": {"name": "Restablecimiento del filtro"}}, "climate": {"aircon_extra": {"name": "Aire acondicionado", "state_attributes": {"fan_mode": {"state": {"health": "Saludable", "medhigh": "Altura media", "medlow": "Medio-bajo", "natural": "Natural", "quiet": "Tran<PERSON>lo", "sleep": "<PERSON><PERSON><PERSON>", "strong": "<PERSON><PERSON>e"}}, "swing_mode": {"state": {"topmost": "Más alto", "top": "Alto", "middle": "Medio", "down": "Abajo", "downmost": "<PERSON><PERSON> a<PERSON>"}}}}, "thermostat": {"name": "Termostato", "state_attributes": {"fan_mode": {"state": {"cycle": "<PERSON><PERSON><PERSON>"}}, "preset_mode": {"state": {"manual": "Manual", "program": "Programa", "temp_override": "Anulación temporal", "perm_override": "Anulación permanente"}}}}, "combo_floor": {"state_attributes": {"preset_mode": {"state": {"cool": "Aire fresco", "fan_only": "Ventilador", "heat": "Calentar aire", "floor_cool": "Piso fresco", "floor_heat": "Piso caliente"}}}}, "swing_as_powerlevel": {"name": "Calentador", "state_attributes": {"swing_mode": {"name": "Nivel de calefacción", "state": {"stop": "Detener", "auto": "Auto"}}}}, "pool_heatpump": {"state_attributes": {"preset_mode": {"state": {"smart_heat": "Calor inteligente", "quick_heat": "Calor rapido", "quiet_heat": "Calor tranquilo", "smart_cool": "Fresco inteligente", "quick_cool": "Fresco rapido", "quiet_cool": "Fresco tranquilo", "auto": "Auto", "smart": "Inteligente", "quick": "<PERSON><PERSON><PERSON><PERSON>", "quiet": "Tran<PERSON>lo"}}}}, "heater": {"name": "Calentador"}, "thermo_switch": {"state_attributes": {"hvac_mode": {"state": {"fan_only": "Pausa"}}}}, "oven": {"state_attributes": {"preset_mode": {"state": {"healthy_steam": "Vapor saludable", "fresh_steam": "Vapor fresco", "high_temp_steam": "Vapor a alta temperatura", "stew": "Estofado", "bake_up_and_down": "Hornear arriba y abajo", "bbq": "BBQ", "bottom_hot_air": "Aire caliente inferior", "on_strong_roast": "<PERSON><PERSON> fuerte", "3d_hot_air": "Aire caliente 3D", "air_fry": "Freír al aire", "steam_frying": "Freír al vapor", "one_click_bread": "Pan de un clic", "quick_heat": "<PERSON><PERSON>", "keep_warm": "Mantener caliente", "unfreeze": "<PERSON><PERSON><PERSON><PERSON>", "fermentation": "Fermentación", "descale": "Descalcificar", "local_recipes": "Recetas locales", "drying": "Secado", "custom": "Personalizado", "low_steaming": "<PERSON><PERSON><PERSON> bajo", "medium_steaming": "Vapor medio", "high_steaming": "<PERSON>apor alto"}}}}}, "humidifier": {"dehumidifier": {"state_attributes": {"mode": {"state": {"laundry": "Ropa seca", "purify": "Purificar"}}}}}, "fan": {"aroma_diffuser": {"name": "Difusor de aroma", "state_attributes": {"preset_mode": {"state": {"low": "<PERSON><PERSON>", "medium": "Medio", "high": "Alto", "continuous": "Continuo", "intermittent": "Intermitente", "timer": "Temporizador"}}}}, "dehumidifier": {"name": "Deshumidificador", "state_attributes": {"preset_mode": {"state": {"purify": "Purificar", "dehumidify": "Deshumid<PERSON>ar"}}}}, "fan_with_presets": {"name": "Ventilador", "state_attributes": {"preset_mode": {"state": {"normal": "Normal", "nature": "Natural", "sleep": "<PERSON><PERSON><PERSON>", "baby": "<PERSON><PERSON><PERSON>", "fresh": "Fresco", "smart": "Inteligente", "strong": "<PERSON><PERSON>e", "custom": "Personalizado", "high": "Alto", "medium": "Medio", "low": "<PERSON><PERSON>", "displayoff": "<PERSON><PERSON><PERSON>", "off": "<PERSON><PERSON><PERSON>"}}}}, "ventilation": {"name": "Ventilación", "state_attributes": {"preset_mode": {"state": {"fresh": "Aire fresco", "circulate": "Circular", "sleep": "<PERSON><PERSON><PERSON>", "auto": "Auto", "eco": "Ecológico", "anti-condensation": "Anticondensación", "extractor": "Extractor", "heat_recovery": "Recuperación de calor", "timer": "Temporizador", "on": "Encender", "off": "<PERSON><PERSON><PERSON>"}}}}}, "light": {"backlight": {"name": "Luz de fondo"}, "display": {"name": "Monitor"}, "embers": {"name": "<PERSON><PERSON><PERSON>"}, "flame": {"name": "Fuego"}, "indicator": {"name": "Indicador"}, "laser": {"name": "<PERSON><PERSON><PERSON>"}, "logs": {"name": "Tronco"}, "nightlight": {"name": "Luz de noche"}}, "lock": {"child_lock": {"name": "Bloqueo infantil"}}, "number": {"timer": {"name": "Temporizador"}, "timer_x": {"name": "Temporizador {x}"}}, "select": {"currency": {"name": "Moneda", "state": {"usd": "USD", "eur": "EUR", "cny": "CNY", "cad": "CAD", "gbp": "GBP"}}, "heat_pump_mode": {"name": "Modo de bomba de calor", "state": {"cool": "<PERSON><PERSON><PERSON><PERSON>", "heat": "Calentar", "auto": "Auto", "floor_heat": "Calor de piso", "off": "<PERSON><PERSON><PERSON>", "hotwater": "Agua caliente", "hotwater_cool": "Agua caliente y enfriar", "hotwater_heat": "Agua caliente y calentar", "hotwater_auto": "Agua caliente y auto", "hotwater_floor_heat": "Agua caliente y calor de piso"}}, "initial_state": {"name": "Estado inicial", "state": {"off": "<PERSON><PERSON><PERSON>", "on": "Encendido", "memory": "Memoria"}}, "kettle_mode": {"name": "<PERSON><PERSON> her<PERSON>", "state": {"off": "<PERSON><PERSON><PERSON>", "heat": "<PERSON><PERSON>", "boil": "<PERSON><PERSON>", "quick_heat": "<PERSON><PERSON>", "quick_boil": "<PERSON><PERSON>", "keep_warm": "Mantener caliente", "custom": "Personalizado", "dechlorinate": "<PERSON><PERSON><PERSON><PERSON>", "black_tea": "Té", "green_tea": "<PERSON>a", "coffee": "Café", "honey_water": "Agua con miel", "infant_formula": "Fórmula infantil", "white_tea": "<PERSON><PERSON> blanco", "oolong_tea": "<PERSON><PERSON>"}}, "language": {"name": "Idioma", "state": {"chinese": "中文", "chinese_traditional": "中文(繁體)", "english": "English", "french": "Français", "german": "De<PERSON>ch", "italian": "Italiano", "japanese": "日本語", "korean": "한국어", "latin": "Lingua Latina", "portuguese": "Português", "russian": "Русский", "spanish": "Español", "turkish": "Türkçe"}}, "light_mode": {"name": "<PERSON><PERSON> de luz", "state": {"off": "<PERSON><PERSON><PERSON>", "on": "Encender", "state": "Estado", "locator": "Localizador"}}, "mopping": {"name": "Fregado", "state": {"off": "<PERSON><PERSON><PERSON>", "auto": "Automático", "low": "<PERSON><PERSON>", "medium": "Medio", "high": "Alto"}}, "recipe": {"name": "Receta", "state": {"pizza": "Pizza", "fries": "<PERSON><PERSON> fritas", "chicken": "Pollo", "shrimp": "Camarones", "fish": "Pescado", "chicken_drumsticks": "<PERSON><PERSON><PERSON> de <PERSON>o", "vegetables": "<PERSON><PERSON><PERSON><PERSON>", "desserts": "<PERSON><PERSON>", "none": "<PERSON><PERSON><PERSON>", "chicken_wings": "Alitas de pollo", "steak": "Filete", "onion_rings": "<PERSON><PERSON>", "bacon": "Tocino", "cake": "Pastel", "bread": "Pan", "toast": "Tostada", "sausage": "Sal<PERSON>cha", "dry_fruit": "<PERSON>uta seca", "custom": "Personalizado", "cloud_recipe": "Receta de la nube", "default": "Predeterminado", "keep_warm": "Mantener caliente", "preheat": "Precalentar"}}, "scene": {"name": "Escena", "state": {"relax": "<PERSON><PERSON><PERSON><PERSON>", "movie": "<PERSON><PERSON><PERSON><PERSON>", "party": "Fiesta", "romantic": "R<PERSON><PERSON><PERSON><PERSON>", "night": "Noche", "morning": "<PERSON><PERSON><PERSON>", "working": "Trabajando", "leisure": "<PERSON><PERSON>", "vacation": "Vacaciones", "reading": "Leyendo", "twinkle": "<PERSON><PERSON><PERSON>", "gaming": "<PERSON><PERSON><PERSON>", "none": "<PERSON><PERSON><PERSON>"}}, "timer": {"name": "Temporizador", "state": {"cancel": "<PERSON><PERSON><PERSON>", "continuous": "Continuo", "30s": "30 segundos", "1m": "1 minuto", "2m": "2 minutos", "5m": "5 minutos", "10m": "10 minutos", "20m": "20 minutos", "30m": "30 minutos", "40m": "40 minutos", "1h": "1 hora", "1h30m": "1 hora 30 minutos", "2h": "2 horas", "2h30m": "2 horas 30 minutos", "3h": "3 horas", "3h30m": "3 horas 30 minutos", "4h": "4 horas", "4h30m": "4 horas 30 minutos", "5h": "5 horas", "5h30m": "5 horas 30 minutos", "6h": "6 horas", "6h30m": "6 horas 30 minutos", "7h": "7 horas", "7h30m": "7 horas 30 minutos", "8h": "8 horas", "8h30m": "8 horas 30 minutos", "9h": "9 horas", "9h30m": "9 horas 30 minutos", "10h": "10 horas", "11h": "11 horas", "12h": "12 horas", "13h": "13 horas", "14h": "14 horas", "15h": "15 horas", "16h": "16 horas", "17h": "17 horas", "18h": "18 horas", "19h": "19 horas", "20h": "20 horas", "21h": "21 horas", "22h": "22 horas", "23h": "23 horas", "24h": "24 horas", "36h": "36 horas", "48h": "48 horas", "72h": "72 horas", "1d": "1 día", "2d": "2 días", "3d": "3 días", "4d": "4 días", "5d": "5 días", "6d": "6 días", "7d": "7 días"}}, "temperature_unit": {"name": "Unidad de temperatura", "state": {"celsius": "<PERSON><PERSON><PERSON>", "fahrenheit": "Fahrenheit"}}, "oven_built_in_recipe": {"name": "Receta incorporada", "state": {"none": "<PERSON><PERSON><PERSON>", "steamed_egg_with_okra": "Huevo al vapor con okra", "steamed_sea_bass": "Lubina al vapor", "steamed_prawns": "Camarones al vapor", "handmade_steamed_bread": "Pan al vapor casero", "fan_steamed_baby_vegetables": "Verduras para bebés al vapor con ventilador", "braised_pork": "<PERSON><PERSON> estofado", "snow_fungus_and_bird_s_nest": "<PERSON><PERSON> de <PERSON> y hongo de nieve", "crab_pot": "<PERSON><PERSON> cangrejo", "potato_ribs": "Costillas de patata", "coconut_chicken_soup": "Sopa de pollo de coco", "snack_platter": "<PERSON> aperitivos", "chicken_skewers": "Brochetas de pollo", "roasted_pork_knuckle": "<PERSON><PERSON><PERSON> de cerdo asado", "dried_lemon": "Limón seco", "pork_jerky": "Carne de cerdo seca", "crispy_hairtail": "Cola de caballo crujiente", "spicy_grilled_fish": "Pescado a la parrilla picante", "roasted_sweet_potatoes": "Batatas asadas", "roasted_chicken_wings": "Alitas de pollo asadas", "cumin_lamb_chops": "Chuletas de cordero al comino", "honey_grilled_chicken": "Pollo a la parrilla con miel", "garlic_eggplant": "<PERSON><PERSON><PERSON><PERSON>", "portuguese_egg_tart": "Pastel de huevo portugués", "creme_brulee": "Crema quemada", "cocoa_chips": "Chips de cacao", "butter_cookies": "Galletas de mantequilla", "chiffon_cake": "Pastel de gasa", "puff_pastry": "<PERSON><PERSON> de masa", "red_bean_bread": "Pan de frijoles rojos", "milk_toast": "Tostada de leche"}}}, "sensor": {"air_quality": {"name": "Calidad del aire", "state": {"excellent": "Excelente", "good": "Bueno", "moderate": "Moderado", "poor": "Malo", "severe": "<PERSON><PERSON><PERSON>"}}, "status": {"name": "Estado", "state": {"available": "Disponible", "plugged_in": "Conectado", "fault_unplugged": "Error (desconectado)", "charging": "Cargando", "discharging": "Descargando", "waiting": "<PERSON><PERSON><PERSON><PERSON>", "charged": "<PERSON><PERSON>", "fault": "Error", "paused": "<PERSON><PERSON><PERSON>", "waiting_for_authorization": "Esperando autorización", "standby": "En espera", "heating": "Calentando", "cooling": "<PERSON><PERSON><PERSON><PERSON>", "keeping_warm": "Manteniendo caliente", "no_water": "Sin agua", "boiling": "<PERSON><PERSON><PERSON><PERSON>", "reserve_only": "Reserva solament", "unknown": "Desconocido", "idle": "Inactivo", "auto": "Automático", "manual": "Manual", "rain_delay": "Retraso por lluvia", "off": "<PERSON><PERSON><PERSON>", "on": "Encendido", "cooking": "<PERSON><PERSON><PERSON><PERSON>", "done": "<PERSON><PERSON>", "door_open": "Puerta abierta", "setting": "<PERSON><PERSON><PERSON><PERSON>", "pre_heating": "Precalentando", "scheduled": "Programado", "at_temperature": "A la temperatura", "done_stage_1": "Hecho etapa 1", "done_stage_2": "Hecho etapa 2", "done_stage_3": "Hecho etapa 3", "done_stage_4": "Hecho etapa 4", "done_stage_5": "Hecho etapa 5", "done_stage_6": "Hecho etapa 6", "done_stage_7": "Hecho etapa 7", "done_stage_8": "Hecho etapa 8", "done_stage_9": "Hecho etapa 9", "done_stage_10": "Hecho etapa 10", "no_food": "Sin comida", "jammed": "Atascado", "blocked": "Bloqueado", "feeding": "<PERSON><PERSON><PERSON><PERSON>", "feeding_complete": "Alimentación completa", "caking": "Formando una capa", "cleaning": "Limpiando", "sleep": "<PERSON><PERSON><PERSON><PERSON>", "sterilizing": "E<PERSON><PERSON><PERSON><PERSON>", "deodorizing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "occupied": "Ocupado", "normal": "Normal", "low": "<PERSON><PERSON>", "high": "Alto", "unwashed": "<PERSON> lavar", "pre_washing": "Pre-lavado", "washing": "<PERSON><PERSON><PERSON>", "rinsing": "<PERSON><PERSON><PERSON><PERSON>", "drying": "<PERSON><PERSON><PERSON>", "air_purging": "Purificación de aire", "anti_freeze": "Anticongelante", "close": "<PERSON><PERSON><PERSON>", "monitor": "Monitor", "working": "Trabajando", "warning": "Advertencia", "starting": "Iniciando", "emptying": "<PERSON><PERSON><PERSON><PERSON>", "resetting": "Restableciendo", "reverse": "Revers<PERSON>", "full": "<PERSON><PERSON><PERSON>", "empty": "Vacío", "missing": "<PERSON><PERSON><PERSON><PERSON>", "formatting": "Formateando", "unformatted": "Sin formatear"}}, "time_remaining": {"name": "Tiempo restante"}, "time_remaining_x": {"name": "Tiempo restante {x}"}, "cooking_status": {"name": "Estado de cocción", "state": {"wait": "Esperando para cocinar", "reservation": "En reserva", "cooking": "En progreso", "cancel": "Cocción cancelada", "done": "Cocción completada", "pause": "Pausa en la cocción"}}, "water_level": {"name": "Nivel de agua", "state": {"full": "<PERSON><PERSON><PERSON>", "high": "Alto", "medium": "Medio", "low": "<PERSON><PERSON>", "empty": "Vacío"}}, "energy_produced": {"name": "Energía producida"}, "energy_consumed": {"name": "Energía consumida"}, "energy_produced_x": {"name": "Energía producida {x}"}, "energy_consumed_x": {"name": "Energía consumida {x}"}, "current_x": {"name": "Corriente {x}"}, "voltage_x": {"name": "Voltaje {x}"}, "power_x": {"name": "Potencia {x}"}}, "switch": {"anti_frost": {"name": "<PERSON><PERSON><PERSON>"}, "evaporator_cleaning": {"name": "Limpieza del evaporador"}, "ionizer": {"name": "<PERSON><PERSON><PERSON>"}, "keytone": {"name": "Tono del teclado"}, "outlet_x": {"name": "Salida {x}"}, "sleep": {"name": "Modo de suspensión"}, "switch_x": {"name": "Interruptor {x}"}, "electrolytic_sterilization": {"name": "Esterilización electrolítica"}, "uv_sterilization": {"name": "Esterilización UV"}}, "text": {"scene": {"name": "Escena"}}, "time": {"timer": {"name": "Temporizador"}, "timer_x": {"name": "Temporizador {x}"}}, "valve": {"valve_x": {"name": "Válvula {x}"}}, "water_heater": {"water_air": {"name": "Calentador de agua"}, "kettle": {"name": "<PERSON><PERSON><PERSON>"}}}}