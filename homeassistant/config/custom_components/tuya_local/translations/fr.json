{"title": "Tuya Local", "config": {"step": {"user": {"title": "Configurer votre appareil Tuya <PERSON>", "description": "Les appareils peuvent être ajoutés manuellement ou assistés par le cloud à l'aide de l'application SmartLife.", "data": {"setup_mode": "Choix de configuration :"}}, "cloud": {"title": "Connectez-vous <PERSON>", "description": "Saisissez votre code utilisateur SmartLife ou Tuya.\n\nVous pouvez trouver ce code dans l'application SmartLife ou Tuya dans l'écran **Paramètres** > **Compte et sécurité**", "data": {"user_code": "Code d'utilisateur:"}}, "scan": {"title": "Complétez la connexion", "description": "Utilisez l'application SmartLife ou l'application Tuya pour scanner le code QR suivant afin de terminer la connexion.\n\nContinuez à l'étape suivante une fois que vous avez terminé cette étape dans l'application."}, "choose_device": {"title": "Choisissez l'appareil à ajouter", "description": "Veuillez sélectionner l'appareil à ajouter dans la première liste déroulante. ", "data": {"device_id": "Choisissez l'appareil :", "hub_id": "Choisissez la passerelle :"}}, "search": {"title": "Localisez l'adresse IP de l'appareil", "description": "Tuya cloud ne fournit pas d'adresses IP locales"}, "local": {"title": "Configurer votre appareil Tuya <PERSON>", "description": "[Suivre ces instructions pour trouver le 'device id' et la 'local key'.](https://github.com/make-all/tuya-local#finding-your-device-id-and-local-key)", "data": {"host": "Adresse <PERSON> ou Nom d'hote", "device_id": "Device ID (ou device_id de la passerelle pour les appareils connectés via une passerelle)", "local_key": "Local key", "protocol_version": "Version du Protocole (Auto si inconnu)", "poll_only": "Poll only (Essayez ceci si votre appareil ne fonctionne pas complètement)", "device_cid": "node_id ou uuid de sous-appareil (Pour les appareils connectés via une passerelle)"}}, "select_type": {"title": "Choisissez le type d'appareil", "description": "Choisissez le type qui correspond à votre appareil", "data": {"type": "Type d'appareil"}}, "choose_entities": {"title": "Details appareil", "description": "Choisissez un nom pour cet appareil", "data": {"name": "Nom"}}}, "abort": {"already_configured": "Un appareil avec cet ID a déjà été ajouté.", "not_supported": "<PERSON><PERSON><PERSON><PERSON>, il n'y a pas de support pour cet appareil.", "no_devices": "Impossible de trouver des appareils non enregistrés pour le compte."}, "error": {"connection": "Impossible de se connecter à votre appareil avec ces réglages. Il peut s'agir d'un problème intermittent ou les réglages sont incorrects.", "does_not_need_hub": "L'appareil n'a pas besoin de passerelle et une a été sélectionnée. ", "needs_hub": "L'appareil a besoin d'une passerelle et aucune n'a été sélectionnée."}}, "selector": {"setup_mode": {"options": {"cloud": "Configuration de l'appareil assistée par le cloud SmartLife.", "manual": "Fournissez manuellement les informations de connexion de l’appareil.", "cloud_fresh_login": "Configuration de l'appareil assistée par le cloud SmartLife (nouvelle connexion)."}}}, "options": {"step": {"user": {"title": "Configurer votre appareil Tuya <PERSON>", "description": "[Suivre ces instructions pour trouver le 'device id' et la 'local key'.](https://github.com/make-all/tuya-local#finding-your-device-id-and-local-key)", "data": {"host": "Adresse <PERSON> ou Nom d'hote", "local_key": "Local key", "protocol_version": "Version du Protocole (Auto si inconnu)", "poll_only": "Poll only (essayez ceci si votre appareil ne fonctionne pas complètement)"}}}, "error": {"connection": "Impossible de se connecter à votre appareil avec ces réglages. Il peut s'agir d'un problème intermittent ou les réglages sont incorrects."}, "abort": {"not_supported": "<PERSON><PERSON><PERSON><PERSON>, il n'y a pas de support pour cet appareil."}}, "entity": {"binary_sensor": {"direction": {"name": "Direction", "state": {"off": "Entrant", "on": "Sortant"}}, "defrost": {"name": "Dégivrer", "state": {"off": "Normale", "on": "Dégivrage"}}, "tank_empty": {"name": "Réservoir", "state": {"off": "OK", "on": "Faible"}}, "tank_full": {"name": "Réservoir", "state": {"off": "OK", "on": "<PERSON><PERSON><PERSON>"}}, "wake": {"name": "Réveil", "state": {"off": "<PERSON><PERSON><PERSON>", "on": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "casdon_oven_fault": {"state_attributes": {"description": {"state": {"e1": "E1: Protection contre la surchauffe de la chambre (dépasse 290°C)", "e2": "E2: Protection contre la surchauffe du bac d'évaporation (dépasse 200°C)", "e3": "E3: Protection contre la basse température de la chambre (inférieure à 35°C)", "e4": "E4: Protection contre la basse température du bac d'évaporation (inférieure à 35°C)", "e5": "E5: Erreur de connexion du capteur (Circuit ouvert)", "e6": "E6: <PERSON><PERSON>ur de connexion du capteur (Court-circuit)", "e7": "E7: Erreur de communication de la carte d'affichage", "e8": "E8: R<PERSON>er<PERSON><PERSON> d'eau ouvert (CN7) ou Bac d'évaporation sec (CN3)"}}}}}, "button": {"factory_reset": {"name": "Réinitialisation d'usine"}, "filter_reset": {"name": "Réinitialisation du filtre"}}, "climate": {"aircon_extra": {"name": "Climatiseur", "state_attributes": {"fan_mode": {"state": {"health": "<PERSON><PERSON>", "medhigh": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "medlow": "Moyen-<PERSON><PERSON>", "natural": "Naturel", "quiet": "Silence", "sleep": "<PERSON><PERSON><PERSON>", "strong": "Fort"}}, "swing_mode": {"state": {"topmost": "<PERSON><PERSON><PERSON> haut", "top": "<PERSON><PERSON>", "middle": "Milieu", "down": "Bas", "downmost": "<PERSON><PERSON><PERSON> bas"}}}}, "thermostat": {"name": "Thermostat", "state_attributes": {"fan_mode": {"state": {"cycle": "Cycle"}}, "preset_mode": {"state": {"manual": "<PERSON>", "program": "Programme", "temp_override": "Annulation temporaire", "perm_override": "Annulation permanente"}}}}, "combo_floor": {"state_attributes": {"preset_mode": {"state": {"cool": "Air froid", "fan_only": "Ventilation", "heat": "Air Chaud", "floor_cool": "Sol froid", "floor_heat": "Sol chaud"}}}}, "swing_as_powerlevel": {"name": "<PERSON><PERSON><PERSON>", "state_attributes": {"swing_mode": {"name": "<PERSON><PERSON><PERSON> de chau<PERSON>", "state": {"stop": "Stop", "auto": "Auto"}}}}, "pool_heatpump": {"state_attributes": {"preset_mode": {"state": {"smart_heat": "Chauffage Intelligent", "quick_heat": "<PERSON><PERSON><PERSON>", "quiet_heat": "Chauffage Eco", "smart_cool": "Froid Intelligent", "quick_cool": "Froid Boost", "quiet_cool": "Froid Eco", "auto": "Auto", "smart": "Intelligent", "quick": "Boost", "quiet": "Eco"}}}}, "heater": {"name": "<PERSON><PERSON><PERSON>"}, "thermo_switch": {"state_attributes": {"hvac_mode": {"state": {"fan_only": "Pause"}}}}, "oven": {"state_attributes": {"preset_mode": {"state": {"healthy_steam": "Vapeur saine", "fresh_steam": "<PERSON><PERSON><PERSON> fra<PERSON>", "high_temp_steam": "Vapeur haute température", "stew": "Ragoût", "bake_up_and_down": "<PERSON><PERSON>sson haut et bas", "bbq": "Barbecue", "bottom_hot_air": "Air chaud bas", "on_strong_roast": "Rôtissage fort", "3d_hot_air": "Air chaud 3D", "air_fry": "Friture à l'air", "steam_frying": "Friture à la vapeur", "one_click_bread": "Pain en un clic", "quick_heat": "Chauffage rapide", "keep_warm": "Garder au chaud", "unfreeze": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fermentation": "Fermentation", "descale": "Détartrage", "local_recipes": "Recettes locales", "drying": "Séchage", "custom": "<PERSON><PERSON><PERSON><PERSON>", "low_steaming": "Faible vapeur", "medium_steaming": "<PERSON><PERSON><PERSON> moyenne", "high_steaming": "Haute vapeur"}}}}}, "humidifier": {"dehumidifier": {"state_attributes": {"mode": {"state": {"laundry": "Vêtements secs", "purify": "Purifier"}}}}}, "fan": {"aroma_diffuser": {"name": "Diffuseur d'arômes", "state_attributes": {"preset_mode": {"state": {"low": "Faible", "medium": "<PERSON><PERSON><PERSON>", "high": "Fort", "continuous": "<PERSON><PERSON><PERSON>", "intermittent": "Intermittent", "timer": "<PERSON><PERSON><PERSON>"}}}}, "dehumidifier": {"name": "Déshumidificateur", "state_attributes": {"preset_mode": {"state": {"purify": "Purifier", "dehumidify": "Déshumidifier"}}}}, "fan_with_presets": {"name": "Ventilateur", "state_attributes": {"preset_mode": {"state": {"normal": "Normale", "nature": "Naturel", "sleep": "<PERSON><PERSON><PERSON>", "baby": "<PERSON><PERSON><PERSON><PERSON>", "fresh": "<PERSON><PERSON>", "smart": "Intelligent", "strong": "Fort", "custom": "<PERSON><PERSON><PERSON><PERSON>", "high": "<PERSON><PERSON>", "medium": "<PERSON><PERSON><PERSON>", "low": "Bas", "displayoff": "Affichage désactivé", "off": "Désactivé"}}}}, "ventilation": {"name": "Ventilation", "state_attributes": {"preset_mode": {"state": {"fresh": "Air frais", "circulate": "Circuler", "sleep": "<PERSON><PERSON><PERSON>", "auto": "Auto", "eco": "Écologique", "anti-condensation": "Anti-condensation", "extractor": "Extracteur", "heat_recovery": "Récupération de chaleur", "timer": "<PERSON><PERSON><PERSON>", "on": "Allumé", "off": "<PERSON><PERSON><PERSON>"}}}}}, "light": {"backlight": {"name": "Ré<PERSON>é<PERSON>lairage"}, "display": {"name": "É<PERSON>ran"}, "embers": {"name": "<PERSON><PERSON><PERSON>"}, "flame": {"name": "<PERSON>lamme"}, "indicator": {"name": "Indicateur"}, "laser": {"name": "Laser"}, "logs": {"name": "Bûche"}, "nightlight": {"name": "Veilleuse"}}, "lock": {"child_lock": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> enfant"}}, "number": {"timer": {"name": "<PERSON><PERSON><PERSON>"}, "timer_x": {"name": "Minuteur {x}"}}, "select": {"currency": {"name": "<PERSON><PERSON>", "state": {"usd": "USD", "eur": "EUR", "cny": "CNY", "cad": "CAD", "gbp": "GBP"}}, "heat_pump_mode": {"name": "Mode de pompe à chaleur", "state": {"heat": "<PERSON><PERSON><PERSON>", "cool": "Refroidissement", "auto": "Auto", "floor_heat": "<PERSON><PERSON><PERSON> de sol", "off": "<PERSON><PERSON><PERSON>", "hotwater": "<PERSON><PERSON> chaude", "hotwater_cool": "Eau chaude et refroidissement", "hotwater_heat": "<PERSON>au chaude et chauffage", "hotwater_auto": "Eau chaude et auto", "hotwater_floor_heat": "<PERSON>au chaude et chauffage de sol"}}, "initial_state": {"name": "État initial", "state": {"off": "Désactivé", "on": "Activé", "memory": "M<PERSON><PERSON><PERSON>"}}, "kettle_mode": {"name": "Mode Bouilloire", "state": {"off": "<PERSON><PERSON><PERSON>", "heat": "<PERSON><PERSON><PERSON>", "boil": "Ébullition", "quick_heat": "Chauffage rapide", "quick_boil": "Ébullition rapide", "keep_warm": "Garder au chaud", "custom": "<PERSON><PERSON><PERSON><PERSON>", "dechlorinate": "Déschlorination", "black_tea": "Thé noir", "green_tea": "Thé vert", "coffee": "Café", "honey_water": "<PERSON><PERSON> au miel", "infant_formula": "Lait infantile", "white_tea": "<PERSON><PERSON><PERSON> blanc", "oolong_tea": "<PERSON><PERSON><PERSON>"}}, "language": {"name": "<PERSON><PERSON>", "state": {"chinese": "中文", "chinese_traditional": "中文(繁體)", "english": "English", "french": "Français", "german": "De<PERSON>ch", "italian": "Italiano", "japanese": "日本語", "korean": "한국어", "latin": "Lingua Latina", "portuguese": "Português", "russian": "Русский", "spanish": "Español", "turkish": "Türkçe"}}, "light_mode": {"name": "Mode d'éclairage", "state": {"off": "<PERSON><PERSON><PERSON>", "on": "Allumé", "state": "État", "locator": "Localisateur"}}, "mopping": {"name": "Nettoyage", "state": {"off": "Désactivé", "auto": "Automatique", "low": "Bas", "medium": "<PERSON><PERSON><PERSON>", "high": "<PERSON><PERSON>"}}, "recipe": {"name": "Recette", "state": {"pizza": "Pizza", "fries": "Frites", "chicken": "Poulet", "shrimp": "<PERSON><PERSON><PERSON><PERSON>", "fish": "Poisson", "chicken_drumsticks": "<PERSON><PERSON><PERSON> de <PERSON>", "vegetables": "Lé<PERSON>es", "desserts": "Desserts", "none": "Aucun", "chicken_wings": "<PERSON><PERSON>", "steak": "Steak", "onion_rings": "Ron<PERSON><PERSON> d'oignon", "bacon": "<PERSON>", "cake": "Gâteau", "bread": "Pain", "toast": "Toast", "sausage": "<PERSON><PERSON><PERSON>", "dry_fruit": "Fruits secs", "custom": "<PERSON><PERSON><PERSON><PERSON>", "cloud_recipe": "Recette Cloud", "default": "Défaut", "keep_warm": "Garder au chaud", "preheat": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "scene": {"name": "<PERSON><PERSON>", "state": {"relax": "Détente", "movie": "Film", "party": "<PERSON><PERSON><PERSON>", "romantic": "Romantique", "night": "Nuit", "morning": "<PERSON>in", "working": "Travail", "leisure": "<PERSON><PERSON>", "vacation": "Vacances", "reading": "Lecture", "twinkle": "Scintillement", "gaming": "<PERSON><PERSON>", "none": "Aucun"}}, "timer": {"name": "<PERSON><PERSON><PERSON>", "state": {"cancel": "Annuler", "continuous": "<PERSON><PERSON><PERSON>", "30s": "30 secondes", "1m": "1 minute", "2m": "2 minutes", "5m": "5 minutes", "10m": "10 minutes", "20m": "20 minutes", "30m": "30 minutes", "40m": "40 minutes", "1h": "1 heure", "1h30m": "1 heure 30 minutes", "2h": "2 heures", "2h30m": "2 heures 30 minutes", "3h": "3 heures", "3h30m": "3 heures 30 minutes", "4h": "4 heures", "4h30m": "4 heures 30 minutes", "5h": "5 heures", "5h30m": "5 heures 30 minutes", "6h": "6 heures", "6h30m": "6 heures 30 minutes", "7h": "7 heures", "7h30m": "7 heures 30 minutes", "8h": "8 heures", "8h30m": "8 heures 30 minutes", "9h": "9 heures", "9h30m": "9 heures 30 minutes", "10h": "10 heures", "11h": "11 heures", "12h": "12 heures", "13h": "13 heures", "14h": "14 heures", "15h": "15 heures", "16h": "16 heures", "17h": "17 heures", "18h": "18 heures", "19h": "19 heures", "20h": "20 heures", "21h": "21 heures", "22h": "22 heures", "23h": "23 heures", "24h": "24 heures", "36h": "36 heures", "48h": "48 heures", "72h": "72 heures", "1d": "1 jour", "2d": "2 jours", "3d": "3 jours", "4d": "4 jours", "5d": "5 jours", "6d": "6 jours", "7d": "7 jours"}}, "temperature_unit": {"name": "Unité de température", "state": {"celsius": "<PERSON><PERSON><PERSON>", "fahrenheit": "Fahrenheit"}}, "oven_built_in_recipe": {"name": "Recette intégrée", "state": {"none": "Aucun", "steamed_egg_with_okra": "Oeuf cuit à la vapeur avec okra", "steamed_sea_bass": "Bar cuit à la vapeur", "steamed_prawns": "<PERSON>revettes cuites à la vapeur", "handmade_steamed_bread": "Pain cuit à la vapeur fait main", "fan_steamed_baby_vegetables": "Légumes pour bébé cuits à la vapeur avec ventilateur", "braised_pork": "<PERSON><PERSON>", "snow_fungus_and_bird_s_nest": "Fungus des neiges et nid d'oiseau", "crab_pot": "<PERSON><PERSON><PERSON>", "potato_ribs": "Côtes de porc aux pommes de terre", "coconut_chicken_soup": "Soupe de poulet à la noix de coco", "snack_platter": "Assiette de collations", "chicken_skewers": "Brochettes de poulet", "roasted_pork_knuckle": "<PERSON><PERSON><PERSON> <PERSON> r<PERSON>", "dried_lemon": "<PERSON>it<PERSON> s<PERSON>", "pork_jerky": "Viande de porc séchée", "crispy_hairtail": "Hairtail croustillant", "spicy_grilled_fish": "Poisson grillé <PERSON>", "roasted_sweet_potatoes": "Patates douces rôties", "roasted_chicken_wings": "<PERSON>les de poulet rôties", "cumin_lamb_chops": "Côtelettes d'agneau au cumin", "honey_grilled_chicken": "Poulet grillé au miel", "garlic_eggplant": "Aubergine à l'ail", "portuguese_egg_tart": "Tarte aux oeufs portugaise", "creme_brulee": "Crème brûlée", "cocoa_chips": "Chips de cacao", "butter_cookies": "Biscuits au beurre", "chiffon_cake": "G<PERSON><PERSON> chiffon", "puff_pastry": "Pâte feuilletée", "red_bean_bread": "Pain aux haricots rouges", "milk_toast": "Pain au lait"}}}, "sensor": {"air_quality": {"name": "Qualité de l'air", "state": {"excellent": "Excellent", "good": "<PERSON>", "moderate": "<PERSON><PERSON><PERSON><PERSON>", "poor": "<PERSON><PERSON><PERSON><PERSON>", "severe": "Grave"}}, "status": {"name": "État", "state": {"available": "Disponible", "plugged_in": "Branché", "fault_unplugged": "<PERSON><PERSON><PERSON><PERSON> (débranché)", "charging": "En charge", "discharging": "En décharge", "waiting": "En attente", "charged": "<PERSON><PERSON><PERSON>", "fault": "Défaut", "paused": "En pause", "waiting_for_authorization": "En attente d'autorisation", "standby": "<PERSON><PERSON><PERSON>", "heating": "<PERSON><PERSON><PERSON>", "cooling": "Refroidissement", "keeping_warm": "Garder au chaud", "no_water": "Pa<PERSON> d'eau", "boiling": "Ébullition", "reserve_only": "Réserve uniquement", "unknown": "Inconnu", "idle": "Inactif", "auto": "Auto", "manual": "<PERSON>", "rain_delay": "<PERSON><PERSON><PERSON> de pluie", "off": "<PERSON><PERSON><PERSON>", "on": "Allumé", "cooking": "<PERSON><PERSON><PERSON><PERSON>", "done": "Fait", "door_open": "Porte ouverte", "setting": "Mise en place", "pre_heating": "Préchauffage", "scheduled": "Programmé", "at_temperature": "À température", "done_stage_1": "Étape 1 terminée", "done_stage_2": "Étape 2 terminée", "done_stage_3": "Étape 3 terminée", "done_stage_4": "Étape 4 terminée", "done_stage_5": "Étape 5 terminée", "done_stage_6": "Étape 6 terminée", "done_stage_7": "Étape 7 terminée", "done_stage_8": "Étape 8 terminée", "done_stage_9": "Étape 9 terminée", "done_stage_10": "Étape 10 terminée", "no_food": "Pas de nourriture", "jammed": "Coincé", "blocked": "<PERSON><PERSON><PERSON><PERSON>", "feeding": "Alimentation", "feeding_complete": "Alimentation complète", "caking": "Agglomération", "cleaning": "Nettoyage", "sleep": "<PERSON><PERSON><PERSON>", "sterilizing": "Stérilisation", "deodorizing": "Désodorisation", "occupied": "<PERSON><PERSON><PERSON><PERSON>", "normal": "Normal", "low": "Faible", "high": "<PERSON><PERSON>", "unwashed": "Non lavé", "pre_washing": "Pré-lavage", "washing": "Lavage", "rinsing": "Rinçage", "drying": "Séchage", "air_purging": "Purification de l'air", "anti_freeze": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "monitor": "Surveillance", "working": "Travail", "warning": "Avertissement", "starting": "Démarrage", "emptying": "Vidange", "resetting": "Réinitialisation", "reverse": "Inverser", "full": "<PERSON><PERSON>", "empty": "Vide", "missing": "Manquant", "formatting": "Formatage", "unformatted": "Non formaté"}}, "time_remaining": {"name": "Temps restant"}, "time_remaining_x": {"name": "Temps restant {x}"}, "cooking_status": {"name": "État de la cuisson", "state": {"wait": "En attente de cuisson", "reservation": "En réservation", "cooking": "En cours", "cancel": "Annulation de la cuisson", "done": "<PERSON><PERSON><PERSON> termin<PERSON>", "pause": "<PERSON><PERSON><PERSON> en pause"}}, "water_level": {"name": "Niveau d'eau", "state": {"full": "<PERSON><PERSON>", "high": "<PERSON><PERSON>", "medium": "Milieu", "low": "Bas", "empty": "Vide"}}, "energy_produced": {"name": "Énergie produite"}, "energy_consumed": {"name": "Énergie consommée"}, "energy_produced_x": {"name": "Énergie produite {x}"}, "energy_consumed_x": {"name": "Énergie consommée {x}"}, "current_x": {"name": "Courant {x}"}, "voltage_x": {"name": "Tension {x}"}, "power_x": {"name": "Puissance {x}"}}, "switch": {"anti_frost": {"name": "Anti-gel"}, "evaporator_cleaning": {"name": "Nettoyage évaporateur"}, "ionizer": {"name": "<PERSON><PERSON><PERSON>"}, "keytone": {"name": "Tonalité des touches"}, "outlet_x": {"name": "Prise {x}"}, "sleep": {"name": "<PERSON><PERSON><PERSON>"}, "switch_x": {"name": "Interrupteur {x}"}, "electrolytic_sterilization": {"name": "Stérilisation électrolytique"}, "uv_sterilization": {"name": "Stérilisation UV"}}, "text": {"scene": {"name": "<PERSON><PERSON>"}}, "time": {"timer": {"name": "<PERSON><PERSON><PERSON>"}, "timer_x": {"name": "Minuteur {x}"}}, "valve": {"valve_x": {"name": "<PERSON><PERSON> {x}"}}, "water_heater": {"water_air": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "kettle": {"name": "<PERSON><PERSON><PERSON><PERSON>"}}}}