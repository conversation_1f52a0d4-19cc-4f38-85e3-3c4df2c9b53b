{"title": "Tuya Local", "config": {"step": {"user": {"title": "Configure o seu dispositivo Tuya Local", "description": "Os dispositivos podem ser adicionados manualmente ou assistidos pela Tuya Cloud com a ajuda do aplicativo SmartLife.", "data": {"setup_mode": "Escolha de configuração:"}}, "cloud": {"title": "Faça login no serviço Tuya", "description": "Insira o seu código de utilizador SmartLife ou Tuya.\n\nPode encontrar esse código na App SmartLife ou Tuya em **Configurações** > opção **Conta e segurança** e insira o código apresentado ", "data": {"user_code": "Código de utilizador:"}}, "scan": {"title": "Conclua o login", "description": "Use a App SmartLife ou Tuya para ler o seguinte código QR e concluir o login.\n\nPasse à próxima etapa assim que concluir a leitura na App."}, "choose_device": {"title": "Escolha o dispositivo a ser adicionado", "description": "Escolha o dispositivo a ser adicionado na primeira lista de escolhas. ", "data": {"device_id": "Escolha o dispositivo:", "hub_id": "Escolha o gateway:"}}, "search": {"title": "Localize o endereço IP do dispositivo", "description": "O Tuya Cloud não fornece endereços IP locais"}, "local": {"title": "Configure seu dispositivo Tuya Local", "description": "[Siga estas instruções para encontrar o ID do seu dispositivo e a chave local.](https://github.com/make-all/tuya-local#finding-your-device-id-and-local-key)", "data": {"host": "Endereço IP ou nome do host", "device_id": "Device ID (ou device_id do gateway para dispositivos conectados via gateway)", "local_key": "Chave Local", "protocol_version": "Versão do protocolo (tente auto se não souber)", "poll_only": "Pesquisar (experimente isto se o seu dispositivo não funcionar totalmente)", "device_cid": "node_id ou uuid do sub-dispositivo (para dispositivos conectados via gateway)"}}, "select_type": {"title": "Escolha o tipo de dispositivo", "description": "Escolha o tipo que corresponde ao seu dispositivo", "data": {"type": "Tipo de dispositivo"}}, "choose_entities": {"title": "Detalhes do dispositivo", "description": "Escolha um nome para este dispositivo", "data": {"name": "Nome"}}}, "abort": {"already_configured": "Um dispositivo com esse ID já foi adicionado.", "not_supported": "<PERSON><PERSON><PERSON><PERSON>, não há suporte para este dispositivo.", "no_devices": "Não foi possível encontrar nenhum dispositivo ainda não registado para a conta."}, "error": {"connection": "Não foi possível conectar ao seu dispositivo com estes detalhes. Pode ser um problema intermitente ou podem estar incorretos.", "does_not_need_hub": "O dispositivo não precisa de gateway mas um foi selecionado. ", "needs_hub": "O dispositivo precisa de um gateway e nenhum foi selecionado."}}, "selector": {"setup_mode": {"options": {"cloud": "Configuração do dispositivo assistido pela Cloud SmartLife.", "manual": "Forneça manualmente informações de conexão do dispositivo.", "cloud_fresh_login": "Configuração do dispositivo assistido pela Cloud SmartLife com login fresco."}}}, "options": {"step": {"user": {"title": "Configure seu dispositivo Tuya Local", "description": "[Siga estas instruções para encontrar o ID do seu dispositivo e a chave local.](https://github.com/make-all/tuya-local#finding-your-device-id-and-local-key)", "data": {"host": "Endereço IP ou nome do host", "local_key": "Chave Local", "protocol_version": "Versão do protocolo (experimente auto se não souber)", "poll_only": "Pesquisar (experimente isto se o seu dispositivo não funcionar totalmente)"}}}, "error": {"connection": "Não foi possível conectar o seu dispositivo com estes detalhes. Pode ser um problema intermitente ou podem estar incorretos."}, "abort": {"not_supported": "<PERSON><PERSON><PERSON><PERSON>, não há suporte para este dispositivo."}}, "entity": {"binary_sensor": {"direction": {"name": "Direção", "state": {"off": "Entrada", "on": "<PERSON><PERSON><PERSON>"}}, "defrost": {"name": "<PERSON><PERSON><PERSON><PERSON>", "state": {"off": "Normal", "on": "<PERSON><PERSON><PERSON><PERSON>"}}, "tank_empty": {"name": "Tan<PERSON>", "state": {"off": "OK", "on": "Baixo"}}, "tank_full": {"name": "Tan<PERSON>", "state": {"off": "OK", "on": "<PERSON><PERSON><PERSON>"}}, "wake": {"name": "Acordar", "state": {"off": "Dormindo", "on": "Acordado"}}, "casdon_oven_fault": {"state_attributes": {"description": {"state": {"e1": "E1: Proteção contra superaquecimento da câmara (excede 290°C)", "e2": "E2: Proteção contra superaquecimento da bandeja de evaporação (excede 200°C)", "e3": "E3: Proteção contra baixa temperatura da câmara (abaixo de 35°C)", "e4": "E4: Proteção contra baixa temperatura da bandeja de evaporação (abaixo de 35°C)", "e5": "E5: <PERSON><PERSON> de conexão do sensor (Circuito aberto)", "e6": "E6: <PERSON><PERSON> de conexão do sensor (Circuito curto)", "e7": "E7: Erro de comunicação da placa de exibição", "e8": "E8: <PERSON><PERSON> (CN7) ou Bandeja de evaporação seca (CN3)"}}}}}, "button": {"factory_reset": {"name": "Redefinição de fábrica"}, "filter_reset": {"name": "Redefinição do filtro"}}, "climate": {"aircon_extra": {"name": "Ar condicionado extra", "state_attributes": {"fan_mode": {"state": {"health": "<PERSON><PERSON><PERSON><PERSON>", "medhigh": "Médio-alto", "medlow": "Médio-baixo", "natural": "Natural", "quiet": "Quieto", "sleep": "<PERSON><PERSON><PERSON>", "strong": "Forte"}}, "swing_mode": {"state": {"topmost": "<PERSON><PERSON> alto", "top": "Alto", "middle": "<PERSON><PERSON>", "down": "Abaixo", "downmost": "<PERSON><PERSON>"}}}}, "thermostat": {"name": "Termostato", "state_attributes": {"fan_mode": {"state": {"cycle": "<PERSON><PERSON><PERSON>"}}, "preset_mode": {"state": {"manual": "Manual", "program": "Programa", "temp_override": "Substituição temporária", "perm_override": "Substituição permanente"}}}}, "combo_floor": {"state_attributes": {"preset_mode": {"state": {"cool": "Ar fresco", "fan_only": "Ventilador", "heat": "<PERSON><PERSON> aque<PERSON>o", "floor_cool": "Chão fresco", "floor_heat": "Chão aquecido"}}}}, "swing_as_powerlevel": {"name": "Aque<PERSON>or", "state_attributes": {"swing_mode": {"name": "Nível de calor", "state": {"stop": "<PERSON><PERSON>", "auto": "Auto"}}}}, "pool_heatpump": {"state_attributes": {"preset_mode": {"state": {"smart_heat": "Calor inteligente", "quick_heat": "<PERSON><PERSON>", "quiet_heat": "<PERSON><PERSON> silencio<PERSON>", "smart_cool": "Arrefecimento inteligente", "quick_cool": "Arrefecimento rápido", "quiet_cool": "Arrefecimento silencioso", "auto": "Auto", "smart": "Inteligente", "quick": "<PERSON><PERSON><PERSON><PERSON>", "quiet": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "heater": {"name": "Aque<PERSON>or"}, "thermo_switch": {"state_attributes": {"hvac_mode": {"state": {"fan_only": "Pausa"}}}}, "oven": {"state_attributes": {"preset_mode": {"state": {"healthy_steam": "<PERSON><PERSON><PERSON>", "fresh_steam": "Vapor fresco", "high_temp_steam": "Vapor de alta temperatura", "stew": "<PERSON><PERSON><PERSON>", "bake_up_and_down": "Assar para cima e para baixo", "bbq": "Churrasco", "bottom_hot_air": "Ar quente inferior", "on_strong_roast": "<PERSON><PERSON> forte", "3d_hot_air": "<PERSON><PERSON> quente <PERSON>", "air_fry": "Fritar no ar", "steam_frying": "Fritura a vapor", "one_click_bread": "Pão de um clique", "quick_heat": "Aquecimento rápido", "keep_warm": "<PERSON><PERSON> quente", "unfreeze": "<PERSON><PERSON><PERSON><PERSON>", "fermentation": "Fermentação", "descale": "Descalcificar", "local_recipes": "Receitas locais", "drying": "Secagem", "custom": "Personalizado", "low_steaming": "Cozimento a vapor baixo", "medium_steaming": "Cozimento a vapor médio", "high_steaming": "Cozimento a vapor alto"}}}}}, "humidifier": {"dehumidifier": {"state_attributes": {"mode": {"state": {"laundry": "Roupa seca", "purify": "Purificar"}}}}}, "fan": {"aroma_diffuser": {"name": "Difusor de aroma", "state_attributes": {"preset_mode": {"state": {"low": "Baixo", "medium": "Médio", "high": "Alto", "continuous": "<PERSON><PERSON><PERSON><PERSON>", "intermittent": "Intermitente", "timer": "Temporizador"}}}}, "dehumidifier": {"name": "Desumidificador", "state_attributes": {"preset_mode": {"state": {"purify": "Purificar", "dehumidify": "Desumidificar"}}}}, "fan_with_presets": {"name": "Ventilador", "state_attributes": {"preset_mode": {"state": {"normal": "Normal", "nature": "Natural", "sleep": "<PERSON><PERSON><PERSON>", "baby": "<PERSON><PERSON><PERSON>", "fresh": "Fresco", "smart": "Inteligente", "strong": "Forte", "custom": "Personalizado", "high": "Alto", "medium": "Médio", "low": "Baixo", "displayoff": "Exibição desligada", "off": "Des<PERSON><PERSON>"}}}}, "ventilation": {"name": "Ventilação", "state_attributes": {"preset_mode": {"state": {"fresh": "Ar fresco", "circulate": "Circular", "sleep": "<PERSON><PERSON><PERSON>", "auto": "Auto", "eco": "Ecológico", "anti-condensation": "Anti-condensação", "extractor": "Extrator", "heat_recovery": "Recuperação de calor", "timer": "Temporizador", "on": "Ligado", "off": "Des<PERSON><PERSON>"}}}}}, "light": {"backlight": {"name": "Luz de fundo"}, "display": {"name": "Tela"}, "embers": {"name": "<PERSON><PERSON><PERSON>"}, "flame": {"name": "<PERSON><PERSON>"}, "indicator": {"name": "Indicador"}, "laser": {"name": "Laser"}, "logs": {"name": "<PERSON><PERSON>"}, "nightlight": {"name": "Luz noturna"}}, "lock": {"child_lock": {"name": "Bloqueio infantil"}}, "number": {"timer": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "timer_x": {"name": "Cronômetro {x}"}}, "select": {"currency": {"name": "<PERSON><PERSON>", "state": {"usd": "USD", "eur": "EUR", "cny": "CNY", "cad": "CAD", "gbp": "GBP"}}, "heat_pump_mode": {"name": "<PERSON>do da bomba de calor", "state": {"heat": "Aquecimento", "cool": "Arrefecimento", "auto": "Automático", "floor_heat": "Aquecimento do chão", "off": "Des<PERSON><PERSON>", "hotwater": "Água quente", "hotwater_cool": "Água quente e resfriamento", "hotwater_heat": "Água quente e aquecimento", "hotwater_auto": "Água quente e automática", "hotwater_floor_heat": "Água quente e aquecimento do chão"}}, "initial_state": {"name": "Estado inicial", "state": {"off": "Des<PERSON><PERSON>", "on": "Ligado", "memory": "Memória"}}, "kettle_mode": {"name": "<PERSON><PERSON> da <PERSON>aleira", "state": {"off": "Des<PERSON><PERSON>", "boil": "<PERSON><PERSON><PERSON>", "heat": "Aquecimento", "quick_boil": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>a", "quick_heat": "Aquecimento rápido", "keep_warm": "<PERSON><PERSON> quente", "custom": "Personalizado", "dechlorinate": "<PERSON><PERSON><PERSON><PERSON>", "black_tea": "<PERSON><PERSON> preto", "green_tea": "<PERSON><PERSON> verde", "coffee": "Café", "honey_water": "Água com mel", "infant_formula": "Fórmula infantil", "white_tea": "<PERSON><PERSON> branco", "oolong_tea": "<PERSON><PERSON> oolong"}}, "language": {"name": "Idioma", "state": {"chinese": "中文", "chinese_traditional": "中文(繁體)", "english": "English", "french": "Français", "german": "De<PERSON>ch", "italian": "Italiano", "japanese": "日本語", "korean": "한국어", "latin": "Lingua Latina", "portuguese": "Português", "russian": "Русский", "spanish": "Español", "turkish": "Türkçe"}}, "light_mode": {"name": "<PERSON><PERSON> de luz", "state": {"off": "Des<PERSON><PERSON>", "on": "Ligado", "state": "Estado", "locator": "Localizador"}}, "mopping": {"name": "Limpeza", "state": {"off": "Des<PERSON><PERSON>", "auto": "Automático", "low": "Baixo", "medium": "Médio", "high": "Alto"}}, "recipe": {"name": "<PERSON><PERSON><PERSON>", "state": {"pizza": "Pizza", "fries": "Batatas fritas", "chicken": "Frango", "shrimp": "Camarão", "fish": "Peixe", "chicken_drumsticks": "Coxas de frango", "vegetables": "Legumes", "desserts": "Sobremesas", "none": "<PERSON><PERSON><PERSON>", "chicken_wings": "Asas de frango", "steak": "Bife", "onion_rings": "<PERSON><PERSON><PERSON>", "bacon": "<PERSON>", "cake": "<PERSON><PERSON>", "bread": "Pão", "toast": "<PERSON><PERSON>", "sausage": "<PERSON><PERSON><PERSON>", "dry_fruit": "<PERSON>uta seca", "custom": "Personalizado", "cloud_recipe": "<PERSON><PERSON><PERSON> da nuvem", "default": "Padrão", "keep_warm": "<PERSON><PERSON> quente", "preheat": "Pré-aquecer"}}, "scene": {"name": "<PERSON><PERSON>", "state": {"relax": "<PERSON><PERSON><PERSON>", "movie": "Filme", "party": "Festa", "romantic": "R<PERSON><PERSON><PERSON><PERSON>", "night": "Noite", "morning": "Manhã", "working": "Trabalhando", "leisure": "Lazer", "vacation": "<PERSON><PERSON><PERSON><PERSON>", "reading": "<PERSON><PERSON>", "twinkle": "Cint<PERSON>r", "gaming": "<PERSON><PERSON><PERSON>", "none": "<PERSON><PERSON><PERSON>"}}, "timer": {"name": "<PERSON><PERSON><PERSON><PERSON>", "state": {"cancel": "<PERSON><PERSON><PERSON>", "continuous": "<PERSON><PERSON><PERSON><PERSON>", "30s": "30 segundos", "1m": "1 minuto", "2m": "2 minutos", "5m": "5 minutos", "10m": "10 minutos", "20m": "20 minutos", "30m": "30 minutos", "40m": "40 minutos", "1h": "1 hora", "1h30m": "1 hora e 30 minutos", "2h": "2 horas", "2h30m": "2 horas e 30 minutos", "3h": "3 horas", "3h30m": "3 horas e 30 minutos", "4h": "4 horas", "4h30m": "4 horas e 30 minutos", "5h": "5 horas", "5h30m": "5 horas e 30 minutos", "6h": "6 horas", "6h30m": "6 horas e 30 minutos", "7h": "7 horas", "7h30m": "7 horas e 30 minutos", "8h": "8 horas", "8h30m": "8 horas e 30 minutos", "9h": "9 horas", "9h30m": "9 horas e 30 minutos", "10h": "10 horas", "11h": "11 horas", "12h": "12 horas", "13h": "13 horas", "14h": "14 horas", "15h": "15 horas", "16h": "16 horas", "17h": "17 horas", "18h": "18 horas", "19h": "19 horas", "20h": "20 horas", "21h": "21 horas", "22h": "22 horas", "23h": "23 horas", "24h": "24 horas", "36h": "36 horas", "48h": "48 horas", "72h": "72 horas", "1d": "1 dia", "2d": "2 dias", "3d": "3 dias", "4d": "4 dias", "5d": "5 dias", "6d": "6 dias", "7d": "7 dias"}}, "temperature_unit": {"name": "Unidade de temperatura", "state": {"celsius": "<PERSON><PERSON><PERSON>", "fahrenheit": "Fahrenheit"}}, "oven_built_in_recipe": {"name": "Receita embutida no forno", "state": {"none": "<PERSON><PERSON><PERSON>", "steamed_egg_with_okra": "Ovo cozido com quiabo", "steamed_sea_bass": "<PERSON><PERSON>", "steamed_prawns": "Camarõ<PERSON> co<PERSON>", "handmade_steamed_bread": "Pão cozido à mão", "fan_steamed_baby_vegetables": "Legumes para bebês cozidos com ventilador", "braised_pork": "<PERSON><PERSON><PERSON>", "snow_fungus_and_bird_s_nest": "Fungo da neve e ninho de pássaro", "crab_pot": "Panela de caranguejo", "potato_ribs": "Costelas de batata", "coconut_chicken_soup": "Sopa de frango com coco", "snack_platter": "Prato de lanche", "chicken_skewers": "Espetinhos de frango", "roasted_pork_knuckle": "<PERSON><PERSON> de porco assado", "dried_lemon": "Limão seco", "pork_jerky": "Carne de porco seca", "crispy_hairtail": "Cauda de cabelo crocante", "spicy_grilled_fish": "<PERSON>eixe grel<PERSON>o pica<PERSON>", "roasted_sweet_potatoes": "Batatas doces assadas", "roasted_chicken_wings": "Asas de frango assadas", "cumin_lamb_chops": "Costeletas de cordeiro com cominho", "honey_grilled_chicken": "Frango grelhado com mel", "garlic_eggplant": "<PERSON><PERSON><PERSON>", "portuguese_egg_tart": "Torta de ovo portuguesa", "creme_brulee": "Creme brulee", "cocoa_chips": "Cacau", "butter_cookies": "Biscoitos de manteiga", "chiffon_cake": "<PERSON><PERSON> chiffon", "puff_pastry": "<PERSON><PERSON> folhada", "red_bean_bread": "Pão de feijão verm<PERSON>ho", "milk_toast": "<PERSON>rada de leite"}}}, "sensor": {"air_quality": {"name": "Qualidade do ar", "state": {"excellent": "Excelente", "good": "Bo<PERSON>", "moderate": "Moderado", "poor": "<PERSON><PERSON><PERSON>", "severe": "<PERSON><PERSON><PERSON>"}}, "status": {"name": "Status", "state": {"available": "Disponível", "plugged_in": "Conectado", "fault_unplugged": "Falha (desconectado)", "charging": "Carregando", "discharging": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "waiting": "Aguardando", "charged": "Carregado", "fault": "<PERSON><PERSON><PERSON>", "paused": "<PERSON><PERSON><PERSON>", "waiting_for_authorization": "Aguardando autorização", "standby": "Em espera", "heating": "Aquecimento", "cooling": "Arrefecimento", "keeping_warm": "<PERSON><PERSON><PERSON> quente", "no_water": "<PERSON><PERSON>", "boiling": "Fervendo", "reserve_only": "Apenas reserva", "unknown": "Desconhecido", "idle": "Inativo", "auto": "Automático", "manual": "Manual", "rain_delay": "Atraso de chuva", "off": "Des<PERSON><PERSON>", "on": "Ligado", "cooking": "Cozinhando", "done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "door_open": "Porta aberta", "setting": "Configuração", "pre_heating": "Pré-aquecimento", "scheduled": "Agendado", "at_temperature": "Na te", "done_stage_1": "Concluído estágio 1", "done_stage_2": "Concluído estágio 2", "done_stage_3": "Concluído estágio 3", "done_stage_4": "Concluído estágio 4", "done_stage_5": "Concluído <PERSON> 5", "done_stage_6": "Concluído <PERSON> 6", "done_stage_7": "Concluído <PERSON> 7", "done_stage_8": "Concluído <PERSON> 8", "done_stage_9": "Concluído <PERSON> 9", "done_stage_10": "Concluído está<PERSON> 10", "no_food": "Sem comida", "jammed": "Encravado", "blocked": "Bloqueado", "feeding": "<PERSON><PERSON><PERSON><PERSON>", "feeding_complete": "Alimentação", "caking": "Aglomerando", "cleaning": "Limpeza", "sleep": "<PERSON><PERSON><PERSON>", "sterilizing": "E<PERSON>ili", "deodorizing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "occupied": "Ocupado", "normal": "Normal", "low": "Baixo", "high": "Alto", "unwashed": "<PERSON><PERSON> lavado", "pre_washing": "Pré-lavagem", "washing": "<PERSON><PERSON><PERSON>", "rinsing": "<PERSON><PERSON>á<PERSON>", "drying": "Secagem", "air_purging": "Purificação do ar", "anti_freeze": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "monitor": "Monitora", "working": "Trabalhando", "warning": "Aviso", "starting": "Iniciando", "emptying": "Esvaziando", "resetting": "Redefinindo", "reverse": "Reverso", "full": "<PERSON><PERSON><PERSON>", "empty": "<PERSON><PERSON><PERSON>", "missing": "<PERSON><PERSON><PERSON><PERSON>", "formatting": "Formatando", "unformatted": "Não formatado"}}, "time_remaining": {"name": "Tempo restante"}, "time_remaining_x": {"name": "Tempo restante {x}"}, "cooking_status": {"name": "Status de cozimento", "state": {"wait": "Aguardando cozimento", "reservation": "Em reserva", "cooking": "Em andamento", "cancel": "Cancelamento de cozimento", "done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pause": "Pausa no cozimento"}}, "water_level": {"name": "Nível de <PERSON>gua", "state": {"full": "<PERSON><PERSON><PERSON>", "high": "Alto", "medium": "Médio", "low": "Baixo", "empty": "<PERSON><PERSON><PERSON>"}}, "energy_produced": {"name": "Energia produzida"}, "energy_consumed": {"name": "Energia consumida"}, "energy_produced_x": {"name": "Energia produzida {x}"}, "energy_consumed_x": {"name": "Energia consumida {x}"}, "current_x": {"name": "Corrente {x}"}, "voltage_x": {"name": "Tensão {x}"}, "power_x": {"name": "Potência {x}"}}, "switch": {"anti_frost": {"name": "<PERSON><PERSON><PERSON>"}, "evaporator_cleaning": {"name": "Limpeza de evaporador"}, "ionizer": {"name": "<PERSON><PERSON><PERSON>"}, "keytone": {"name": "Tom do teclado"}, "outlet_x": {"name": "Tomada {x}"}, "sleep": {"name": "<PERSON><PERSON><PERSON>"}, "switch_x": {"name": "Interruptor {x}"}, "electrolytic_sterilization": {"name": "Esterilização eletrolítica"}, "uv_sterilization": {"name": "Esterilização UV"}}, "text": {"scene": {"name": "<PERSON><PERSON>"}}, "time": {"timer": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "timer_x": {"name": "Cronômetro {x}"}}, "valve": {"valve_x": {"name": "Válvula {x}"}}, "water_heater": {"water_air": {"name": "Aquecedor de <PERSON>"}, "kettle": {"name": "<PERSON><PERSON><PERSON>"}}}}