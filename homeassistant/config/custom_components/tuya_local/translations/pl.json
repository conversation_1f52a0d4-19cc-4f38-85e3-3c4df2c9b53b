{"title": "Tuya Local", "config": {"step": {"user": {"title": "Skonfiguruj swoje urządzenie Tuya Local", "description": "Urządzenia można dodawać ręcznie lub przy pomocy chmury za pomocą aplikacji SmartLife.", "data": {"setup_mode": "Wybór konfiguracji:"}}, "cloud": {"title": "Zaloguj się do Tuyi", "description": "Wprowadź swój kod użytkownika SmartLife lub Tuya.\n\nKod ten znajdziesz w aplikacji SmartLife lub Tuya na ekranie **Ustawienia** > **Konto i bezpieczeństwo** i wprowadź kod widoczny na ekranie ", "data": {"user_code": "Kod użytkownika:"}}, "scan": {"title": "Dokończ logowanie", "description": "Użyj aplikacji SmartLife lub aplikacji Tuya"}, "choose_device": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Wybierz urządzenie do dodania z pierwszej listy rozwijanej. ", "data": {"device_id": "<PERSON><PERSON><PERSON>rz <PERSON>rz<PERSON>zenie:", "hub_id": "<PERSON><PERSON><PERSON><PERSON> bramkę:"}}, "search": {"title": "Znajdź adres IP urządzenia", "description": "Chmura Tuya nie udostępnia lokalnych adresów IP"}, "local": {"title": "Skonfiguruj swoje urządzenie Tuya Local", "description": "[Postępuj zgodnie z instrukcjami by <PERSON><PERSON><PERSON><PERSON> swoje <PERSON>ce ID oraz Local key.](https://github.com/make-all/tuya-local#finding-your-device-id-and-local-key)", "data": {"host": "Adres IP lub nazwa hosta", "device_id": "Device ID (lub device_id bramki dla urządzeń podłączonych poprzez bramkę)", "local_key": "Local key", "protocol_version": "<PERSON><PERSON>ja protokołu (spróbuj auto jeśli nieznana)", "poll_only": "Poll only (spróbuj jeśli Twoje urządzenie nie działa w pełni)", "device_cid": "Sub device node_id lub uuid (dla urządzeń podłączonych poprzez bramkę)"}}, "select_type": {"title": "Wybierz typ urządzenia", "description": "Wybierz typ odpowiadający Twojemu <PERSON>", "data": {"type": "Typ urządzenia"}}, "choose_entities": {"title": "Szczegóły urządzenia", "description": "Wybierz nazwę dla urządzenia", "data": {"name": "Nazwa"}}}, "abort": {"already_configured": "Urządzenie z takim ID już istnieje.", "not_supported": "<PERSON><PERSON><PERSON><PERSON><PERSON>, to urządzenie nie jest wspierane.", "no_devices": "Nie udało się znaleźć żadnych niezarejestrowanych urządzeń dla konta."}, "error": {"connection": "Nie można podłączyć się do urządzenia z tymi danymi. To może być tymczasowy problem lub dane mogą być niewłaściwe.", "does_not_need_hub": "Urządzenie nie potrzebuje bramy i taką wybrano. ", "needs_hub": "Urządzenie wymaga bramy"}}, "selector": {"setup_mode": {"options": {"cloud": "Konfiguracja urządzenia SmartLife wspomagana chmurą.", "manual": "Ręcznie podaj informacje o połączeniu urządzenia.", "cloud_fresh_login": "Konfiguracja urządzenia SmartLife wspomagana chmurą (świeże logowanie)."}}}, "options": {"step": {"user": {"title": "Skonfiguruj swoje urządzenie Tuya Local", "description": "[Postępuj zgodnie z instrukcjami by <PERSON><PERSON><PERSON><PERSON> swój Local key.](https://github.com/make-all/tuya-local#finding-your-device-id-and-local-key)", "data": {"host": "Adres IP lub nazwa hosta", "local_key": "Local key", "protocol_version": "<PERSON><PERSON>ja protokołu (spróbuj auto jeśli nieznana)", "poll_only": "Poll only (spróbuj jeśli Twoje urządzenie nie działa w pełni)"}}}, "error": {"connection": "Nie można podłączyć się do urządzenia z tymi danymi. To może być tymczasowy problem lub dane mogą być niewłaściwe."}, "abort": {"not_supported": "<PERSON><PERSON><PERSON><PERSON><PERSON>, to urządzenie nie jest wspierane."}}, "entity": {"binary_sensor": {"direction": {"name": "<PERSON><PERSON><PERSON><PERSON>", "state": {"off": "Przychodzący", "on": "Wychodzący"}}, "defrost": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state": {"off": "<PERSON><PERSON><PERSON><PERSON>", "on": "Rozmrażanie"}}, "tank_empty": {"name": "Zbiornik", "state": {"off": "OK", "on": "<PERSON><PERSON>"}}, "tank_full": {"name": "Zbiornik", "state": {"off": "OK", "on": "Pełny"}}, "wake": {"name": "<PERSON><PERSON><PERSON>", "state": {"off": "<PERSON><PERSON><PERSON><PERSON>", "on": "<PERSON><PERSON><PERSON>"}}, "casdon_oven_fault": {"state_attributes": {"description": {"state": {"e1": "E1: Ochrona przed przegrzaniem komory (przekracza 290°C)", "e2": "E2: Ochrona przed przegrzaniem tacki parownika (przekracza 200°C)", "e3": "E3: Ochrona przed niską temperaturą komory (poniżej 35°C)", "e4": "E4: Ochrona przed niską temperaturą tacki parownika (poniżej 35°C)", "e5": "E5: <PERSON><PERSON><PERSON><PERSON> połączenia czujnika (obwód otwarty)", "e6": "E6: <PERSON><PERSON><PERSON> (zwarcie)", "e7": "E7: Błąd komunikacji płyty wyświetlacza", "e8": "E8: <PERSON><PERSON><PERSON><PERSON> zbiornik wody (CN7) lub <PERSON>a tacka par<PERSON> (CN3)"}}}}}, "button": {"factory_reset": {"name": "Przywrócenie ustawień fabrycznych"}, "filter_reset": {"name": "Reset filtra"}}, "climate": {"aircon_extra": {"name": "Klimatyzacja", "state_attributes": {"fan_mode": {"state": {"health": "Zdrowy", "medhigh": "Średni-Mocny", "medlow": "Średni-Słaby", "natural": "Naturalny", "quiet": "Cichy", "sleep": "Uśpiony", "strong": "Silny"}}, "swing_mode": {"state": {"topmost": "Najwyższy", "top": "<PERSON><PERSON><PERSON>", "middle": "Średni", "down": "<PERSON><PERSON>", "downmost": "Najniższy"}}}}, "thermostat": {"name": "Termostat", "state_attributes": {"fan_mode": {"state": {"cycle": "Cykl"}}, "preset_mode": {"state": {"manual": "Ręczny", "program": "Program", "temp_override": "Tymczasowo Nadpisany", "perm_override": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "combo_floor": {"state_attributes": {"preset_mode": {"state": {"cool": "Zimne powietrze", "fan_only": "Wiatrak", "heat": "Gorące powietrze", "floor_cool": "Chłodzenie podłogi", "floor_heat": "Ogrzewanie podłogi"}}}}, "swing_as_powerlevel": {"name": "Podgrz<PERSON><PERSON><PERSON>", "state_attributes": {"swing_mode": {"name": "Poziom ogrzewania", "state": {"stop": "Stop", "auto": "Auto"}}}}, "pool_heatpump": {"state_attributes": {"preset_mode": {"state": {"smart_heat": "Inteligentne ogrzewanie", "quick_heat": "Szybkie ogrzewanie", "quiet_heat": "<PERSON><PERSON>", "smart_cool": "Inteligentne chłodzenie", "quick_cool": "Szybkie chłodzenie", "quiet_cool": "<PERSON><PERSON> chł<PERSON>zenie", "auto": "Auto", "smart": "Inteligentne", "quick": "Szybkie", "quiet": "C<PERSON>"}}}}, "heater": {"name": "Podgrz<PERSON><PERSON><PERSON>"}, "thermo_switch": {"state_attributes": {"hvac_mode": {"state": {"fan_only": "<PERSON><PERSON>"}}}}, "oven": {"state_attributes": {"preset_mode": {"state": {"healthy_steam": "Zdrowa para", "fresh_steam": "Świeża para", "high_temp_steam": "Wysoka temperatura pary", "stew": "Duszenie", "bake_up_and_down": "Pieczenie góra i dół", "bbq": "BBQ", "bottom_hot_air": "Dolne gorące powietrze", "on_strong_roast": "Na silnym <PERSON>u", "3d_hot_air": "3D gorące powietrze", "air_fry": "Air Fry", "steam_frying": "Parowarzenie i smażenie", "one_click_bread": "Jedno kliknięcie chleb", "quick_heat": "Szybkie ogrzewanie", "keep_warm": "Podgrzewanie", "unfreeze": "Rozmrażanie", "fermentation": "Fermentacja", "descale": "<PERSON><PERSON><PERSON><PERSON> kamie<PERSON>", "local_recipes": "Lokalne przepisy", "drying": "Suszenie", "custom": "Niestandardowy", "low_steaming": "Niskie par<PERSON>nie", "medium_steaming": "<PERSON><PERSON><PERSON> parowanie", "high_steaming": "Wysokie parowanie"}}}}}, "humidifier": {"dehumidifier": {"state_attributes": {"mode": {"state": {"laundry": "Suszenie ubrania", "purify": "Oczyszczanie"}}}}}, "fan": {"aroma_diffuser": {"name": "Rozpylacz zapachów", "state_attributes": {"preset_mode": {"state": {"low": "<PERSON><PERSON>", "medium": "Średni", "high": "<PERSON><PERSON><PERSON>", "continuous": "Ciągły", "intermittent": "Przerywany", "timer": "Regulator czasowy"}}}}, "dehumidifier": {"name": "Osuszacz", "state_attributes": {"preset_mode": {"state": {"purify": "Oczyszczanie", "dehumidify": "Osuszanie"}}}}, "fan_with_presets": {"name": "Wen<PERSON><PERSON>", "state_attributes": {"preset_mode": {"state": {"normal": "<PERSON><PERSON><PERSON><PERSON>", "nature": "Naturalny", "sleep": "Uśpiony", "baby": "Dziecko", "fresh": "Świeży", "smart": "Inteligentny", "strong": "Silny", "custom": "Niestandardowy", "high": "<PERSON><PERSON><PERSON>", "medium": "Średni", "low": "<PERSON><PERSON>", "displayoff": "Wyświetlacz wyłączony", "off": "Wyłączony"}}}}, "ventilation": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "state_attributes": {"preset_mode": {"state": {"fresh": "Świeże powietrze", "circulate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sleep": "Uśpiony", "auto": "Auto", "eco": "Eco", "anti-condensation": "Zapobieganie kond<PERSON>acji", "extractor": "Wyciąg", "heat_recovery": "Odzysk ciepła", "timer": "Regulator czasowy", "on": "Włączony", "off": "Wyłączony"}}}}}, "light": {"backlight": {"name": "Podświetlenie"}, "display": {"name": "Wyświetlacz"}, "embers": {"name": "Żar"}, "flame": {"name": "Płomień"}, "indicator": {"name": "Wskaźnik"}, "laser": {"name": "Laser"}, "logs": {"name": "Kłoda"}, "nightlight": {"name": "<PERSON><PERSON><PERSON> światło"}}, "lock": {"child_lock": {"name": "Zabezpieczenie przed dziećmi"}}, "number": {"timer": {"name": "Regulator czasowy"}, "timer_x": {"name": "Regulator czasowy {x}"}}, "select": {"currency": {"name": "<PERSON><PERSON><PERSON>", "state": {"usd": "USD", "eur": "EUR", "cny": "CNY", "cad": "CAD", "gbp": "GBP"}}, "heat_pump_mode": {"name": "<PERSON>b pompy cie<PERSON>ła", "state": {"heat": "Ogrzewanie", "cool": "Chłodzenie", "auto": "Auto", "floor_heat": "Ogrzewanie podłogi", "off": "Wyłączony", "hotwater": "Podgrzewanie wody", "hotwater_cool": "Podgrzewanie wody i chłodzenie", "hotwater_heat": "Podgrzewanie wody i ogrzewanie", "hotwater_auto": "Podgrzewanie wody i auto", "hotwater_floor_heat": "Podgrzewanie wody i ogrzewanie podłogi"}}, "initial_state": {"name": "<PERSON>", "state": {"off": "Wyłączony", "on": "Włączony", "memory": "<PERSON><PERSON><PERSON><PERSON>"}}, "kettle_mode": {"name": "<PERSON><PERSON>", "state": {"off": "Wyłączony", "heat": "Ogrzewanie", "boil": "Got<PERSON>nie", "quick_heat": "Szybkie ogrzewanie", "quick_boil": "Szybkie gotowanie", "keep_warm": "Podgrzewanie", "custom": "Niestandardowy", "dechlorinate": "Odchlorowanie", "black_tea": "<PERSON><PERSON><PERSON> herbata", "green_tea": "Ziel<PERSON> herbata", "coffee": "<PERSON><PERSON>", "honey_water": "Woda z miodem", "infant_formula": "<PERSON><PERSON><PERSON> d<PERSON>", "white_tea": "Biała herbata", "oolong_tea": "Herb<PERSON>"}}, "language": {"name": "Język", "state": {"chinese": "中文", "chinese_traditional": "中文(繁體)", "english": "English", "french": "Français", "german": "De<PERSON>ch", "italian": "Italiano", "japanese": "日本語", "korean": "한국어", "latin": "Lingua Latina", "portuguese": "Português", "russian": "Русский", "spanish": "Español", "turkish": "Türkçe"}}, "light_mode": {"name": "<PERSON><PERSON> świ<PERSON>ła", "state": {"off": "Wyłączony", "on": "Włączony", "state": "<PERSON>", "locator": "Lok<PERSON>za<PERSON>"}}, "mopping": {"name": "<PERSON><PERSON>", "state": {"off": "Wyłączony", "auto": "Automatyczny", "low": "<PERSON><PERSON>", "medium": "Średni", "high": "<PERSON><PERSON><PERSON>"}}, "recipe": {"name": "<PERSON><PERSON><PERSON><PERSON>", "state": {"pizza": "Pizza", "fries": "<PERSON><PERSON><PERSON>", "chicken": "<PERSON><PERSON><PERSON>", "shrimp": "K<PERSON>et<PERSON>", "fish": "Ryba", "chicken_drumsticks": "Udko z kurczaka", "vegetables": "Warzywa", "desserts": "<PERSON><PERSON>", "none": "Brak", "chicken_wings": "Skrzydełka z kurczaka", "steak": "Stek", "onion_rings": "Kolcze cebuli", "bacon": "<PERSON><PERSON><PERSON>", "cake": "<PERSON><PERSON><PERSON>", "bread": "<PERSON><PERSON><PERSON>", "toast": "Tost", "sausage": "Kiełbasa", "dry_fruit": "Owoce suszone", "custom": "Niestandardowy", "cloud_recipe": "Przepis z chmury", "default": "Domyślny", "keep_warm": "Podgrzewanie", "preheat": "Rozgrzewanie"}}, "scene": {"name": "<PERSON><PERSON>", "state": {"relax": "<PERSON><PERSON><PERSON>", "movie": "Film", "party": "<PERSON><PERSON>rez<PERSON>", "romantic": "Romantyczny", "night": "Noc", "morning": "<PERSON><PERSON><PERSON>", "working": "<PERSON><PERSON><PERSON>", "leisure": "<PERSON><PERSON><PERSON> czas", "vacation": "<PERSON><PERSON><PERSON><PERSON>", "reading": "Czytanie", "twinkle": "<PERSON><PERSON><PERSON><PERSON>", "gaming": "G<PERSON>", "none": "Brak"}}, "timer": {"name": "Regulator czasowy", "state": {"cancel": "<PERSON><PERSON><PERSON>", "continuous": "Ciągły", "30s": "30 sekund", "1m": "1 minuta", "2m": "2 minuty", "5m": "5 minut", "10m": "10 minut", "20m": "20 minut", "30m": "30 minut", "40m": "40 minut", "1h": "1 godzina", "1h30m": "1 god<PERSON><PERSON> 30 minut", "2h": "2 godziny", "2h30m": "2 godziny 30 minut", "3h": "3 godziny", "3h30m": "3 godziny 30 minut", "4h": "4 godziny", "4h30m": "4 godziny 30 minut", "5h": "5 godzin", "5h30m": "5 godzin 30 minut", "6h": "6 godzin", "6h30m": "6 godzin 30 minut", "7h": "7 godzin", "7h30m": "7 godzin 30 minut", "8h": "8 godzin", "8h30m": "8 godzin 30 minut", "9h": "9 godzin", "9h30m": "9 godzin 30 minut", "10h": "10 godzin", "11h": "11 godzin", "12h": "12 godzin", "13h": "13 godzin", "14h": "14 godzin", "15h": "15 godzin", "16h": "16 godzin", "17h": "17 godzin", "18h": "18 godzin", "19h": "19 godzin", "20h": "20 godzin", "21h": "21 godzin", "22h": "22 godziny", "23h": "23 godziny", "24h": "24 godziny", "36h": "36 godzin", "48h": "48 godzin", "72h": "72 godziny", "1d": "1 dzień", "2d": "2 dni", "3d": "3 dni", "4d": "4 dni", "5d": "5 dni", "6d": "6 dni", "7d": "7 dni"}}, "temperature_unit": {"name": "Jednostka temperatury", "state": {"celsius": "Ce<PERSON><PERSON><PERSON>", "fahrenheit": "Fahrenheita"}}, "oven_built_in_recipe": {"name": "Wbudowan<PERSON> przep<PERSON>", "state": {"none": "Brak", "steamed_bread": "<PERSON><PERSON><PERSON>y", "steamed_egg_with_okra": "Jajko na parze z okrą", "steamed_sea_bass": "Dorsz na parze", "steamed_prawns": "Krewetki na parze", "handmade_steamed_bread": "<PERSON><PERSON><PERSON>", "fan_steamed_baby_vegetables": "Warzywa dla dzieci na parze z wentylatorem", "braised_pork": "Duszone mięso wieprzowe", "snow_fungus_and_bird_s_nest": "Grzyby śnieżne i gniazda ptaków", "crab_pot": "Garnek kraba", "potato_ribs": "Ziemniaki z żeberkami", "coconut_chicken_soup": "Zupa z kurczaka kokosowego", "snack_platter": "Półmisek przekąsek", "chicken_skewers": "Ku<PERSON><PERSON> na patyku", "roasted_pork_knuckle": "Pieczona golonka", "dried_lemon": "<PERSON><PERSON><PERSON>", "pork_jerky": "Suszona wieprzowina", "crispy_hairtail": "Ch<PERSON><PERSON><PERSON>cy hairtail", "spicy_grilled_fish": "Pikantna ryba z grilla", "roasted_sweet_potatoes": "Pieczone bataty", "roasted_chicken_wings": "Pieczone skrzydełka z kurczaka", "cumin_lamb_chops": "Kotlety jagnięce z kminem", "honey_grilled_chicken": "<PERSON><PERSON><PERSON> z miodem z grilla", "garlic_eggplant": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "portuguese_egg_tart": "Portugalski placek z jajkiem", "creme_brulee": "Creme Brulee", "cocoa_chips": "Czekoladowe chipsy", "butter_cookies": "Ciasteczka maslane", "chiffon_cake": "<PERSON><PERSON><PERSON>", "puff_pastry": "<PERSON><PERSON><PERSON>", "red_bean_bread": "Chleb z czerwoną fasolą", "milk_toast": "Mlecz<PERSON> tost"}}}, "sensor": {"air_quality": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> powietrza", "state": {"excellent": "Doskonała", "good": "Dobra", "moderate": "Umiarkowana", "poor": "Zła", "severe": "<PERSON><PERSON><PERSON>"}}, "status": {"name": "Status", "state": {"available": "Dostępny", "plugged_in": "Podłączony", "fault_unplugged": "Usterka (odłączony)", "charging": "Ładowanie", "discharging": "Rozładowywanie", "waiting": "Oczekiwanie", "charged": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fault": "Uster<PERSON>", "paused": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "waiting_for_authorization": "Oczekiwanie na autoryzację", "standby": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heating": "Ogrzewanie", "cooling": "Chłodzenie", "keeping_warm": "Podgrzewanie", "no_water": "<PERSON><PERSON> wody", "boiling": "Got<PERSON>nie", "reserve_only": "<PERSON><PERSON><PERSON>", "unknown": "<PERSON><PERSON><PERSON><PERSON>", "idle": "Bezczynny", "auto": "Auto", "manual": "Ręczny", "rain_delay": "Opóźnienie deszczowe", "off": "Wyłączony", "on": "Włączony", "cooking": "Got<PERSON>nie", "done": "<PERSON><PERSON><PERSON>", "door_open": "<PERSON><PERSON><PERSON>", "setting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pre_heating": "Rozgrzewanie", "scheduled": "Zaplanowane", "at_temperature": "W temperaturze", "done_stage_1": "Gotowe etap 1", "done_stage_2": "Gotowe etap 2", "done_stage_3": "Gotowe etap 3", "done_stage_4": "Gotowe etap 4", "done_stage_5": "Gotowe etap 5", "done_stage_6": "Gotowe etap 6", "done_stage_7": "Gotowe etap 7", "done_stage_8": "Gotowe etap 8", "done_stage_9": "Gotowe etap 9", "done_stage_10": "Gotowe etap 10", "no_food": "Brak <PERSON>", "jammed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blocked": "Zablokowany", "feeding": "<PERSON><PERSON><PERSON><PERSON>", "feeding_complete": "Karmienie zakończone", "caking": "Zlepianie", "cleaning": "Czyszczenie", "sleep": "Uśpienie", "sterilizing": "Sterylizacja", "deodorizing": "Odświeżanie", "occupied": "Zaj<PERSON><PERSON>", "normal": "Normalny", "low": "<PERSON><PERSON>", "high": "<PERSON><PERSON><PERSON>", "unwashed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pre_washing": "Przed myciem", "washing": "<PERSON><PERSON>", "rinsing": "Płukanie", "drying": "Suszenie", "air_purging": "Oczyszczanie powietrza", "anti_freeze": "Ochrona przed zamarzaniem", "close": "Zamknięty", "monitor": "Monitorowanie", "working": "Pracuje", "warning": "Ostrzeżenie", "starting": "Uruchamianie", "emptying": "Opróżnianie", "resetting": "Resetowanie", "reverse": "Odwrócone", "full": "Pełne", "empty": "<PERSON><PERSON><PERSON>", "missing": "Brakujące", "formatting": "Formatowanie", "unformatted": "Niezformatowane"}}, "time_remaining": {"name": "Pozostały czas"}, "time_remaining_x": {"name": "Pozostały czas {x}"}, "cooking_status": {"name": "Got<PERSON>nie", "state": {"wait": "Oczekiwanie na gotowanie", "reservation": "Zarezerwowane", "cooking": "<PERSON> trakcie", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "done": "Gotowanie zakończone", "pause": "Gotowanie wstrzymane"}}, "water_level": {"name": "<PERSON><PERSON><PERSON> wody", "state": {"full": "Pełny", "high": "<PERSON><PERSON><PERSON>", "medium": "Średni", "low": "<PERSON><PERSON>", "empty": "<PERSON><PERSON><PERSON>"}}, "energy_produced": {"name": "Energia wyprodukowana"}, "energy_consumed": {"name": "Energia zużyta"}, "energy_produced_x": {"name": "Energia wyprodukowana {x}"}, "energy_consumed_x": {"name": "Energia zużyta {x}"}, "current_x": {"name": "Prąd {x}"}, "voltage_x": {"name": "<PERSON><PERSON><PERSON><PERSON> {x}"}, "power_x": {"name": "Moc {x}"}}, "switch": {"anti_frost": {"name": "Ochrona przed zamarzaniem"}, "evaporator_cleaning": {"name": "Czyszczenie parownika"}, "ionizer": {"name": "<PERSON><PERSON><PERSON>"}, "keytone": {"name": "Dzwięk <PERSON>"}, "outlet_x": {"name": "Gniazdko {x}"}, "sleep": {"name": "Tryb uśpienia"}, "switch_x": {"name": "Przełącznik {x}"}, "electrolytic_sterilization": {"name": "Elektrolityczna sterylizacja"}, "uv_sterilization": {"name": "Sterylizacja UV"}}, "text": {"scene": {"name": "<PERSON><PERSON>"}}, "time": {"timer": {"name": "Regulator czasowy"}, "timer_x": {"name": "Regulator czasowy {x}"}}, "valve": {"valve_x": {"name": "Zawór {x}"}}, "water_heater": {"water_air": {"name": "Podgrzewacz wody"}, "kettle": {"name": "<PERSON><PERSON><PERSON><PERSON>"}}}}