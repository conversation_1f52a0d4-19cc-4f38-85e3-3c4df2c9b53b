{"title": "Tuya Local", "config": {"step": {"user": {"title": "Konfigurer din lokale Tuya enhet", "description": "Enheter kan enten legges til manuelt eller skyassistert ved hjelp av SmartLife appen.", "data": {"setup_mode": "Oppsettvalg:"}}, "cloud": {"title": "Logg inn på Tuya", "description": "Skriv inn SmartLife eller Tuya brukerkoden din.\n\nDu finner denne koden i SmartLife appen eller Tuya appen i **Innstillinger** > **Konto og sikkerhet**-skjermbildet", "data": {"user_code": "Brukerkode:"}}, "scan": {"title": "<PERSON>f<PERSON>r på<PERSON>", "description": "Bruk SmartLife appen eller Tuya appen for å skanne følgende QR-kode for å fullføre påloggingen.\n\nFortsett til neste trinn når du har fullført dette trinnet i appen."}, "choose_device": {"title": "Velg enheten du vil legge til", "description": "Velg enheten du vil legge til fra den første rullegardinlisten. ", "data": {"device_id": "Velg enhet:", "hub_id": "Velg gateway:"}}, "search": {"title": "Finn enhetens IP-adresse", "description": "Tuya cloud gir ikke lokale IP-adresser"}, "local": {"title": "Konfigurer din lokale Tuya enhet", "description": "[<PERSON>ølg instruksjonene her for å finne enhets-id og den lokale nøkkelen.](https://github.com/make-all/tuya-local#finding-your-device-id-and-local-key)", "data": {"host": "IP addresse eller vertsnavn", "or device_id of gateway for devices connected via gateway": "", "device_id": "Enhets-ID (device_id, eller device_id for gateway for enheter tilkoblet via gateway)", "local_key": "<PERSON><PERSON> nøkkel", "protocol_version": "Protocol versjon (prøv auto hvis du der usikker)", "poll_only": "Kun poll (prøv dette hvis enhenten ikke virker ordentlig)", "device_cid": "Underenhets node_id eller uuid (for enheter tilkoblet via gateway)"}}, "select_type": {"title": "Velg enhetstypen", "description": "Velg enhetstypen som tilsvarer din enhet", "data": {"type": "Enhetstype"}}, "choose_entities": {"title": "Enhetsdetaljer", "description": "Angi et navn for din enhet", "data": {"name": "Navn"}}}, "abort": {"already_configured": "En enhet med denne ID-en eksisterer allerede.", "not_supported": "<PERSON><PERSON><PERSON>, det er ingen støtte for denne enheten.", "no_devices": "Kan ikke finne noen uregistrerte enheter for kontoen."}, "error": {"connection": "Kunne ikke koble til enheten med de angitte detaljene. Det kan være en midlertidig feil, eller feil med detaljene angitt.", "does_not_need_hub": "Enheten trenger ikke en gateway", "needs_hub": "Enheten trenger en gateway"}}, "selector": {"setup_mode": {"options": {"cloud": "SmartLife sky-assist<PERSON>.", "manual": "Oppgi informasjon om enhetstilkobling manuelt.", "cloud_fresh_login": "SmartLife sky-assist<PERSON> en<PERSON> med ny pålogging."}}}, "options": {"step": {"user": {"title": "Konfigurer din lokale Tuya enhet", "description": "[<PERSON>ølg instruksjonene her for å finne enhets-id og den lokale nøkkelen.](https://github.com/make-all/tuya-local#finding-your-device-id-and-local-key)", "data": {"host": "IP addresse eller vertsnavn", "local_key": "<PERSON><PERSON> nøkkel", "protocol_version": "Protocol versjon (prøv auto hvis du der usikker)", "poll_only": "Kun poll (prøv dette hvis enhenten ikke virker ordentlig)"}}}, "error": {"connection": "Kunne ikke koble til enheten med de angitte detaljene. Det kan være en midlertidig feil, eller feil med detaljene angitt."}, "abort": {"not_supported": "<PERSON><PERSON><PERSON>, det er ingen støtte for denne enheten"}}, "entity": {"binary_sensor": {"direction": {"name": "Retning", "state": {"off": "<PERSON><PERSON>", "on": "Ute"}}, "defrost": {"name": "<PERSON>e", "state": {"off": "Normal", "on": "Tining"}}, "tank_empty": {"name": "Tank", "state": {"off": "OK", "on": "Lav"}}, "tank_full": {"name": "Tank", "state": {"off": "OK", "on": "Full"}}, "wake": {"name": "<PERSON><PERSON><PERSON><PERSON>", "state": {"off": "Søvn", "on": "Våken"}}, "casdon_oven_fault": {"state_attributes": {"description": {"state": {"e1": "E1: <PERSON><PERSON> overoppheting beskyttelse (over 290°C)", "e2": "E2: Evaporation tray overoppheting beskyttelse (over 200°C)", "e3": "E3: Kammer lav temperatur beskyttelse (under 35°C)", "e4": "E4: Evaporation tray lav temperatur beskyttelse (under 35°C)", "e5": "E5: <PERSON><PERSON> (Åpen krets)", "e6": "E6: <PERSON><PERSON> (Kortslutning)", "e7": "E7: Displaybrett kommunikasjonsfeil", "e8": "E8: <PERSON><PERSON><PERSON> (CN7) eller Evaporation tray tørr (CN3)"}}}}}, "button": {"factory_reset": {"name": "Fabrikkinnstilling"}, "filter_reset": {"name": "Filter tilbakestilling"}}, "climate": {"aircon_extra": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "state_attributes": {"fan_mode": {"state": {"health": "Healthy", "medhigh": "Medium-Høy", "medlow": "Medium-Lav", "natural": "Nøytral", "quiet": "<PERSON>e", "sleep": "Søvn", "strong": "Sterk"}}, "swing_mode": {"state": {"topmost": "Høyeste", "top": "Hø<PERSON>", "middle": "Midterst", "down": "<PERSON><PERSON>", "downmost": "<PERSON><PERSON><PERSON>"}}}}, "thermostat": {"name": "Termostat", "state_attributes": {"fan_mode": {"state": {"cycle": "Syklus"}}, "preset_mode": {"state": {"manual": "<PERSON><PERSON>", "program": "Program", "temp_override": "<PERSON><PERSON><PERSON><PERSON><PERSON> overstyring", "perm_override": "Permanent overstyrt"}}}}, "combo_floor": {"state_attributes": {"preset_mode": {"state": {"cool": "<PERSON><PERSON><PERSON><PERSON><PERSON> luft", "fan_only": "Vifte", "heat": "Varm luft", "floor_cool": "<PERSON><PERSON><PERSON><PERSON> gulv", "floor_heat": "Varm gulv"}}}}, "swing_as_powerlevel": {"name": "Varmeapparat", "state_attributes": {"swing_mode": {"name": "<PERSON><PERSON><PERSON>", "state": {"stop": "Stopp", "auto": "Auto"}}}}, "pool_heatpump": {"state_attributes": {"preset_mode": {"state": {"smart_heat": "Smart varming", "quick_heat": "<PERSON><PERSON><PERSON> varming", "quiet_heat": "<PERSON><PERSON> varming", "smart_cool": "<PERSON> kjøling", "quick_cool": "<PERSON><PERSON><PERSON> k<PERSON>ø<PERSON>", "quiet_cool": "<PERSON><PERSON> kjø<PERSON>", "auto": "Auto", "smart": "Smart", "quick": "<PERSON><PERSON><PERSON>", "quiet": "<PERSON>e"}}}}, "heater": {"name": "Varmeapparat"}, "thermo_switch": {"state_attributes": {"hvac_mode": {"state": {"fan_only": "Pause"}}}}, "oven": {"state_attributes": {"preset_mode": {"state": {"healthy_steam": "Sunn damp", "fresh_steam": "Fersk damp", "high_temp_steam": "Høy temp damp", "stew": "Stuing", "bake_up_and_down": "Bake opp og ned", "bbq": "BBQ", "bottom_hot_air": "<PERSON><PERSON><PERSON>", "on_strong_roast": "<PERSON><PERSON> sterk steking", "3d_hot_air": "3D varmluft", "air_fry": "Luftfritering", "steam_frying": "Dampsteking", "one_click_bread": "Ett-klikk brød", "quick_heat": "<PERSON><PERSON><PERSON> varming", "keep_warm": "Hold varm", "unfreeze": "Avriming", "fermentation": "Fermentering", "descale": "Avkalkning", "local_recipes": "Lokale oppskrifter", "drying": "<PERSON><PERSON><PERSON><PERSON>", "custom": "Egendefinert", "low_steaming": "Lav Steaming", "medium_steaming": "Medium Steaming", "high_steaming": "Høy Steaming"}}}}}, "humidifier": {"dehumidifier": {"state_attributes": {"mode": {"state": {"laundry": "<PERSON><PERSON><PERSON> klær", "purify": "<PERSON><PERSON>"}}}}}, "fan": {"aroma_diffuser": {"name": "Aroma diffuser", "state_attributes": {"preset_mode": {"state": {"low": "Lav", "medium": "Medium", "high": "Hø<PERSON>", "continuous": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "intermittent": "Intermitterende", "timer": "Timer"}}}}, "dehumidifier": {"name": "Avfukter", "state_attributes": {"preset_mode": {"state": {"purify": "<PERSON><PERSON>", "dehumidify": "Avfukte"}}}}, "fan_with_presets": {"name": "Vifte", "state_attributes": {"preset_mode": {"state": {"normal": "Normal", "nature": "Nøytral", "sleep": "Søvn", "baby": "Baby", "fresh": "Fresh", "smart": "Smart", "strong": "Sterk", "custom": "Egendefinert", "high": "Hø<PERSON>", "medium": "Medium", "low": "Lav", "displayoff": "Skjerm av", "off": "Av"}}}}, "ventilation": {"name": "Ventilasjon", "state_attributes": {"preset_mode": {"state": {"fresh": "Frisk luft", "circulate": "<PERSON><PERSON><PERSON><PERSON>", "sleep": "Sove", "auto": "Auto", "eco": "<PERSON><PERSON>", "anti-condensation": "Anti-kondens", "extractor": "Ekstraktor", "heat_recovery": "Varmeutvinning", "timer": "Timer", "on": "På", "off": "Av"}}}}}, "light": {"backlight": {"name": "Bakgrunnsbelysning"}, "display": {"name": "Skjerm"}, "embers": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "flame": {"name": "<PERSON>lamme"}, "indicator": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "laser": {"name": "Laser"}, "logs": {"name": "Tømmerstokk"}, "nightlight": {"name": "<PERSON><PERSON><PERSON>"}}, "lock": {"child_lock": {"name": "<PERSON><PERSON><PERSON>"}}, "number": {"timer": {"name": "Timer"}, "timer_x": {"name": "Timer {x}"}}, "select": {"currency": {"name": "Valuta", "state": {"usd": "USD", "eur": "EUR", "cny": "CNY", "cad": "CAD", "gbp": "GBP"}}, "heat_pump_mode": {"name": "Varme pumpe modus", "state": {"heat": "Varm", "cool": "<PERSON><PERSON>", "auto": "Auto", "floor_heat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "off": "Av", "hotwater": "V<PERSON><PERSON> vann", "hotwater_cool": "Varmt vann og kald", "hotwater_heat": "Varmt vann og varmt", "hotwater_auto": "Varmt vann og auto", "hotwater_floor_heat": "Varmt vann og gulvvarme"}}, "initial_state": {"name": "Starttilstand", "state": {"off": "Av", "on": "På", "memory": "<PERSON><PERSON>"}}, "kettle_mode": {"name": "<PERSON><PERSON><PERSON><PERSON>", "state": {"off": "Av", "heat": "Varm", "boil": "Kok", "quick_heat": "<PERSON><PERSON><PERSON> varming", "quick_boil": "<PERSON><PERSON><PERSON> koking", "keep_warm": "Hold varm", "custom": "Egendefinert", "dechlorinate": "Avkalkning", "black_tea": "Svart te", "green_tea": "<PERSON><PERSON><PERSON><PERSON> te", "coffee": "<PERSON><PERSON>", "honey_water": "Honningvann", "infant_formula": "Barnemat", "white_tea": "Hvit te", "oolong_tea": "Oolong te"}}, "language": {"name": "Språk", "state": {"chinese": "中文", "chinese_traditional": "中文(繁體)", "english": "English", "french": "Français", "german": "De<PERSON>ch", "italian": "Italiano", "japanese": "日本語", "korean": "한국어", "latin": "Lingua Latina", "portuguese": "Português", "russian": "Русский", "spanish": "Español", "turkish": "Türkçe"}}, "light_mode": {"name": "<PERSON><PERSON><PERSON><PERSON>", "state": {"off": "Av", "on": "På", "state": "Tilstand", "locator": "<PERSON><PERSON><PERSON>"}}, "mopping": {"name": "<PERSON><PERSON>", "state": {"off": "Av", "auto": "Auto", "low": "Lav", "medium": "Medium", "high": "Hø<PERSON>"}}, "recipe": {"name": "Oppskrift", "state": {"pizza": "Pizza", "fries": "Pommes frites", "chicken": "<PERSON><PERSON><PERSON>", "shrimp": "<PERSON><PERSON>", "fish": "Fisk", "chicken_drumsticks": "Kyllinglår", "vegetables": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "desserts": "Desserter", "none": "Ingen", "chicken_wings": "Kyllingving<PERSON>", "steak": "Biff", "onion_rings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bacon": "<PERSON>", "cake": "<PERSON><PERSON>", "bread": "<PERSON><PERSON><PERSON><PERSON>", "toast": "Toast", "sausage": "<PERSON><PERSON><PERSON><PERSON>", "dry_fruit": "Tørket frukt", "custom": "Egendefinert", "cloud_recipe": "Skyoppskrift", "default": "Standard", "keep_warm": "Hold varm", "preheat": "<PERSON><PERSON><PERSON>"}}, "scene": {"name": "Scene", "state": {"relax": "Slapp av", "movie": "Film", "party": "Fest", "romantic": "Romantisk", "night": "Natt", "morning": "<PERSON><PERSON>", "working": "<PERSON><PERSON><PERSON><PERSON>", "leisure": "Fritid", "vacation": "<PERSON><PERSON>", "reading": "Lesing", "twinkle": "Blink", "gaming": "Spill", "none": "Ingen"}}, "timer": {"name": "Timer", "state": {"cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "continuous": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "30s": "30 sekunder", "1m": "1 minutt", "2m": "2 minutter", "5m": "5 minutter", "10m": "10 minutter", "20m": "20 minutter", "30m": "30 minutter", "40m": "40 minutter", "1h": "1 time", "1h30m": "1 time 30 minutter", "2h": "2 timer", "2h30m": "2 timer 30 minutter", "3h": "3 timer", "3h30m": "3 timer 30 minutter", "4h": "4 timer", "4h30m": "4 timer 30 minutter", "5h": "5 timer", "5h30m": "5 timer 30 minutter", "6h": "6 timer", "6h30m": "6 timer 30 minutter", "7h": "7 timer", "7h30m": "7 timer 30 minutter", "8h": "8 timer", "8h30m": "8 timer 30 minutter", "9h": "9 timer", "9h30m": "9 timer 30 minutter", "10h": "10 timer", "11h": "11 timer", "12h": "12 timer", "13h": "13 timer", "14h": "14 timer", "15h": "15 timer", "16h": "16 timer", "17h": "17 timer", "18h": "18 timer", "19h": "19 timer", "20h": "20 timer", "21h": "21 timer", "22h": "22 timer", "23h": "23 timer", "24h": "24 timer", "36h": "36 timer", "48h": "48 timer", "72h": "72 timer", "1d": "1 dag", "2d": "2 dager", "3d": "3 dager", "4d": "4 dager", "5d": "5 dager", "6d": "6 dager", "7d": "7 dager"}}, "temperature_unit": {"name": "Temperaturenhet", "state": {"celsius": "<PERSON><PERSON><PERSON>", "fahrenheit": "Fahrenheit"}}, "oven_built_in_recipe": {"name": "Innebygd oppskrift", "state": {"none": "Ingen", "steamed_egg_with_okra": "Dampet egg med okra", "steamed_sea_bass": "<PERSON><PERSON> havabbor", "steamed_prawns": "<PERSON><PERSON><PERSON> re<PERSON>", "handmade_steamed_bread": "Håndlaget dampet brød", "fan_steamed_baby_vegetables": "<PERSON>ifte dampede babyg<PERSON><PERSON><PERSON>", "braised_pork": "<PERSON><PERSON><PERSON>", "snow_fungus_and_bird_s_nest": "Snøsopp og fuglerede", "crab_pot": "Krabbe gryte", "potato_ribs": "Potet ribber", "coconut_chicken_soup": "Kokosnøtt kyllingsuppe", "snack_platter": "Snackskål", "chicken_skewers": "Kyllingspyd", "roasted_pork_knuckle": "Stekt svineknoker", "dried_lemon": "Tørket sitron", "pork_jerky": "<PERSON><PERSON>ekj<PERSON>tt jerky", "crispy_hairtail": "<PERSON><PERSON><PERSON><PERSON> h<PERSON><PERSON><PERSON>", "spicy_grilled_fish": "K<PERSON>dret grillet fisk", "roasted_sweet_potatoes": "Stekte søtpoteter", "roasted_chicken_wings": "Stekte kyllingvinger", "cumin_lamb_chops": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "honey_grilled_chicken": "Honninggrillet kylling", "garlic_eggplant": "<PERSON><PERSON><PERSON><PERSON><PERSON> auberg<PERSON>", "portuguese_egg_tart": "Portugisisk eggepai", "creme_brulee": "Crème brûlée", "cocoa_chips": "Kakao chips", "butter_cookies": "Smørkaker", "chiffon_cake": "Chiffonkake", "puff_pastry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "red_bean_bread": "<PERSON><PERSON><PERSON>", "milk_toast": "Melketoast"}}}, "sensor": {"air_quality": {"name": "Luftkvalitet", "state": {"excellent": "Utmerket", "good": "God", "moderate": "<PERSON>rat", "poor": "<PERSON><PERSON><PERSON><PERSON>", "severe": "<PERSON><PERSON><PERSON><PERSON>"}}, "status": {"name": "Status", "state": {"available": "Tilgjengelig", "plugged_in": "Tilkoblet", "fault_unplugged": "<PERSON><PERSON> (frakoblet)", "charging": "Lading", "discharging": "<PERSON><PERSON><PERSON><PERSON>", "waiting": "Venter", "charged": "<PERSON><PERSON>", "fault": "<PERSON><PERSON>", "paused": "<PERSON><PERSON><PERSON>", "waiting_for_authorization": "Venter på autorisasjon", "standby": "Standby", "heating": "<PERSON><PERSON><PERSON> opp", "cooling": "<PERSON><PERSON><PERSON><PERSON>", "keeping_warm": "Holder varm", "no_water": "<PERSON><PERSON> vann", "boiling": "<PERSON><PERSON>", "reserve_only": "Reservert", "unknown": "<PERSON><PERSON><PERSON><PERSON>", "idle": "Inaktiv", "auto": "Auto", "manual": "<PERSON><PERSON>", "rain_delay": "<PERSON><PERSON> forsin<PERSON>", "off": "Av", "on": "På", "cooking": "<PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON>", "door_open": "<PERSON><PERSON><PERSON>", "setting": "Innstilling", "pre_heating": "<PERSON><PERSON><PERSON>", "scheduled": "<PERSON><PERSON><PERSON>", "at_temperature": "Ved temperatur", "done_stage_1": "Ferdig trinn 1", "done_stage_2": "Ferdig trinn 2", "done_stage_3": "Ferdig trinn 3", "done_stage_4": "Ferdig trinn 4", "done_stage_5": "Ferdig trinn 5", "done_stage_6": "<PERSON><PERSON>g trinn 6", "done_stage_7": "Ferdig trinn 7", "done_stage_8": "Ferdig trinn 8", "done_stage_9": "Ferdig trinn 9", "done_stage_10": "Ferdig trinn 10", "no_food": "Ingen mat", "jammed": "Fastkjørt", "blocked": "Blokkert", "feeding": "Fôring", "feeding_complete": "<PERSON><PERSON><PERSON> fullført", "caking": "<PERSON><PERSON>", "cleaning": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sleep": "Søvn", "sterilizing": "<PERSON><PERSON><PERSON><PERSON>", "deodorizing": "<PERSON><PERSON><PERSON><PERSON>", "occupied": "<PERSON><PERSON><PERSON>", "normal": "Normal", "low": "Lav", "high": "Hø<PERSON>", "unwashed": "Uvasket", "pre_washing": "Forvasking", "washing": "Vasking", "rinsing": "Skylling", "drying": "<PERSON><PERSON><PERSON><PERSON>", "air_purging": "Luftrensing", "anti_freeze": "Anti-frost", "close": "Lukk", "monitor": "Overvåk", "working": "A<PERSON>eider", "warning": "<PERSON><PERSON><PERSON>", "starting": "Starter", "emptying": "<PERSON><PERSON><PERSON>", "resetting": "Tilbakestilling", "reverse": "Revers", "full": "Full", "empty": "<PERSON>", "missing": "Manglende", "formatting": "Formatering", "unformatted": "Uformaterte"}}, "time_remaining": {"name": "G<PERSON>nst<PERSON>ende tid"}, "time_remaining_x": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tid {x}"}, "cooking_status": {"name": "Koketilstand", "state": {"wait": "Venter på matlaging", "reservation": "Reservert", "cooking": "<PERSON><PERSON><PERSON> p<PERSON>gå<PERSON>", "cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON>", "pause": "Matlaging pause"}}, "water_level": {"name": "Vannivå", "state": {"full": "Full", "high": "Hø<PERSON>", "medium": "Medium", "low": "Lav", "empty": "<PERSON>"}}, "energy_produced": {"name": "<PERSON><PERSON><PERSON> pro<PERSON>"}, "energy_consumed": {"name": "<PERSON><PERSON><PERSON>"}, "energy_produced_x": {"name": "<PERSON>ergi produsert {x}"}, "energy_consumed_x": {"name": "<PERSON><PERSON><PERSON> forbrukt {x}"}, "current_x": {"name": "Strøm {x}"}, "voltage_x": {"name": "Spenning {x}"}, "power_x": {"name": "Effekt {x}"}}, "switch": {"anti_frost": {"name": "Anti-frost"}, "evaporator_cleaning": {"name": "Rengjøring av fordamper"}, "ionizer": {"name": "Ionisator"}, "keytone": {"name": "Tastaturtone"}, "outlet_x": {"name": "Uttak {x}"}, "sleep": {"name": "<PERSON><PERSON>vn<PERSON><PERSON>"}, "switch_x": {"name": "<PERSON><PERSON><PERSON> {x}"}, "electrolytic_sterilization": {"name": "Elektrolytisk sterilisering"}, "uv_sterilization": {"name": "UV-sterilisering"}}, "text": {"scene": {"name": "Scene"}}, "time": {"timer": {"name": "Timer"}, "timer_x": {"name": "Timer {x}"}}, "valve": {"valve_x": {"name": "Ventil {x}"}}, "water_heater": {"water_air": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "kettle": {"name": "<PERSON><PERSON><PERSON>"}}}}