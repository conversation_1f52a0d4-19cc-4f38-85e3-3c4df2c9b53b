{"title": "Tuya Local", "config": {"step": {"user": {"title": "Konfigurieren Sie Ihr Tuya Local-Gerät", "description": "Geräte können entweder manuell oder cloudgestützt mit Hilfe der SmartLife App hinzugefügt werden.", "data": {"setup_mode": "Setup-Auswahl:"}}, "cloud": {"title": "<PERSON>den Si<PERSON> sich bei Tuya an", "description": "Geben Sie Ihren SmartLife oder Tuya Benutzercode ein.\n\nSie finden diesen Code in der SmartLife App oder Tuya App im Bildschirm **Einstellungen** > **Konto und Sicherheit** und geben Sie den dort angezeigten Code ein ", "data": {"user_code": "Benutzercode:"}}, "scan": {"title": "Schließen Sie die Anmeldung ab", "description": "Verwenden Sie die SmartLife App oder die Tuya App"}, "choose_device": {"title": "Wählen Sie das hinzuzufügende Gerät aus", "description": "Bitte wählen Sie das hinzuzufügende Gerät aus der ersten Dropdown-Liste aus. ", "data": {"device_id": "Gerät auswählen:", "hub_id": "Gateway wählen:"}}, "search": {"title": "Suchen Sie die IP-Adresse des Geräts", "description": "Die Tuya-Cloud stellt keine lokalen IP-Adressen bereit"}, "local": {"title": "Konfigurieren Sie Ihr Tuya Local-Gerät", "description": "[Folgen Sie diesen Anweisungen, um Ihre Geräte-ID und Ihren lokalen Schlüssel zu finden.](https://github.com/make-all/tuya-local#finding-your-device-id-and-local-key)", "data": {"host": "IP-Adresse oder Host-Name", "device_id": "Geräte-ID (device_id, oder device_id des Gateways für Geräte, die über ein Gateway angeschlossen sind)", "local_key": "Lokaler Schlüssel", "protocol_version": "Protokollversion (versuchen Sie auto, falls nicht bekannt)", "poll_only": "Nur Abfrage (versuchen Sie dies, wenn Ihr Gerät nicht vollständig funktioniert)", "device_cid": "Sub Geräte node_id oder uuid (für Geräte, die über ein Gateway angeschlossen sind)"}}, "select_type": {"title": "Wählen Sie den Gerätetyp aus", "description": "Wählen Sie den für Ihr Gerät geeigneten Typ aus.", "data": {"type": "Gerätetyp"}}, "choose_entities": {"title": "G<PERSON><PERSON><PERSON><PERSON>", "description": "Wählen Sie einen Namen für dieses Gerät aus.", "data": {"name": "G<PERSON><PERSON><PERSON><PERSON>"}}}, "abort": {"already_configured": "Es wurde bereits ein Gerät mit dieser ID hinzugefügt.", "not_supported": "Dieses Gerät wird leider nicht unterstützt.", "no_devices": "<PERSON><PERSON><PERSON> das Konto konnten keine nicht registrierten Geräte gefunden werden."}, "error": {"connection": "Mit diesen Angaben kann keine Verbindung zu Ihrem Gerät hergestellt werden. Es könnte sich um ein intermittierendes Problem handeln, oder die <PERSON>aben sind falsch.", "does_not_need_hub": "Das Gerät benötigt kein Gateway und es wurde eines ausgewählt. ", "needs_hub": "Das Gerät benötigt ein Gateway und es wurde keines ausgewählt."}}, "selector": {"setup_mode": {"options": {"cloud": "Cloud-gestützte Geräteeinrichtung von SmartLife.", "manual": "Geben Sie manuell Informationen zur Geräteverbindung an.", "cloud_fresh_login": "Cloud-gestützte Geräteeinrichtung von SmartLife mit einem frischen Login."}}}, "options": {"step": {"user": {"title": "Konfigurieren Sie Ihr Tuya Local Gerät", "description": "[Folgen Si<PERSON> diesen Anweisungen, um Ihren lokalen Schlüssel zu finden.](https://github.com/make-all/tuya-local#finding-your-device-id-and-local-key)", "data": {"host": "IP-Adresse oder Host-Name", "local_key": "Lokaler Schlüssel", "protocol_version": "Protokollversion (versuchen Sie auto, falls nicht bekannt)", "poll_only": "Nur Abfrage (versuchen Sie dies, wenn Ihr Gerät nicht vollständig funktioniert)"}}}, "error": {"connection": "Mit diesen Angaben kann keine Verbindung zu Ihrem Gerät hergestellt werden. Es könnte sich um ein intermittierendes Problem handeln, oder die <PERSON>aben sind falsch."}, "abort": {"not_supported": "Dieses Gerät wird leider nicht unterstützt."}}, "entity": {"binary_sensor": {"direction": {"name": "<PERSON><PERSON><PERSON>", "state": {"off": "In", "on": "Out"}}, "defrost": {"name": "<PERSON>f<PERSON>t", "state": {"off": "Normal", "on": "Defrosting"}}, "tank_empty": {"name": "Tank", "state": {"off": "OK", "on": "<PERSON><PERSON><PERSON>"}}, "tank_full": {"name": "Tank", "state": {"off": "OK", "on": "Voll"}}, "wake": {"name": "Wake", "state": {"off": "Schlafend", "on": "<PERSON>ach"}}, "casdon_oven_fault": {"state_attributes": {"description": {"state": {"e1": "E1: Kammer Überhitzungsschutz (über 290°C)", "e2": "E2: Verdunstungsschale Überhitzungsschutz (über 200°C)", "e3": "E3: <PERSON><PERSON> Ni<PERSON>mperaturschutz (unter 35°C)", "e4": "E4: Verdunstungsschale Niedertemperaturschutz (unter 35°C)", "e5": "E5: Sensorverbindungsfehler (offener Stromkreis)", "e6": "E6: E6: Sensorfehlfunktion (Kurzschluss)", "e7": "E7: Anzeigetafel Kommunikationsfehler", "e8": "E8: Wassertank offen (CN7) oder Verdunstungsschale trocken (CN3)"}}}}}, "button": {"factory_reset": {"name": "Werkseinstellungen"}, "filter_reset": {"name": "<PERSON><PERSON>"}}, "climate": {"aircon_extra": {"name": "<PERSON><PERSON><PERSON>", "state_attributes": {"fan_mode": {"state": {"health": "Gesund", "medhigh": "Mittel-hoch", "medlow": "Mittel-ni<PERSON><PERSON>", "natural": "<PERSON><PERSON><PERSON><PERSON>", "quiet": "<PERSON><PERSON>", "sleep": "Schlafen", "strong": "<PERSON>"}}, "swing_mode": {"state": {"topmost": "Ganz oben", "top": "<PERSON><PERSON>", "middle": "<PERSON><PERSON>", "down": "Unten", "downmost": "Ganz unten"}}}}, "thermostat": {"name": "Thermostat", "state_attributes": {"fan_mode": {"state": {"cycle": "Z<PERSON><PERSON>"}}, "preset_mode": {"state": {"manual": "<PERSON><PERSON>", "program": "Programm", "temp_override": "Temporäre Außerkraftsetzung", "perm_override": "Permanente Außerkraftsetzung"}}}}, "combo_floor": {"state_attributes": {"preset_mode": {"state": {"cool": "Luft kühlen", "fan_only": "Ventilator", "heat": "Luft erwärmen", "floor_cool": "Boden kühlen", "floor_heat": "Boden heizen"}}}}, "swing_as_powerlevel": {"name": "Heizung", "state_attributes": {"swing_mode": {"name": "Heizstufe", "state": {"stop": "Stop", "auto": "Auto"}}}}, "pool_heatpump": {"state_attributes": {"preset_mode": {"state": {"smart_heat": "Intelligent heizen", "quick_heat": "<PERSON><PERSON><PERSON> heizen", "quiet_heat": "<PERSON><PERSON> heizen", "smart_cool": "Intelligent kühlen", "quick_cool": "<PERSON><PERSON><PERSON>", "quiet_cool": "<PERSON><PERSON>", "auto": "Auto", "smart": "Intelligent", "quick": "<PERSON><PERSON><PERSON>", "quiet": "<PERSON><PERSON>"}}}}, "heater": {"name": "Heizung"}, "thermo_switch": {"state_attributes": {"hvac_mode": {"state": {"fan_only": "Pause"}}}}, "oven": {"state_attributes": {"preset_mode": {"state": {"healthy_steam": "Gesunder Dampf", "fresh_steam": "Frischer Dampf", "high_temp_steam": "Hohe Temperatur Dampf", "stew": "<PERSON><PERSON><PERSON><PERSON>", "bake_up_and_down": "<PERSON>en oben und unten", "bbq": "BBQ", "bottom_hot_air": "Unten Heißluft", "on_strong_roast": "<PERSON><PERSON>", "3d_hot_air": "3D Heißluft", "air_fry": "Air Fry", "steam_frying": "Dampffr", "one_click_bread": "Ein-Klick-<PERSON>rot", "quick_heat": "<PERSON><PERSON><PERSON> heizen", "keep_warm": "Warmhalten", "unfreeze": "Auftauen", "fermentation": "Gärung", "descale": "Entkalken", "local_recipes": "Lokale Rezepte", "drying": "Trocknen", "custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "low_steaming": "<PERSON><PERSON><PERSON><PERSON>", "medium_steaming": "Mi<PERSON>res Dämpfen", "high_steaming": "Hohes Dämpfen"}}}}}, "humidifier": {"dehumidifier": {"state_attributes": {"mode": {"state": {"laundry": "<PERSON>ä<PERSON> trocknen", "purify": "Reinigen"}}}}}, "fan": {"aroma_diffuser": {"name": "Aroma Diffuser", "state_attributes": {"preset_mode": {"state": {"low": "<PERSON><PERSON><PERSON>", "medium": "<PERSON><PERSON><PERSON>", "high": "Hoch", "continuous": "Dauerbetrieb", "intermittent": "Intervall", "timer": "Timer"}}}}, "dehumidifier": {"name": "Entfeuchter", "state_attributes": {"preset_mode": {"state": {"purify": "Reinigen", "dehumidify": "Entfeuchten"}}}}, "fan_with_presets": {"name": "Ventilator", "state_attributes": {"preset_mode": {"state": {"normal": "Normal", "nature": "<PERSON><PERSON><PERSON><PERSON>", "sleep": "Schlafen", "baby": "Baby", "fresh": "<PERSON><PERSON>", "smart": "Intelligent", "strong": "<PERSON>", "custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "high": "Hoch", "medium": "<PERSON><PERSON><PERSON>", "low": "<PERSON><PERSON><PERSON>", "displayoff": "Anzeige aus", "off": "Aus"}}}}, "ventilation": {"name": "Lüftung", "state_attributes": {"preset_mode": {"state": {"fresh": "Frischluft", "circulate": "Zirkulieren", "sleep": "Schlafen", "auto": "Auto", "eco": "Eco", "anti-condensation": "Anti-Kondensation", "extractor": "Abluft", "heat_recovery": "Wärmerückgewinnung", "timer": "Timer", "on": "An", "off": "Aus"}}}}}, "light": {"backlight": {"name": "Hintergrundbeleuchtung"}, "display": {"name": "Anzeige"}, "embers": {"name": "Glut"}, "flame": {"name": "<PERSON>lamme"}, "indicator": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "laser": {"name": "Laser"}, "logs": {"name": "Holzscheite"}, "nightlight": {"name": "<PERSON><PERSON><PERSON><PERSON>"}}, "lock": {"child_lock": {"name": "Kindersicherung"}}, "number": {"timer": {"name": "Timer"}, "timer_x": {"name": "Timer {x}"}}, "select": {"currency": {"name": "Währung", "state": {"usd": "USD", "eur": "EUR", "cny": "CNY", "cad": "CAD", "gbp": "GBP"}}, "heat_pump_mode": {"name": "<PERSON><PERSON><PERSON>epumpenmo<PERSON>", "state": {"heat": "Heizen", "cool": "<PERSON><PERSON><PERSON><PERSON>", "auto": "Auto", "floor_heat": "Bodenheizung", "off": "Aus", "hotwater": "Warmwasser", "hotwater_cool": "Warmwasser Kühlen", "hotwater_heat": "Warmwasser Heizen", "hotwater_auto": "Warmwasser Auto", "hotwater_floor_heat": "Warmwasser Bodenheizung"}}, "initial_state": {"name": "Startzustand", "state": {"off": "Aus", "on": "An", "memory": "<PERSON><PERSON><PERSON><PERSON>"}}, "kettle_mode": {"name": "Kessel<PERSON><PERSON>", "state": {"off": "Aus", "heat": "Heizen", "boil": "<PERSON><PERSON>", "quick_heat": "Schnellheizen", "quick_boil": "Schnellkochen", "keep_warm": "Warmhalten", "custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dechlorinate": "Entchlorieren", "black_tea": "<PERSON><PERSON><PERSON><PERSON> Tee", "green_tea": "<PERSON><PERSON><PERSON><PERSON>", "coffee": "<PERSON><PERSON><PERSON>", "honey_water": "<PERSON><PERSON><PERSON><PERSON>", "infant_formula": "<PERSON><PERSON><PERSON>", "white_tea": "<PERSON><PERSON><PERSON>", "oolong_tea": "Oolong-Tee"}}, "language": {"name": "<PERSON><PERSON><PERSON>", "state": {"chinese": "中文", "chinese_traditional": "中文(繁體)", "english": "English", "french": "Français", "german": "De<PERSON>ch", "italian": "Italiano", "japanese": "日本語", "korean": "한국어", "latin": "Lingua Latina", "portuguese": "Português", "russian": "Русский", "spanish": "Español", "turkish": "Türkçe"}}, "light_mode": {"name": "Lichtmodus", "state": {"off": "Aus", "on": "An", "state": "Zustand", "locator": "Locator"}}, "mopping": {"name": "Wischen", "state": {"off": "Aus", "auto": "Automatisch", "low": "<PERSON><PERSON><PERSON>", "medium": "<PERSON><PERSON><PERSON>", "high": "Hoch"}}, "recipe": {"name": "Rezept", "state": {"pizza": "Pizza", "fries": "<PERSON><PERSON><PERSON>", "chicken": "<PERSON><PERSON><PERSON><PERSON>", "shrimp": "Garnelen", "fish": "<PERSON><PERSON>", "chicken_drumsticks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vegetables": "<PERSON><PERSON><PERSON><PERSON>", "desserts": "Desserts", "none": "<PERSON><PERSON>", "chicken_wings": "Hähnchenflügel", "steak": "Steak", "onion_rings": "Z<PERSON><PERSON><PERSON>ring<PERSON>", "bacon": "Speck", "cake": "<PERSON><PERSON>", "bread": "<PERSON><PERSON>", "toast": "Toast", "sausage": "<PERSON><PERSON>", "dry_fruit": "Trockenfrüchte", "custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cloud_recipe": "Cloud-Rezept", "default": "Standard", "keep_warm": "Warmhalten", "preheat": "Vorheizen"}}, "scene": {"name": "Szene", "state": {"relax": "Entspannen", "movie": "Film", "party": "Party", "romantic": "<PERSON><PERSON><PERSON>", "night": "<PERSON><PERSON>", "morning": "<PERSON><PERSON>", "working": "Arbeiten", "leisure": "Freizeit", "vacation": "<PERSON><PERSON><PERSON><PERSON>", "reading": "<PERSON><PERSON>", "twinkle": "Blinken", "gaming": "<PERSON><PERSON><PERSON>", "none": "<PERSON><PERSON>"}}, "timer": {"name": "Timer", "state": {"cancel": "Abbrechen", "continuous": "Dauerbetrieb", "30s": "30 Sekunden", "1m": "1 Minute", "2m": "2 Minuten", "5m": "5 Minuten", "10m": "10 Minuten", "20m": "20 Minuten", "30m": "30 Minuten", "40m": "40 Minuten", "1h": "1 Stunde", "1h30m": "1 Stunde 30 Minuten", "2h": "2 Stunden", "2h30m": "2 Stunden 30 Minuten", "3h": "3 Stunden", "3h30m": "3 Stunden 30 Minuten", "4h": "4 Stunden", "4h30m": "4 Stunden 30 Minuten", "5h": "5 Stunden", "5h30m": "5 Stunden 30 Minuten", "6h": "6 Stunden", "6h30m": "6 Stunden 30 Minuten", "7h": "7 Stunden", "7h30m": "7 Stunden 30 Minuten", "8h": "8 Stunden", "8h30m": "8 Stunden 30 Minuten", "9h": "9 Stunden", "9h30m": "9 Stunden 30 Minuten", "10h": "10 Stunden", "11h": "11 Stunden", "12h": "12 Stunden", "13h": "13 Stunden", "14h": "14 Stunden", "15h": "15 Stunden", "16h": "16 Stunden", "17h": "17 Stunden", "18h": "18 Stunden", "19h": "19 Stunden", "20h": "20 Stunden", "21h": "21 Stunden", "22h": "22 Stunden", "23h": "23 Stunden", "24h": "24 Stunden", "36h": "36 Stunden", "48h": "48 Stunden", "72h": "72 Stunden", "1d": "1 Tag", "2d": "2 Tage", "3d": "3 Tage", "4d": "4 Tage", "5d": "5 Tage", "6d": "6 Tage", "7d": "7 Tage"}}, "temperature_unit": {"name": "Temperatur-Einheit", "state": {"celsius": "<PERSON><PERSON><PERSON>", "fahrenheit": "Fahrenheit"}}, "oven_built_in_recipe": {"name": "Eingebautes Rezept", "state": {"none": "<PERSON><PERSON>", "steamed_egg_with_okra": "Gedämpftes Ei mit Okra", "steamed_sea_bass": "Gedämpfter Wolfsbarsch", "steamed_prawns": "Gedämpfte Garnelen", "handmade_steamed_bread": "Handgemachtes gedämpftes Brot", "fan_steamed_baby_vegetables": "Ventilator gedämpftes Babygemüse", "braised_pork": "Geschmortes Schweinefleisch", "snow_fungus_and_bird_s_nest": "Schneepilz und Vogelnest", "crab_pot": "Krabbenpfanne", "potato_ribs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "coconut_chicken_soup": "Kokosnusshühnersuppe", "snack_platter": "Snackplatte", "chicken_skewers": "Hähnchenspieße", "roasted_pork_knuckle": "Gebratene Schweinshaxe", "dried_lemon": "Getrocknete Zitrone", "pork_jerky": "Schweinefleischjerky", "crispy_hairtail": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spicy_grilled_fish": "Scharf gegrillter Fisch", "roasted_sweet_potatoes": "Gebratene Süßkartoffeln", "roasted_chicken_wings": "Gebratene Hähnchenflügel", "cumin_lamb_chops": "Kreuzkümmel-Lammkoteletts", "honey_grilled_chicken": "<PERSON><PERSON> gegrilltes Hähnchen", "garlic_eggplant": "Knoblauch-Aubergine", "portuguese_egg_tart": "Portugiesische Eier-Ta", "creme_brulee": "<PERSON><PERSON><PERSON>", "cocoa_chips": "Ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "butter_cookies": "Butterkekse", "chiffon_cake": "Chiffonkuchen", "puff_pastry": "Blätterteig", "red_bean_bread": "Rote-Bohnen-Brot", "milk_toast": "Milchtoast"}}}, "sensor": {"air_quality": {"name": "Luftqualität", "state": {"excellent": "Ausgezeichnet", "good": "Gut", "moderate": "<PERSON><PERSON><PERSON><PERSON>", "poor": "<PERSON><PERSON><PERSON><PERSON>", "severe": "Schwer"}}, "status": {"name": "Status", "state": {"available": "Verfügbar", "plugged_in": "Angeschlossen", "fault_unplugged": "<PERSON><PERSON> (nicht angeschlossen)", "charging": "Laden", "discharging": "Entladen", "waiting": "<PERSON><PERSON>", "charged": "Geladen", "fault": "<PERSON><PERSON>", "paused": "<PERSON><PERSON><PERSON><PERSON>", "waiting_for_authorization": "Warten auf Autorisierung", "standby": "Standby", "heating": "Aufheizen", "cooling": "Abkühlen", "keeping_warm": "Warmhalten", "no_water": "<PERSON><PERSON>", "boiling": "<PERSON><PERSON>", "reserve_only": "Reserve nur", "unknown": "Unbekannt", "idle": "<PERSON><PERSON><PERSON><PERSON>", "auto": "Auto", "manual": "<PERSON><PERSON>", "rain_delay": "Regenverzögerung", "off": "Aus", "on": "An", "cooking": "<PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON>", "door_open": "<PERSON><PERSON><PERSON> offen", "setting": "Einstellung", "pre_heating": "Vorheizen", "scheduled": "<PERSON><PERSON><PERSON>", "at_temperature": "Temperatur erreicht", "done_stage_1": "Fertig Stufe 1", "done_stage_2": "Fertig Stufe 2", "done_stage_3": "Fertig Stufe 3", "done_stage_4": "Fertig Stufe 4", "done_stage_5": "Fertig Stufe 5", "done_stage_6": "<PERSON>rtig <PERSON>uf<PERSON> 6", "done_stage_7": "Fertig Stufe 7", "done_stage_8": "Fertig Stufe 8", "done_stage_9": "Fertig Stufe 9", "done_stage_10": "Fertig Stufe 10", "no_food": "<PERSON><PERSON>", "jammed": "Verstopft", "blocked": "<PERSON><PERSON><PERSON>", "feeding": "<PERSON><PERSON><PERSON><PERSON>", "feeding_complete": "Füttern abgeschlossen", "caking": "<PERSON><PERSON>", "cleaning": "Reinigung", "sleep": "Schlafen", "sterilizing": "Sterilisieren", "deodorizing": "Geruchsbeseitigung", "occupied": "Besetzt", "normal": "Normal", "low": "<PERSON><PERSON><PERSON>", "high": "Hoch", "unwashed": "Ungewaschen", "pre_washing": "Vorwäsche", "pre_rinsing": "Vorspülen", "washing": "<PERSON><PERSON>", "rinsing": "Spü<PERSON>", "drying": "Trocknen", "air_purging": "Luftreinigung", "anti_freeze": "Anti-Frost", "close": "Schließen", "monitor": "Überwachen", "working": "Arbeiten", "warning": "<PERSON><PERSON><PERSON>", "starting": "Starten", "emptying": "<PERSON><PERSON>", "resetting": "Z<PERSON>ücksetzen", "reverse": "Rück<PERSON><PERSON><PERSON>", "full": "Voll", "empty": "<PERSON><PERSON>", "missing": "<PERSON><PERSON><PERSON>", "formatting": "Formatieren", "unformatted": "Unformatiert"}}, "time_remaining": {"name": "Verbleibende Zeit"}, "time_remaining_x": {"name": "Verbleibende Zeit {x}"}, "cooking_status": {"name": "<PERSON><PERSON><PERSON>", "state": {"wait": "<PERSON><PERSON> auf Koch<PERSON>", "reservation": "In Reservation", "cooking": "<PERSON><PERSON>", "cancel": "Kochen abgebrochen", "done": "Kochen abgeschlossen", "pause": "<PERSON><PERSON> paus<PERSON>t"}}, "water_level": {"name": "Wasserstand", "state": {"empty": "<PERSON><PERSON>", "low": "<PERSON><PERSON><PERSON>", "medium": "<PERSON><PERSON><PERSON>", "high": "Hoch", "full": "Voll"}}, "energy_produced": {"name": "Produzierte Energie"}, "energy_consumed": {"name": "Verbrauchte Energie"}, "energy_produced_x": {"name": "Produzierte Energie {x}"}, "energy_consumed_x": {"name": "Verbrauchte Energie {x}"}, "current_x": {"name": "Strom {x}"}, "voltage_x": {"name": "Spannung {x}"}, "power_x": {"name": "Le<PERSON>ung {x}"}}, "switch": {"anti_frost": {"name": "Anti-Frost"}, "evaporator_cleaning": {"name": "Verdampferreinigung"}, "ionizer": {"name": "Ionisator"}, "keytone": {"name": "<PERSON><PERSON>"}, "outlet_x": {"name": "Steckdose {x}"}, "sleep": {"name": "Schlafen"}, "switch_x": {"name": "<PERSON><PERSON><PERSON> {x}"}, "electrolytic_sterilization": {"name": "Elektrolytische Sterilisation"}, "uv_sterilization": {"name": "UV Sterilization"}}, "text": {"scene": {"name": "Szene"}}, "time": {"timer": {"name": "Timer"}, "timer_x": {"name": "Timer {x}"}}, "valve": {"valve_x": {"name": "Ventil {x}"}}, "water_heater": {"water_air": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "kettle": {"name": "<PERSON><PERSON><PERSON><PERSON>"}}}}