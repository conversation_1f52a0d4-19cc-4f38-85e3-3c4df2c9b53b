{"title": "Tuya Local", "config": {"step": {"user": {"title": "Налаштуйте свій пристрій Tuya Local", "description": "Пристрої можна додавати вручну або за допомогою хмари за допомогою програми SmartLife.", "data": {"setup_mode": "Вибір налаштування:"}}, "cloud": {"title": "Увійти в Tuya", "description": "Введіть свій код користувача SmartLife або Tuya.\n\nЦей код можна знайти в програмі SmartLife або Tuya у меню **Налаштування** > екран **Обліковий запис і безпека** та введіть код", "data": {"user_code": "Код користувача:"}}, "scan": {"title": "Зав<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> вхід", "description": "Скористайтеся додатком SmartLife або Tuya"}, "choose_device": {"title": "Виберіть пристрій для додавання", "description": "Будь ласка", "data": {"device_id": "Виберіть пристрій:", "hub_id": "Виберіть шлюз:"}}, "search": {"title": "Знайдіть IP-адресу пристрою", "description": "Х<PERSON><PERSON><PERSON><PERSON> Tuya не надає локальних IP-адрес"}, "local": {"title": "Налаштуйте свій пристрій Tuya Local", "description": "[Дотримуйтеся цих інструкцій, щоб знайти ідентифікатор пристрою та локальний ключ.](https://github.com/make-all/tuya-local#finding-your-device-id-and-local-key)", "data": {"host": "IP-адреса або ім'я хоста", "or device_id of gateway for devices connected via gateway": "", "device_id": "Ідентифікатор пристрою (device_id, або device_id шлюзу для пристроїв, підключених через шлюз)", "local_key": "Локальний ключ (local_key)", "protocol_version": "Версія протоколу (якщо не знаєте, спробуйте 'auto')", "poll_only": "Виключно опитування (спробуйте це, якщо ваш пристрій не зовсім працює)", "device_cid": "Sub device node_id або uuid (для пристроїв, підключених через шлюз)"}}, "select_type": {"title": "Оберіть тип пристрою", "description": "Виберіть тип, що відповідає вашому пристрою", "data": {"type": "Тип пристрою"}}, "choose_entities": {"title": "Налаштування пристрою", "description": "Оберіть назву для цього пристрою", "data": {"name": "Назва"}}}, "abort": {"already_configured": "Пристрій з таким ідентифікатором уже додано.", "not_supported": "На жаль, цей пристрій не підтримується.", "no_devices": "Не вдалося знайти незареєстровані пристрої для облікового запису."}, "error": {"connection": "Неможливо підключитися до пристрою з вказаними налаштуваннями. Це може бути випадковий збій або, можливо, в налаштуваннях є помилки.", "does_not_need_hub": "Пристрою не потрібен шлюз", "needs_hub": "Для пристрою потрібен шлюз"}}, "selector": {"setup_mode": {"options": {"cloud": "Хмарне налаштування пристрою SmartLife.", "manual": "Вручну надайте інформацію про підключення пристрою.", "cloud_fresh_login": "Хмарне налаштування пристрою SmartLife з новим входом."}}}, "options": {"step": {"user": {"title": "Налаштуйте свій пристрій Tuya Local", "description": "[Дотримуйтеся цих інструкцій, щоб знайти локальний ключ.](https://github.com/make-all/tuya-local#finding-your-device-id-and-local-key)", "data": {"host": "IP-адреса або ім'я хоста", "local_key": "Локальний ключ (local_key)", "protocol_version": "Версія протоколу (якщо не знаєте, спробуйте 'auto')", "poll_only": "Виключно опитування (спробуйте це, якщо ваш пристрій не зовсім працює)"}}}, "error": {"connection": "Неможливо підключитися до пристрою з вказаними налаштуваннями. Це може бути випадковий збій або, можливо, в налаштуваннях є помилки."}, "abort": {"not_supported": "На жаль, цей пристрій не підтримується."}}, "entity": {"binary_sensor": {"direction": {"name": "Напрямок", "state": {"off": "Вхідний", "on": "Вихідний"}}, "defrost": {"name": "Розморожування", "state": {"off": "Нормально", "on": "Розморожування"}}, "tank_empty": {"name": "<PERSON>а<PERSON><PERSON>", "state": {"off": "OK", "on": "Низький"}}, "tank_full": {"name": "<PERSON>а<PERSON><PERSON>", "state": {"off": "OK", "on": "Повний"}}, "wake": {"name": "Пробудження", "state": {"off": "Сон", "on": "Будильник"}}, "casdon_oven_fault": {"state_attributes": {"description": {"state": {"e1": "E1: За<PERSON><PERSON><PERSON>т від перегріву камери (перевищує 290°C)", "e2": "E2: За<PERSON><PERSON><PERSON>т від перегріву випарника (перевищує 200°C)", "e3": "E3: <PERSON>а<PERSON><PERSON><PERSON>т від низької температури камери (нижче 35°C)", "e4": "E4: <PERSON><PERSON><PERSON><PERSON><PERSON>т від низької температури випарника (нижче 35°C)", "e5": "E5: Помилка підключення датчика (відкритий коло)", "e6": "E6: Помилка датчика (коротке замикання)", "e7": "E7: Помилка зв'язку з платою дисплею", "e8": "E8: Відкритий бак з водою (CN7) або суха піддон для випарника (CN3)"}}}}}, "button": {"factory_reset": {"name": "Скидання до заводських налаштувань"}, "filter_reset": {"name": "Скидання фільтра"}}, "climate": {"aircon_extra": {"name": "Кондиціон<PERSON>р", "state_attributes": {"fan_mode": {"state": {"health": "Здоровий", "medhigh": "Середньо-високий", "medlow": "Середньо-низький", "natural": "Природні", "quiet": "Спокійно", "sleep": "Сон", "strong": "Сильний"}}, "swing_mode": {"state": {"topmost": "<PERSON>а<PERSON><PERSON> верхній", "top": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": "Середній", "down": "Низький", "downmost": "Самий низький"}}}}, "thermostat": {"name": "Термостат", "state_attributes": {"fan_mode": {"state": {"cycle": "<PERSON>и<PERSON><PERSON>"}}, "preset_mode": {"state": {"manual": "Інструкція", "program": "Програма", "temp_override": "Тимчасове перевизначення", "perm_override": "Постійне перевизначення"}}}}, "combo_floor": {"state_attributes": {"preset_mode": {"state": {"cool": "Прохолодне повітря", "fan_only": "Вентилятор", "heat": "Підігріте повітря", "floor_cool": "Прохолодна підлога", "floor_heat": "Тепла підлога"}}}}, "swing_as_powerlevel": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_attributes": {"swing_mode": {"name": "Рівень нагріву", "state": {"stop": "Стій", "auto": "Авто"}}}}, "pool_heatpump": {"state_attributes": {"preset_mode": {"state": {"smart_heat": "Розумне тепло", "quick_heat": "Швидке тепло", "quiet_heat": "Тихе тепло", "smart_cool": "Розумне холонути", "quick_cool": "Швидке холонути", "quiet_cool": "Тихе холонути", "auto": "Авто", "smart": "Розумний", "quick": "Швидкий", "quiet": "Тих<PERSON>"}}}}, "heater": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "thermo_switch": {"state_attributes": {"hvac_mode": {"state": {"fan_only": "Пауза"}}}}, "oven": {"state_attributes": {"preset_mode": {"state": {"off": "Вимк", "healthy_steam": "Здоровий пар", "fresh_steam": "Свіжий пар", "high_temp_steam": "Висока температура пару", "stew": "Тушення", "bake_up_and_down": "Випікання зверху і знизу", "bbq": "BBQ", "bottom_hot_air": "Нижн<PERSON>й гарячий повітря", "on_strong_roast": "На сильному смаженні", "3d_hot_air": "3D гарячий повітря", "air_fry": "Повітряна фритюрниця", "steam_frying": "Парове смаження", "one_click_bread": "<PERSON><PERSON><PERSON><PERSON> клік хліб", "quick_heat": "Швидке нагрівання", "keep_warm": "Підтримувати тепло", "unfreeze": "Розморожування", "fermentation": "Ферментація", "descale": "Відкальцювання", "local_recipes": "Місцеві рецепти", "drying": "Сушіння", "custom": "Користувацький", "low_steaming": "Низьке парування", "medium_steaming": "Середнє парування", "high_steaming": "Високе парування"}}}}}, "humidifier": {"dehumidifier": {"state_attributes": {"mode": {"state": {"laundry": "Висушити білизну", "purify": "Очистити"}}}}}, "fan": {"aroma_diffuser": {"name": "Ароматизатор", "state_attributes": {"preset_mode": {"state": {"low": "Низький", "medium": "Середній", "high": "Високий", "continuous": "Постійний", "intermittent": "Інтермітентний", "timer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}}, "dehumidifier": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_attributes": {"preset_mode": {"state": {"purify": "Очистити", "dehumidify": "Осушити"}}}}, "fan_with_presets": {"name": "Вентилятор", "state_attributes": {"preset_mode": {"state": {"normal": "Нормально", "nature": "Природні", "sleep": "Сон", "baby": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fresh": "Свіжий", "smart": "Розумний", "strong": "Сильний", "custom": "Користувацький", "high": "Високий", "medium": "Середній", "low": "Низький", "displayoff": "Дисплей вимк", "off": "Вимк"}}}}, "ventilation": {"name": "Вентиляція", "state_attributes": {"preset_mode": {"state": {"fresh": "Свіже повітря", "circulate": "Циркулювати", "sleep": "Сон", "auto": "Авто", "eco": "Еко", "anti-condensation": "Антиконденсат", "extractor": "Екстрактор", "heat_recovery": "Відновлення тепла", "timer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "on": "Увімк", "off": "Вимк"}}}}}, "light": {"backlight": {"name": "Підсвічування"}, "display": {"name": "Дис<PERSON><PERSON>ей"}, "embers": {"name": "Вуглинки"}, "flame": {"name": "Полум'я"}, "indicator": {"name": "Індикатор"}, "laser": {"name": "Лазерна"}, "logs": {"name": "Колода"}, "nightlight": {"name": "Нічне світло"}}, "lock": {"child_lock": {"name": "Блокування від дітей"}}, "number": {"timer": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "timer_x": {"name": "Та<PERSON>мер {x}"}}, "select": {"currency": {"name": "Валюта", "state": {"usd": "USD", "eur": "EUR", "cny": "CNY", "cad": "CAD", "gbp": "GBP"}}, "heat_pump_mode": {"name": "Режим теплового насоса", "state": {"heat": "Нагрівання", "cool": "Охолодження", "auto": "Авто", "floor_heat": "Тепла підлога", "off": "Вимк", "hotwater": "Гаряча вода", "hotwater_cool": "Гаряча вода та охолодження", "hotwater_heat": "Гаряча вода та нагрівання", "hotwater_auto": "Гаряча вода та авто", "hotwater_floor_heat": "Гаряча вода та тепла підлога"}}, "initial_state": {"name": "Початковий стан", "state": {"off": "Вимк", "on": "Увімк", "memory": "Пам'ять"}}, "kettle_mode": {"name": "Режим чайника", "state": {"off": "Вимк", "heat": "Нагрівання", "boil": "Ки<PERSON>'ятіння", "quick_heat": "Швидке нагрівання", "quick_boil": "Швидке кип'ятіння", "keep_warm": "Підтримувати тепло", "custom": "Користувацький", "dechlorinate": "Дехлорування", "black_tea": "<PERSON>о<PERSON><PERSON><PERSON> чай", "green_tea": "Зелений чай", "coffee": "Кава", "honey_water": "Медова вода", "infant_formula": "Дитяча суміш", "white_tea": "<PERSON><PERSON><PERSON><PERSON> чай", "oolong_tea": "Улун"}}, "language": {"name": "Мова", "state": {"chinese": "中文", "chinese_traditional": "中文(繁體)", "english": "English", "french": "Français", "german": "De<PERSON>ch", "italian": "Italiano", "japanese": "日本語", "korean": "한국어", "latin": "Lingua Latina", "portuguese": "Português", "russian": "Русский", "spanish": "Español", "turkish": "Türkçe"}}, "light_mode": {"name": "Режим світла", "state": {"off": "Вимк", "on": "Увімк", "state": "Стан", "locator": "Локатор"}}, "mopping": {"name": "Прибирання", "state": {"off": "Вимк", "auto": "Авто", "low": "Низький", "medium": "Середній", "high": "Високий"}}, "recipe": {"name": "Рецепт", "state": {"pizza": "Піца", "fries": "Картопля фрі", "chicken": "Курка", "shrimp": "Креветки", "fish": "Риба", "chicken_drumsticks": "Курячі гомілки", "vegetables": "Овочі", "desserts": "Десерти", "none": "Нічого", "chicken_wings": "Куряч<PERSON> крильця", "steak": "Стейк", "onion_rings": "Цибуляні кільця", "bacon": "Бек<PERSON>н", "cake": "Торт", "bread": "Х<PERSON><PERSON>б", "toast": "Тост", "sausage": "Ковбаса", "dry_fruit": "Сухофрукти", "custom": "Користувацький", "cloud_recipe": "Хмарний рецепт", "default": "За замовчуванням", "keep_warm": "Підтримувати тепло", "preheat": "Попередній нагрів"}}, "scene": {"name": "Сцена", "state": {"relax": "<PERSON>ел<PERSON><PERSON><PERSON>", "movie": "Фільм", "party": "Вечірка", "romantic": "Романтика", "night": "<PERSON><PERSON><PERSON>", "morning": "Ранок", "working": "Робота", "leisure": "Дозвілля", "vacation": "Відпустка", "reading": "Читання", "twinkle": "Ме<PERSON>ехтіння", "gaming": "Ігри", "none": "Нічого"}}, "timer": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state": {"cancel": "Скасувати", "continuous": "Постійний", "30s": "30 секунд", "1m": "1 хвилина", "2m": "2 хвилини", "5m": "5 хвилин", "10m": "10 хвилин", "20m": "20 хвилин", "30m": "30 хвилин", "40m": "40 хвилин", "1h": "1 година", "1h30m": "1 година 30 хвилин", "2h": "2 години", "2h30m": "2 години 30 хвилин", "3h": "3 години", "3h30m": "3 години 30 хвилин", "4h": "4 години", "4h30m": "4 години 30 хвилин", "5h": "5 годин", "5h30m": "5 годин 30 хвилин", "6h": "6 годин", "6h30m": "6 годин 30 хвилин", "7h": "7 годин", "7h30m": "7 годин 30 хвилин", "8h": "8 годин", "8h30m": "8 годин 30 хвилин", "9h": "9 годин", "9h30m": "9 годин 30 хвилин", "10h": "10 годин", "11h": "11 годин", "12h": "12 годин", "13h": "13 годин", "14h": "14 годин", "15h": "15 годин", "16h": "16 годин", "17h": "17 годин", "18h": "18 годин", "19h": "19 годин", "20h": "20 годин", "21h": "21 годин", "22h": "22 годин", "23h": "23 годин", "24h": "24 години", "36h": "36 годин", "48h": "48 годин", "72h": "72 години", "1d": "1 день", "2d": "2 дні", "3d": "3 дні", "4d": "4 дні", "5d": "5 днів", "6d": "6 днів", "7d": "7 днів"}}, "temperature_unit": {"name": "Температурна одиниця", "state": {"celsius": "Цельсія", "fahrenheit": "Фаренгейт"}}, "oven_built_in_recipe": {"name": "Вбудований рецепт", "state": {"none": "Нічого", "steamed_egg_with_okra": "Парове яйце з окрою", "steamed_sea_bass": "Паровий морський окунь", "steamed_prawns": "Парові креветки", "handmade_steamed_bread": "Ручний паровий хліб", "fan_steamed_baby_vegetables": "Вентилятор парових дитячих овочів", "braised_pork": "Тушкована свинина", "snow_fungus_and_bird_s_nest": "Снігова гриби та гнізда птахів", "crab_pot": "Крабовий горщик", "potato_ribs": "Картопляні ребра", "coconut_chicken_soup": "Кокосовий курячий суп", "snack_platter": "Снек-платтер", "chicken_skewers": "Курячі шашлики", "roasted_pork_knuckle": "Смажена свиняча копита", "dried_lemon": "Сушений лимон", "pork_jerky": "Свинячий джерки", "crispy_hairtail": "Хрустка косатка", "spicy_grilled_fish": "Гостре смажена риба", "roasted_sweet_potatoes": "Смажені солодкі картоплі", "roasted_chicken_wings": "Смажені курячі крильця", "cumin_lamb_chops": "<PERSON>у<PERSON><PERSON>н ламб чопс", "honey_grilled_chicken": "Медовий смажений курча", "garlic_eggplant": "<PERSON>а<PERSON><PERSON><PERSON>к баклажан", "portuguese_egg_tart": "Португальський яєчний тарт", "creme_brulee": "Крем-брюле", "cocoa_chips": "Какао-чіпси", "butter_cookies": "Масляні печиво", "chiffon_cake": "Шифоновий торт", "puff_pastry": "Відкритий пиріг", "red_bean_bread": "Червоний бобовий хліб", "milk_toast": "Молочний тост"}}}, "sensor": {"air_quality": {"name": "Якість повітря", "state": {"excellent": "Відмінно", "good": "Добре", "moderate": "Помірно", "poor": "Погано", "severe": "Важко"}}, "status": {"name": "Статус", "state": {"available": "Доступний", "plugged_in": "Підключений", "fault_unplugged": "Помилка (відключено)", "charging": "Заряджається", "discharging": "Розряджається", "waiting": "Очікування", "charged": "Заряджено", "fault": "Помилка", "paused": "Призупинено", "waiting_for_authorization": "Очікування авторизації", "standby": "Очікування", "heating": "Нагрівання", "cooling": "Охолодження", "keeping_warm": "Тримати тепло", "no_water": "Немає води", "boiling": "Ки<PERSON>'ятіння", "reserve_only": "Тільки резерв", "unknown": "Невідомо", "idle": "Вільний", "auto": "Авто", "manual": "Ручний", "rain_delay": "Затримка дощу", "off": "Вимк", "on": "Увімк", "cooking": "Приготування", "done": "Готово", "door_open": "Двері відкриті", "setting": "Налаштування", "pre_heating": "Попередній нагрів", "scheduled": "Заплановано", "at_temperature": "При температурі", "done_stage_1": "Готово стадія 1", "done_stage_2": "Готово стадія 2", "done_stage_3": "Готово стадія 3", "done_stage_4": "Готово стадія 4", "done_stage_5": "Готово стадія 5", "done_stage_6": "Готово стадія 6", "done_stage_7": "Готово стадія 7", "done_stage_8": "Готово стадія 8", "done_stage_9": "Готово стадія 9", "done_stage_10": "Готово стадія 10", "no_food": "Немає їжі", "jammed": "Заклинило", "blocked": "Заблоковано", "feeding": "Годування", "feeding_complete": "Годування завершено", "caking": "Тортування", "cleaning": "Чищення", "sleep": "Сон", "sterilizing": "Стерилізація", "deodorizing": "Дезодорація", "occupied": "Зайнято", "normal": "Нормально", "low": "Низький", "high": "Високий", "unwashed": "Не вимито", "pre_washing": "Попереднє миття", "washing": "Миття", "rinsing": "Промивання", "drying": "Сушіння", "air_purging": "Очищення повітря", "anti_freeze": "Антизамерзання", "close": "Закрито", "monitor": "Мон<PERSON><PERSON>ор", "working": "Працює", "warning": "Попередження", "starting": "Початок", "emptying": "Опорожнення", "resetting": "Скидання", "reverse": "Реверс", "full": "Повний", "empty": "Порожній", "missing": "Від<PERSON>у<PERSON><PERSON><PERSON>й", "formatting": "Форматування", "unformatted": "Неформатований"}}, "time_remaining": {"name": "Залишився час"}, "time_remaining_x": {"name": "Залишився час {x}"}, "cooking_status": {"name": "Статус", "state": {"wait": "Очікування на приготування", "reservation": "В резерві", "cooking": "В процесі", "cancel": "Скасувати", "done": "Готово", "pause": "Приготування призупинено"}}, "water_level": {"name": "Рівень води", "state": {"full": "Повний", "high": "Високий", "medium": "Середній", "low": "Низький", "empty": "Порожній"}}, "energy_produced": {"name": "Вироблена енергія"}, "energy_consumed": {"name": "Спожита енергія"}, "energy_produced_x": {"name": "Вироблена енергія {x}"}, "energy_consumed_x": {"name": "Спожита енергія {x}"}, "current_x": {"name": "Поточний {x}"}, "voltage_x": {"name": "Напруга {x}"}, "power_x": {"name": "Потужність {x}"}}, "switch": {"anti_frost": {"name": "Протиморозний"}, "evaporator_cleaning": {"name": "Очищення випарника"}, "ionizer": {"name": "Іонізатор"}, "keytone": {"name": "<PERSON>о<PERSON> клавіш"}, "outlet_x": {"name": "Розетка {x}"}, "sleep": {"name": "Сон"}, "switch_x": {"name": "<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON> {x}"}, "electrolytic_sterilization": {"name": "Електролітична стерилізація"}, "uv_sterilization": {"name": "УФ стерилізація"}}, "text": {"scene": {"name": "Сцена"}}, "time": {"timer": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "timer_x": {"name": "Та<PERSON>мер {x}"}}, "valve": {"valve_x": {"name": "<PERSON>ла<PERSON>ан {x}"}}, "water_heater": {"water_air": {"name": "Водонагрів<PERSON>ч"}, "kettle": {"name": "Чай<PERSON><PERSON>к"}}}}