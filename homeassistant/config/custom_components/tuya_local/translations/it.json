{"title": "Tuya Local", "config": {"step": {"user": {"title": "Configura il tuo dispositivo Tuya Local", "description": "I dispositivi possono essere aggiunti manualmente o tramite cloud con l'aiuto dell'app SmartLife.", "data": {"setup_mode": "Scegli configurazione:"}}, "cloud": {"title": "Accedi a Tu<PERSON>", "description": "Inserisci il tuo codice utente SmartLife o Tuya.\n\nPuoi trovare questo codice nell'app SmartLife o nell'app Tuya nella schermata **Impostazioni** > **Account e sicurezza**. Inserisci il codice utente esattamente come mostrato nell'app.", "data": {"user_code": "Codice utente:"}}, "scan": {"title": "Completa il login", "description": "Utilizza l'app SmartLife o l'app Tuya per scansionare il seguente codice QR per autorizzare l'accesso.\n\nProsegui al passo successivo una volta completato."}, "choose_device": {"title": "Scegli il dispositivo da aggiungere", "description": "Scegli il dispositivo da aggiungere tramite il primo menu a tendina. I dispositivi già presenti non vengono mostrati.\n\nSe il dispositivo si connette tramite un gateway, selezionalo dalla lista gateway, altrimenti scegli None", "data": {"device_id": "Seleziona dispositivo:", "hub_id": "Seleziona gateway:"}}, "search": {"title": "Individuazione indirizzo IP del dispositivo", "description": "Tuya cloud non fornisce indirizzi IP locali, quindi ora dobbiamo cercare nella rete locale per trovare il dispositivo. Potrebbero volerci fino a 20 secondi.\n\nQualora dovesse non andare a buon fine, inserisci l'indirizzo IP del dispositivo manualmente.\n\nPer garantire la corretta esecuzione di questo passo e la successiva aggiunta del dispositivo chiudi l'app Tuya."}, "local": {"title": "Configura il tuo dispositivo Tuya Local", "description": "[Segui queste istruzioni per trovare 'device id' e 'local key'.](https://github.com/make-all/tuya-local#finding-your-device-id-and-local-key)", "data": {"host": "Indirizzo IP o hostname", "or device_id of gateway": "oppure ID dispositivo del gateway", "device_id": "Device ID (oppure Device ID dell'hub per dispositivi connessi tramite gateway)", "local_key": "Local key", "protocol_version": "<PERSON>e protocollo (prova 'auto' in caso di dubbi)", "poll_only": "Poll only (prova questa opzione se il dispositivo non funziona correttamente)", "device_cid": "Sub device node_id o uuid (per dispositivi connessi tramite gateway)"}}, "select_type": {"title": "Scegli il tipo di dispositivo", "description": "Seleziona il tipo che corrisponde al tuo dispositivo", "data": {"type": "Tipo dispositivo"}}, "choose_entities": {"title": "Dettagli dispositivo", "description": "Inserisci il nome del dispositivo", "data": {"name": "Nome dispositivo"}}}, "abort": {"already_configured": "E' già presente un dispositivo con l'ID inserito.", "not_supported": "<PERSON><PERSON><PERSON><PERSON>, questo dispositivo non è supportato.", "no_devices": "Impossibile trovare dispositivi non registrati per l'account."}, "error": {"connection": "Impossibile connettersi al dispositivo. Può trattarsi di un problema temporaneo, oppure le informazioni fornite potrebbero non essere corrette.", "does_not_need_hub": "Il dispositivo non necessita di un gateway e ne è stato selezionato uno. ", "needs_hub": "Il dispositivo necessita di un gateway e non ne è stato selezionato nessuno."}}, "selector": {"setup_mode": {"options": {"cloud": "Configurazione del dispositivo SmartLife assistita da cloud.", "manual": "Inserisci manualmente le informazioni sulla connessione del dispositivo.", "cloud_fresh_login": "Configurazione del dispositivo SmartLife assistita da cloud con accesso fresco."}}}, "options": {"step": {"user": {"title": "Configura il tuo dispositivo Tuya Local", "description": "[Segui queste istruzioni per trovare 'device id' e 'local key'.](https://github.com/make-all/tuya-local#finding-your-device-id-and-local-key)", "data": {"host": "Indirizzo IP o hostname", "local_key": "Local key", "protocol_version": "<PERSON>e protocollo (prova 'auto' in caso di dubbi)", "poll_only": "Poll only (prova questa opzione se il dispositivo non funziona correttamente)"}}}, "error": {"connection": "Impossibile connettersi al dispositivo. Può trattarsi di un problema temporaneo, oppure le informazioni fornite potrebbero non essere corrette."}, "abort": {"not_supported": "<PERSON><PERSON><PERSON><PERSON>, questo dispositivo non è supportato."}}, "entity": {"binary_sensor": {"direction": {"name": "Direzione", "state": {"off": "In", "on": "Out"}}, "defrost": {"name": "Scongelare", "state": {"off": "Normale", "on": "Scongelamento"}}, "tank_empty": {"name": "Serbatoio", "state": {"off": "OK", "on": "<PERSON><PERSON><PERSON>"}}, "tank_full": {"name": "Serbatoio", "state": {"off": "OK", "on": "Pieno"}}, "wake": {"name": "Sveglia", "state": {"off": "Spent<PERSON>", "on": "Acceso"}}, "casdon_oven_fault": {"state_attributes": {"description": {"state": {"e1": "E1: Protezione surriscaldamento camera (superiore a 290°C)", "e2": "E2: Protezione surriscaldamento vassoio di evaporazione (superiore a 200°C)", "e3": "E3: Protezione bassa temperatura camera (inferiore a 35°C)", "e4": "E4: Protezione bassa temperatura vassoio di evaporazione (inferiore a 35°C)", "e5": "E5: Errore di connessione del sensore (circuito aperto)", "e6": "E6: Malfunzionamento del sensore (circuito chiuso)", "e7": "E7: Errore di comunicazione della scheda display", "e8": "E8: Serbatoio dell'acqua aperto (CN7) o Vassoio di evaporazione asciutto (CN3)"}}}}}, "button": {"factory_reset": {"name": "Ripristino delle impostazioni di fabbrica"}, "filter_reset": {"name": "Reset del filtro"}}, "climate": {"aircon_extra": {"name": "Climatizzatore", "state_attributes": {"fan_mode": {"state": {"health": "Salutare", "medhigh": "Medio-alta", "medlow": "Medio-bassa", "natural": "Naturale", "quiet": "<PERSON><PERSON><PERSON><PERSON>", "sleep": "Sonno", "strong": "Forte"}}, "swing_mode": {"state": {"topmost": "<PERSON><PERSON>", "top": "Su", "middle": "A metà", "down": "<PERSON><PERSON><PERSON>", "downmost": "<PERSON><PERSON> giù"}}}}, "thermostat": {"name": "Termostato", "state_attributes": {"fan_mode": {"state": {"cycle": "<PERSON><PERSON><PERSON>"}}, "preset_mode": {"state": {"manual": "Manuale", "program": "Programma", "temp_override": "Override temporaneo", "perm_override": "Override permanente"}}}}, "combo_floor": {"state_attributes": {"preset_mode": {"state": {"cool": "Aria fresca", "fan_only": "Solo ventola", "heat": "Aria riscaldata", "floor_cool": "Pavimento fresco", "floor_heat": "<PERSON><PERSON><PERSON> r<PERSON>"}}}}, "swing_as_powerlevel": {"name": "<PERSON><PERSON><PERSON>", "state_attributes": {"swing_mode": {"name": "Livello di riscaldamento", "state": {"stop": "Stop", "auto": "Auto"}}}}, "pool_heatpump": {"state_attributes": {"preset_mode": {"state": {"smart_heat": "Riscaldamanto smart", "quick_heat": "Riscaldamanto rapido", "quiet_heat": "Riscaldamanto silenzioso", "smart_cool": "Raffreddamento smart", "quick_cool": "Raffreddamento rapido", "quiet_cool": "Raffreddamento silenzioso", "auto": "Auto", "smart": "Smart", "quiet": "<PERSON><PERSON><PERSON><PERSON>", "quick": "Veloce"}}}}, "heater": {"name": "<PERSON><PERSON><PERSON>"}, "thermo_switch": {"state_attributes": {"hvac_mode": {"state": {"fan_only": "Pausa"}}}}, "oven": {"state_attributes": {"preset_mode": {"state": {"healthy_steam": "Vapore sano", "fresh_steam": "Vapore fresco", "high_temp_steam": "Vapore ad alta temperatura", "stew": "<PERSON><PERSON><PERSON>", "bake_up_and_down": "<PERSON>uocere sopra e sotto", "bbq": "BBQ", "bottom_hot_air": "Aria calda inferiore", "on_strong_roast": "<PERSON><PERSON><PERSON> forte", "3d_hot_air": "<PERSON> calda <PERSON>", "air_fry": "Friggitrice ad aria", "steam_frying": "Frittura a vapore", "one_click_bread": "Pane in un clic", "quick_heat": "Riscaldamento rapido", "keep_warm": "Mantenimento calore", "unfreeze": "Scongelare", "fermentation": "Fermentazione", "descale": "Decalcificazione", "local_recipes": "Ricette locali", "drying": "Asciugatura", "custom": "<PERSON><PERSON><PERSON><PERSON>", "low_steaming": "<PERSON><PERSON> Steaming", "medium_steaming": "Steaming medio", "high_steaming": "Alto Steaming"}}}}}, "humidifier": {"dehumidifier": {"state_attributes": {"mode": {"state": {"laundry": "Asciuga biancheria", "purify": "Purifica"}}}}}, "fan": {"aroma_diffuser": {"name": "Di<PERSON>usore di a<PERSON>i", "state_attributes": {"preset_mode": {"state": {"low": "<PERSON><PERSON>", "medium": "Medio", "high": "Alto", "continuous": "Continuo", "intermittent": "Intermittente", "timer": "Timer"}}}}, "dehumidifier": {"name": "Deumidificatore", "state_attributes": {"preset_mode": {"state": {"purify": "Purifica", "dehumidify": "Deumidifica"}}}}, "fan_with_presets": {"name": "Ventilatore", "state_attributes": {"preset_mode": {"state": {"normal": "Normale", "nature": "Naturale", "sleep": "Sonno", "baby": "Bambino", "fresh": "Fresco", "smart": "Intelligente", "strong": "Forte", "custom": "<PERSON><PERSON><PERSON><PERSON>", "high": "Alto", "medium": "Medio", "low": "<PERSON><PERSON>", "displayoff": "Display spento", "off": "Spent<PERSON>"}}}}, "ventilation": {"name": "Ventilazione", "state_attributes": {"preset_mode": {"state": {"fresh": "Aria fresca", "circulate": "Circolare", "sleep": "Sonno", "auto": "Auto", "eco": "Eco", "anti-condensation": "Anticondensa", "extractor": "Estrattore", "heat_recovery": "Recupero di calore", "timer": "Timer", "on": "Acceso", "off": "Spent<PERSON>"}}}}}, "light": {"backlight": {"name": "Retroilluminazione"}, "display": {"name": "<PERSON><PERSON><PERSON>"}, "embers": {"name": "Brace"}, "flame": {"name": "<PERSON><PERSON><PERSON>"}, "indicator": {"name": "Indicatore"}, "laser": {"name": "Laser"}, "logs": {"name": "Ceppo"}, "nightlight": {"name": "Luce notturna"}}, "lock": {"child_lock": {"name": "<PERSON><PERSON> b<PERSON>"}}, "number": {"timer": {"name": "Timer"}, "timer_x": {"name": "Timer {x}"}}, "select": {"currency": {"name": "Valuta", "state": {"usd": "USD", "eur": "EUR", "cny": "CNY", "cad": "CAD", "gbp": "GBP"}}, "heat_pump_mode": {"name": "Modalità pompa di calore", "state": {"heat": "Riscaldamento", "cool": "Raffreddamento", "auto": "Auto", "floor_heat": "Riscaldamento pavimento", "off": "Spent<PERSON>", "hotwater": "<PERSON><PERSON><PERSON> calda", "hotwater_cool": "Acqua calda e raffreddamento", "hotwater_heat": "Acqua calda e riscaldamento", "hotwater_auto": "Acqua calda e auto", "hotwater_floor_heat": "Acqua calda e riscaldamento pavimento"}}, "initial_state": {"name": "Stato iniziale", "state": {"off": "Spent<PERSON>", "on": "Acceso", "memory": "Memoria"}}, "kettle_mode": {"name": "Modalità bollitore", "state": {"off": "Spent<PERSON>", "heat": "Riscaldamento", "boil": "<PERSON><PERSON><PERSON>", "quick_heat": "Riscaldamento rapido", "quick_boil": "Bollitura rapida", "keep_warm": "Mantenere caldo", "custom": "<PERSON><PERSON><PERSON><PERSON>", "dechlorinate": "Decalcificazione", "black_tea": "<PERSON><PERSON> nero", "green_tea": "<PERSON><PERSON> verde", "coffee": "Caffè", "honey_water": "Acqua al miele", "infant_formula": "Formula per neonati", "white_tea": "<PERSON><PERSON> bianco", "oolong_tea": "<PERSON><PERSON>"}}, "language": {"name": "<PERSON><PERSON>", "state": {"chinese": "中文", "chinese_traditional": "中文(繁體)", "english": "English", "french": "Français", "german": "De<PERSON>ch", "italian": "Italiano", "japanese": "日本語", "korean": "한국어", "latin": "Latino", "portuguese": "Português", "russian": "Русский", "spanish": "Español", "turkish": "Türkçe"}}, "light_mode": {"name": "Modalità luce", "state": {"off": "Spent<PERSON>", "on": "Acceso", "state": "Stato", "locator": "Localizzatore"}}, "mopping": {"name": "Pulizia", "state": {"off": "Spent<PERSON>", "auto": "Auto", "low": "<PERSON><PERSON>", "medium": "Medio", "high": "Alto"}}, "recipe": {"name": "<PERSON><PERSON>", "state": {"pizza": "Pizza", "fries": "Patatine fritte", "chicken": "Pollo", "shrimp": "<PERSON><PERSON><PERSON><PERSON>", "fish": "Pesce", "chicken_drumsticks": "Cosce di pollo", "vegetables": "Verdure", "desserts": "<PERSON><PERSON><PERSON>", "none": "<PERSON><PERSON><PERSON>", "chicken_wings": "<PERSON>o", "steak": "Bistecca", "onion_rings": "<PERSON><PERSON> di cipolla", "bacon": "Pancetta", "cake": "<PERSON><PERSON>", "bread": "Pane", "toast": "Tostare", "sausage": "<PERSON><PERSON><PERSON><PERSON>", "dry_fruit": "<PERSON><PERSON>a secca", "custom": "<PERSON><PERSON><PERSON><PERSON>", "cloud_recipe": "Ricetta cloud", "default": "Predefinito", "keep_warm": "Mantenimento calore", "preheat": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "scene": {"name": "<PERSON><PERSON>", "state": {"relax": "<PERSON><PERSON><PERSON><PERSON>", "movie": "Film", "party": "Festa", "romantic": "<PERSON>o", "night": "Notte", "morning": "<PERSON><PERSON>", "working": "Lavoro", "leisure": "Tempo libero", "vacation": "Vacanza", "reading": "Lettura", "twinkle": "<PERSON><PERSON><PERSON><PERSON>", "gaming": "Gaming", "none": "<PERSON><PERSON><PERSON>"}}, "timer": {"name": "Timer", "state": {"cancel": "<PERSON><PERSON><PERSON>", "continuous": "Continuo", "30s": "30 secondi", "1m": "1 minuto", "2m": "2 minuti", "5m": "5 minuti", "10m": "10 minuti", "20m": "20 minuti", "30m": "30 minuti", "40m": "40 minuti", "1h": "1 ora", "1h30m": "1 ora 30 minuti", "2h": "2 ore", "2h30m": "2 ore 30 minuti", "3h": "3 ore", "3h30m": "3 ore 30 minuti", "4h": "4 ore", "4h30m": "4 ore 30 minuti", "5h": "5 ore", "5h30m": "5 ore 30 minuti", "6h": "6 ore", "6h30m": "6 ore 30 minuti", "7h": "7 ore", "7h30m": "7 ore 30 minuti", "8h": "8 ore", "8h30m": "8 ore 30 minuti", "9h": "9 ore", "9h30m": "9 ore 30 minuti", "10h": "10 ore", "11h": "11 ore", "12h": "12 ore", "13h": "13 ore", "14h": "14 ore", "15h": "15 ore", "16h": "16 ore", "17h": "17 ore", "18h": "18 ore", "19h": "19 ore", "20h": "20 ore", "21h": "21 ore", "22h": "22 ore", "23h": "23 ore", "24h": "24 ore", "36h": "36 ore", "48h": "48 ore", "72h": "72 ore", "1d": "1 giorno", "2d": "2 giorni", "3d": "3 giorni", "4d": "4 giorni", "5d": "5 giorni", "6d": "6 giorni", "7d": "7 giorni"}}, "temperature_unit": {"name": "Unità di temperatura", "state": {"celsius": "Centigradi", "fahrenheit": "Fahrenheit"}}, "oven_built_in_recipe": {"name": "<PERSON><PERSON> incorporata", "state": {"none": "<PERSON><PERSON><PERSON>", "steamed_egg_with_okra": "Uovo al vapore con okra", "steamed_sea_bass": "Branzino al vapore", "steamed_prawns": "Gamberi al vapore", "handmade_steamed_bread": "Pane al vapore fatto a mano", "fan_steamed_baby_vegetables": "Verdure per bambini al vapore con ventola", "braised_pork": "<PERSON><PERSON> bra<PERSON>o", "snow_fungus_and_bird_s_nest": "Funghi della neve e nido d'u<PERSON>ello", "crab_pot": "Vaso di granchio", "potato_ribs": "Costine di patate", "coconut_chicken_soup": "Zup<PERSON> di pollo al cocco", "snack_platter": "Piatto di snack", "chicken_skewers": "Spiedini di pollo", "roasted_pork_knuckle": "Zampa di maiale arrosto", "dried_lemon": "Limone secco", "pork_jerky": "Carne di maiale essiccata", "crispy_hairtail": "Coda di capelli croccante", "spicy_grilled_fish": "Pesce alla griglia piccante", "roasted_sweet_potatoes": "<PERSON><PERSON> dol<PERSON> a<PERSON>", "roasted_chicken_wings": "Ali di pollo arrosto", "cumin_lamb_chops": "Costine di agnello al cumino", "honey_grilled_chicken": "Pollo alla griglia al miele", "garlic_eggplant": "Melanzane all'aglio", "portuguese_egg_tart": "Tartaruga portoghese", "creme_brulee": "Creme brulee", "cocoa_chips": "Cocoa chips", "butter_cookies": "B<PERSON>tti al burro", "chiffon_cake": "<PERSON><PERSON> chiffon", "puff_pastry": "Pasta sfoglia", "red_bean_bread": "Pane ai fagioli rossi", "milk_toast": "Pane al latte"}}}, "sensor": {"air_quality": {"name": "Qualità dell'aria", "state": {"excellent": "Eccellente", "good": "<PERSON><PERSON><PERSON>", "moderate": "Moderata", "poor": "Scars<PERSON>", "severe": "<PERSON><PERSON><PERSON>"}}, "status": {"name": "Stato", "state": {"available": "Disponibile", "plugged_in": "Collegato", "fault_unplugged": "<PERSON><PERSON><PERSON> (scollegato)", "charging": "In carica", "discharging": "In scarica", "waiting": "In attesa", "charged": "Carica completa", "fault": "<PERSON><PERSON><PERSON>", "paused": "In pausa", "waiting_for_authorization": "In attesa di autorizzazione", "standby": "Standby", "heating": "Riscaldamento", "cooling": "Raffreddamento", "keeping_warm": "Mantenimento calore", "no_water": "Senza acqua", "boiling": "Bollitura", "reserve_only": "<PERSON><PERSON>", "unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "idle": "Inattivo", "auto": "Auto", "manual": "Manuale", "rain_delay": "<PERSON><PERSON> p<PERSON>ia", "off": "Spent<PERSON>", "on": "Acceso", "cooking": "Cottura", "done": "<PERSON><PERSON>", "door_open": "Porta aperta", "setting": "Impostazione", "pre_heating": "Preriscaldamento", "scheduled": "Pianificato", "at_temperature": "A temperatura", "done_stage_1": "Fatto stadio 1", "done_stage_2": "Fatto stadio 2", "done_stage_3": "Fatto stadio 3", "done_stage_4": "Fatto stadio 4", "done_stage_5": "Fatto stadio 5", "done_stage_6": "Fatto stadio 6", "done_stage_7": "Fatto stadio 7", "done_stage_8": "Fatto stadio 8", "done_stage_9": "Fatto stadio 9", "done_stage_10": "Fatto stadio 10", "no_food": "Senza cibo", "jammed": "Inceppato", "blocked": "Bloccato", "feeding": "Alimentazione", "feeding_complete": "Alimentazione completata", "caking": "Agglomerazione", "cleaning": "Pulizia", "sleep": "Sonno", "sterilizing": "Sterilizzazione", "deodorizing": "Deodorizzazione", "occupied": "Occupato", "normal": "Normale", "low": "<PERSON><PERSON>", "high": "Alto", "unwashed": "Non lavato", "pre_washing": "Pre-lavaggio", "washing": "Lavaggio", "rinsing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drying": "Asciugatura", "air_purging": "Spurgatura aria", "anti_freeze": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON>", "monitor": "Monitor", "working": "<PERSON><PERSON><PERSON><PERSON>", "warning": "Attenzione", "starting": "<PERSON><PERSON><PERSON>", "emptying": "Svuotamento", "resetting": "<PERSON><PERSON><PERSON><PERSON>", "reverse": "Inversione", "full": "Pieno", "empty": "<PERSON><PERSON><PERSON>", "missing": "Mancante", "formatting": "Formattazione", "unformatted": "Non formattato"}}, "time_remaining": {"name": "Te<PERSON>"}, "time_remaining_x": {"name": "Tempo rim<PERSON>nte {x}"}, "cooking_status": {"name": "Stato di cottura", "state": {"wait": "In attesa di cucinare", "reservation": "In prenotazione", "cooking": "Cottura in corso", "cancel": "Cott<PERSON> annullata", "done": "Cottura completata", "pause": "Cottura in pausa"}}, "water_level": {"name": "Livello dell'acqua", "state": {"full": "Pieno", "high": "Alto", "medium": "Medio", "low": "<PERSON><PERSON>", "empty": "<PERSON><PERSON><PERSON>"}}, "energy_produced": {"name": "Energia prodotta"}, "energy_consumed": {"name": "Energia consumata"}, "energy_produced_x": {"name": "Energia prodotta {x}"}, "energy_consumed_x": {"name": "Energia consumata {x}"}, "current_x": {"name": "Corrente {x}"}, "voltage_x": {"name": "Tensione {x}"}, "power_x": {"name": "Potenza {x}"}}, "switch": {"anti_frost": {"name": "<PERSON><PERSON><PERSON>"}, "evaporator_cleaning": {"name": "Pulizia evaporatore"}, "ionizer": {"name": "Ionizzatore"}, "keytone": {"name": "tono dei tasti"}, "outlet_x": {"name": "Uscita {x}"}, "sleep": {"name": "Sonno"}, "switch_x": {"name": "Interruttore {x}"}, "electrolytic_sterilization": {"name": "Sterilizzazione elettrolitica"}, "uv_sterilization": {"name": "Sterilizzazione UV"}}, "text": {"scene": {"name": "<PERSON><PERSON>"}}, "time": {"timer": {"name": "Timer"}, "timer_x": {"name": "Timer {x}"}}, "valve": {"valve_x": {"name": "Valvola {x}"}}, "water_heater": {"water_air": {"name": "Scaldabagno"}, "kettle": {"name": "<PERSON><PERSON><PERSON>"}}}}