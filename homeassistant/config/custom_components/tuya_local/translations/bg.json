{"title": "Tuya Local", "config": {"step": {"user": {"title": "Конфигуриране на вашето Tuya Local устройство", "description": "Устройствата могат да се добавят ръчно или с помощта на облак с помощта на приложението SmartLife.", "data": {"setup_mode": "Избор на настройка:"}}, "cloud": {"title": "Влезте в Tuya", "description": "Въведете своя потребителски код за SmartLife или Tuya.\n\nМожете да намерите този код в приложението SmartLife или приложението Tuya в **Настройки** > екран **Акаунт и сигурност** и въведете кода", "data": {"user_code": "Потребителски код:"}}, "scan": {"title": "Завършете влизането", "description": "Използвайте приложението SmartLife или приложението Tuya"}, "choose_device": {"title": "Изберете устройството за добавяне", "description": "Моля", "data": {"device_id": "Изберете устройство:", "hub_id": "Изберете шлюз:"}}, "search": {"title": "Намерете IP адреса на устройството", "description": "Облакът Tuya не предоставя локални IP адреси"}, "local": {"title": "Конфигуриране на вашето Tuya Local устройство", "description": "[Следвайте тези инструкции, за да откриете идентификатора на вашето устройство и локалния ключ] (https://github.com/make-all/tuya-local#finding-your-device-id-and-local-key)", "data": {"host": "IP адрес или име на хост", "device_id": "Device ID (или Devi<PERSON> ID шлюза, за устройства свързани чрез шлюз)", "local_key": "Лок<PERSON><PERSON><PERSON><PERSON> ключ", "protocol_version": "Версия на протокола (опитайте автоматична, ако не е известна)", "poll_only": "Само анкетиране (опитайте това, ако устройството ви не работи напълно)", "device_cid": "Sub device node_id или uuid (за устройства свързани чрез шлюз)"}}, "select_type": {"title": "Изберете типа на устройството", "description": "Изберете типа, който съответства на вашето устройство", "data": {"type": "Тип устройство"}}, "choose_entities": {"title": "Подробности за устройството", "description": "Изберете име за това устройство", "data": {"name": "Име"}}}, "abort": {"already_configured": "Устройството с този идентификатор вече е добавено.", "not_supported": "Съжаляваме, няма поддръжка за това устройство, все още", "no_devices": "Не могат да бъдат намерени нерегистрирани устройства за акаунта."}, "error": {"connection": "Не може да се свърже с вашето устройство с тези данни. Възможно е да става въпрос за прекъсване или данните да са неправилни.", "does_not_need_hub": "Устройството не се нуждае от шлюз и беше избран такъв. ", "needs_hub": "Устройството се нуждае от шлюз и не е избран нито един."}}, "selector": {"setup_mode": {"options": {"cloud": "Настройка на устройство", "manual": "Предоставете ръчно информация за връзката на устройството.", "cloud_fresh_login": "Влезте в Tuya с нов акаунт"}}}, "options": {"step": {"user": {"title": "Конфигуриране на вашето Tuya Local устройство", "description": "[Следвайте тези инструкции, за да откриете своя локален ключ.](https://github.com/make-all/tuya-local#finding-your-device-id-and-local-key)", "data": {"host": "IP адрес или име на хост", "local_key": "Лок<PERSON><PERSON><PERSON><PERSON> ключ", "protocol_version": "Версия на протокола (опитайте автоматична, ако не е известна)", "poll_only": "Само анкетиране (опитайте това, ако устройството ви не работи напълно)"}}}, "error": {"connection": "Не може да се свърже с вашето устройство с тези данни. Възможно е да става въпрос за прекъсване или данните да са неправилни."}, "abort": {"not_supported": "Съжаляваме, няма поддръжка за това устройство, все още"}}, "entity": {"binary_sensor": {"direction": {"name": "Посока", "state": {"off": "Входящ", "on": "Изходящ"}}, "defrost": {"name": "Размразяване", "state": {"off": "Нормално", "on": "Размразяване"}}, "tank_empty": {"name": "Резервоар", "state": {"off": "OK", "on": "Ниско"}}, "tank_full": {"name": "Резервоар", "state": {"off": "OK", "on": "Пълна"}}, "wake": {"name": "Събуждане", "state": {"off": "Сън", "on": "Будене"}}, "casdon_oven_fault": {"state_attributes": {"description": {"state": {"e1": "E1: Камера за прегряване (над 290°C)", "e2": "E2: Защита от прегряване на изпарителния поднос (над 200°C)", "e3": "E3: Защита от ниска температура на камерата (под 35°C)", "e4": "E4: Защита от ниска температура на изпарителния поднос (под 35°C)", "e5": "E5: Грешк<PERSON> в свързването на сензора (Отворен кръг)", "e6": "E6: Гр<PERSON><PERSON>к<PERSON> в сензора (Късо съединение)", "e7": "E7: Грешк<PERSON> в комуникацията на дисплея", "e8": "E8: Отворен резервоар за вода (CN7) или суха тава за изпарение (CN3)"}}}}}, "button": {"factory_reset": {"name": "Фабричен ресет"}, "filter_reset": {"name": "Филтър за ресетиране"}}, "climate": {"aircon_extra": {"name": "Кли<PERSON><PERSON><PERSON>ик", "state_attributes": {"fan_mode": {"state": {"health": "Здрави", "medhigh": "Средна височина", "medlow": "Средно-нисък", "natural": "Естествено", "quiet": "Тихо", "sleep": "Сън", "strong": "<PERSON>ил<PERSON>н"}}, "swing_mode": {"state": {"topmost": "Най-отгоре", "top": "Връх", "middle": "Среден", "down": "Надолу", "downmost": "Най-долу"}}}}, "thermostat": {"name": "Термостат", "state_attributes": {"fan_mode": {"state": {"cycle": "Цикъл"}}, "preset_mode": {"state": {"manual": "Ръководство", "program": "Програма", "temp_override": "Временна отмяна", "perm_override": "Постоянна отмяна"}}}}, "combo_floor": {"state_attributes": {"preset_mode": {"state": {"cool": "Свеж въздух", "fan_only": "Вентила<PERSON><PERSON>р", "heat": "Топлинен въздух", "floor_cool": "Хладен под", "floor_heat": "Топъл под"}}}}, "swing_as_powerlevel": {"name": "Нагревател", "state_attributes": {"swing_mode": {"name": "Ниво на отопление", "state": {"stop": "Спри", "auto": "Автоматичен"}}}}, "pool_heatpump": {"state_attributes": {"preset_mode": {"state": {"smart_heat": "Умен топлина", "quick_heat": "Бърза топлина", "quiet_heat": "Тиха топлина", "smart_cool": "Умен охлаждане", "quick_cool": "Бързо охлаждане", "quiet_cool": "Тихо охлаждане", "auto": "Автоматичен", "smart": "Умен", "quick": "Бързо", "quiet": "Тихо"}}}}, "heater": {"name": "Нагревател"}, "thermo_switch": {"state_attributes": {"hvac_mode": {"state": {"fan_only": "Пауза"}}}}, "oven": {"state_attributes": {"preset_mode": {"state": {"healthy_steam": "Здравословна пара", "fresh_steam": "Свежа пара", "high_temp_steam": "Висока температура на пара", "stew": "<PERSON>а<PERSON><PERSON>н тиган", "bake_up_and_down": "Печка горе и долу", "bbq": "Барбекю", "bottom_hot_air": "Долен горещ въздух", "on_strong_roast": "На силен печене", "3d_hot_air": "3D горещ въздух", "air_fry": "Пържене на въздух", "steam_frying": "Парено пържене", "one_click_bread": "Едно кликване хляб", "quick_heat": "Бързо загряване", "keep_warm": "Поддържайте топло", "unfreeze": "Размразяване", "fermentation": "Ферментация", "descale": "Разклешаване", "local_recipes": "Локални рецепти", "drying": "Сушене", "custom": "Потребителски", "low_steaming": "Ниско парене", "medium_steaming": "Средно парене", "high_steaming": "Високо парене"}}}}}, "humidifier": {"dehumidifier": {"state_attributes": {"mode": {"state": {"laundry": "Сухо пране", "purify": "Пречистете"}}}}}, "fan": {"aroma_diffuser": {"name": "Ароматизатор", "state_attributes": {"preset_mode": {"state": {"low": "Ниско", "medium": "Средно", "high": "Високо", "continuous": "Непрекъснато", "intermittent": "Интервално", "timer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}}, "dehumidifier": {"name": "Обезвлажнител", "state_attributes": {"preset_mode": {"state": {"purify": "Пречистете", "dehumidify": "Обезвлажняване"}}}}, "fan_with_presets": {"name": "Вентила<PERSON><PERSON>р", "state_attributes": {"preset_mode": {"state": {"normal": "Нормално", "nature": "Естествено", "sleep": "Сън", "baby": "Бебе", "fresh": "Свежо", "smart": "Умен", "strong": "<PERSON>ил<PERSON>н", "custom": "Потребителски", "high": "Високо", "medium": "Средно", "low": "Ниско", "displayoff": "Дисплеят е изключен", "off": "Изключете дисплея"}}}}, "ventilation": {"name": "Вентилация", "state_attributes": {"preset_mode": {"state": {"fresh": "Свеж въздух", "circulate": "Циркулирайте", "sleep": "Сън", "auto": "Автоматичен", "eco": "Еко", "anti-condensation": "Антикон<PERSON><PERSON><PERSON>з", "extractor": "Екстрактор", "heat_recovery": "Възстановяване на топлината", "timer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "on": "Включено", "off": "Изключено"}}}}}, "light": {"backlight": {"name": "Подсветка"}, "display": {"name": "Дис<PERSON><PERSON>ей"}, "embers": {"name": "жарава"}, "flame": {"name": "Пламък"}, "indicator": {"name": "Индикатор"}, "laser": {"name": "<PERSON>а<PERSON><PERSON><PERSON>"}, "logs": {"name": "Дървени трупи"}, "nightlight": {"name": "Нощна светлина"}}, "lock": {"child_lock": {"name": "Заключване за деца"}}, "number": {"timer": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "timer_x": {"name": "Та<PERSON>мер {x}"}}, "select": {"currency": {"name": "Валута", "state": {"usd": "USD", "eur": "EUR", "cny": "CNY", "cad": "CAD", "gbp": "GBP"}}, "heat_pump_mode": {"name": "Режим на топлинна помпа", "state": {"heat": "Нагряване", "cool": "Охлаждане", "auto": "Автоматично", "floor_heat": "Подово отопление", "off": "Изключено", "hotwater": "Гореща вода", "hotwater_cool": "Гореща вода и охлаждане", "hotwater_heat": "Гореща вода и отопление", "hotwater_auto": "Гореща вода и автоматично", "hotwater_floor_heat": "Гореща вода и подово отопление"}}, "initial_state": {"name": "Начално състояние", "state": {"off": "Изключено", "on": "Включено", "memory": "Памет"}}, "kettle_mode": {"name": "Режим на чайник", "state": {"off": "Изключено", "heat": "Нагряване", "boil": "Кипене", "quick_heat": "Бързо загряване", "quick_boil": "Бързо кипене", "keep_warm": "Поддържайте топло", "custom": "Потребителски", "dechlorinate": "Дехлориране", "black_tea": "Черно чай", "green_tea": "<PERSON><PERSON><PERSON><PERSON><PERSON> чай", "coffee": "Кафе", "honey_water": "Меден вода", "infant_formula": "Бебешка формула", "white_tea": "<PERSON><PERSON><PERSON>ай", "oolong_tea": "Улун чай"}}, "language": {"name": "Език", "state": {"chinese": "中文", "chinese_traditional": "中文(繁體)", "english": "English", "french": "Français", "german": "De<PERSON>ch", "italian": "Italiano", "japanese": "日本語", "korean": "한국어", "latin": "Lingua Latina", "portuguese": "Português", "russian": "Русский", "spanish": "Español", "turkish": "Türkçe"}}, "light_mode": {"name": "Режим на светлина", "state": {"off": "Изключено", "on": "Включено", "state": "Състояние", "locator": "Локатор"}}, "mopping": {"name": "Подметка", "state": {"off": "Изключено", "auto": "Автоматично", "low": "Ниско", "medium": "Средно", "high": "Високо"}}, "recipe": {"name": "Рецепта", "state": {"pizza": "Пица", "fries": "Картофи", "chicken": "Пиле", "shrimp": "Козици", "fish": "Риба", "chicken_drumsticks": "Пилешки крака", "vegetables": "Зеленчуци", "desserts": "Десерти", "none": "Няма", "chicken_wings": "Пилешки крилца", "steak": "Стек", "onion_rings": "Лукови пръстени", "bacon": "Сладко", "cake": "Торта", "bread": "Х<PERSON><PERSON><PERSON>", "toast": "Тост", "sausage": "Наденица", "dry_fruit": "Сухи плодове", "custom": "Потребителски", "cloud_recipe": "Облак рецепта", "default": "По подразбиране", "keep_warm": "Поддържайте топло", "preheat": "Предварително загряване"}}, "scene": {"name": "Сцена", "state": {"relax": "Релаксирайте", "movie": "<PERSON>ил<PERSON>", "party": "Парти", "romantic": "Романтично", "night": "Нощ", "morning": "Сутрин", "working": "Работа", "leisure": "Свободно време", "vacation": "Ваканция", "reading": "Четене", "twinkle": "Миг<PERSON><PERSON>о", "gaming": "Игри", "none": "Няма"}}, "timer": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state": {"cancel": "Отказ", "continuous": "Непрекъснато", "30s": "30 секунди", "1m": "1 минута", "2m": "2 минути", "5m": "5 минути", "10m": "10 минути", "20m": "20 минути", "30m": "30 минути", "40m": "40 минути", "1h": "1 час", "1h30m": "1 час 30 минути", "2h": "2 часа", "2h30m": "2 часа 30 минути", "3h": "3 часа", "3h30m": "3 часа 30 минути", "4h": "4 часа", "4h30m": "4 часа 30 минути", "5h": "5 часа", "5h30m": "5 часа 30 минути", "6h": "6 часа", "6h30m": "6 часа 30 минути", "7h": "7 часа", "7h30m": "7 часа 30 минути", "8h": "8 часа", "8h30m": "8 часа 30 минути", "9h": "9 часа", "9h30m": "9 часа 30 минути", "10h": "10 часа", "11h": "11 часа", "12h": "12 часа", "13h": "13 часа", "14h": "14 часа", "15h": "15 часа", "16h": "16 часа", "17h": "17 часа", "18h": "18 часа", "19h": "19 часа", "20h": "20 часа", "21h": "21 часа", "22h": "22 часа", "23h": "23 часа", "24h": "24 часа", "36h": "36 часа", "48h": "48 часа", "72h": "72 часа", "1d": "1 ден", "2d": "2 дни", "3d": "3 дни", "4d": "4 дни", "5d": "5 дни", "6d": "6 дни", "7d": "7 дни"}}, "temperature_unit": {"name": "Температурна единица", "state": {"celsius": "Целзий", "fahrenheit": "<PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>т"}}, "oven_built_in_recipe": {"name": "Вградена рецепта", "state": {"none": "Няма", "steamed_egg_with_okra": "Парена яйца с окра", "steamed_sea_bass": "Парена морска бас", "steamed_prawns": "Парени козици", "handmade_steamed_bread": "Ръчно парен хляб", "fan_steamed_baby_vegetables": "Фен парени бебешки зеленчуци", "braised_pork": "Печено свинско", "snow_fungus_and_bird_s_nest": "Снежна гъба и птиче гнездо", "crab_pot": "Кра<PERSON><PERSON> гърнец", "potato_ribs": "Картофени ребра", "coconut_chicken_soup": "Кокосова пилешка супа", "snack_platter": "Плато с хапки", "chicken_skewers": "Пилешки шишчета", "roasted_pork_knuckle": "Печено свинско копито", "dried_lemon": "Сушен лимон", "pork_jerky": "Свинско джърки", "crispy_hairtail": "Хрупкава опашка", "spicy_grilled_fish": "Пикантна печена риба", "roasted_sweet_potatoes": "Печени сладки картофи", "roasted_chicken_wings": "Печени пилешки крилца", "cumin_lamb_chops": "Агнешки котлети с кимион", "honey_grilled_chicken": "Пилешко на скара с мед", "garlic_eggplant": "Чеснова патладжан", "portuguese_egg_tart": "Португалски яйчен тарт", "creme_brulee": "Крем Брюле", "cocoa_chips": "Какаови чипсове", "butter_cookies": "<PERSON><PERSON><PERSON><PERSON><PERSON> кура<PERSON><PERSON><PERSON>и", "chiffon_cake": "Шифонова торта", "puff_pastry": "Слойково тесто", "red_bean_bread": "Хляб с червено боб", "milk_toast": "Млечен тост"}}}, "sensor": {"air_quality": {"name": "Качество на въздуха", "state": {"excellent": "Отлично", "good": "Добро", "moderate": "Умерен", "poor": "Лошо", "severe": "Тежко"}}, "status": {"name": "Статус", "state": {"available": "Достъпно", "plugged_in": "Включен", "fault_unplugged": "Неизправен (изключен)", "charging": "Зарежда се", "discharging": "Разреждане", "waiting": "Изчакване", "charged": "Заред<PERSON>н", "fault": "Неизправен", "paused": "Пауза", "waiting_for_authorization": "Изчакване за разрешение", "standby": "Режим на готовност", "heating": "Загряване", "cooling": "Охлаждане", "keeping_warm": "Поддържайте топло", "no_water": "Няма вода", "boiling": "Кипене", "reserve_only": "Само резервен", "unknown": "Неизвестен", "idle": "Свободен", "auto": "Автоматичен", "manual": "Ръ<PERSON><PERSON>н", "rain_delay": "Забавяне на дъжда", "off": "Изключено", "on": "Включено", "cooking": "Готвене", "done": "Готово", "door_open": "Вратата е отворена", "setting": "Настройка", "pre_heating": "Предварително загряване", "scheduled": "План<PERSON><PERSON><PERSON>но", "at_temperature": "На температура", "done_stage_1": "Готово за стадиум 1", "done_stage_2": "Готово за стадиум 2", "done_stage_3": "Готово за стадиум 3", "done_stage_4": "Готово за стадиум 4", "done_stage_5": "Готово за стадиум 5", "done_stage_6": "Готово за стадиум 6", "done_stage_7": "Готово за стадиум 7", "done_stage_8": "Готово за стадиум 8", "done_stage_9": "Готово за стадиум 9", "done_stage_10": "Готово за стадиум 10", "no_food": "Няма храна", "jammed": "Заглушен", "blocked": "Блокиран", "feeding": "Хранене", "feeding_complete": "Храненето е завършено", "caking": "Торта", "cleaning": "Почистване", "sleep": "Сън", "sterilizing": "Стерилизация", "deodorizing": "Дезодориране", "occupied": "Зает", "normal": "Нормално", "low": "Ниско", "high": "Високо", "unwashed": "Непочистен", "pre_washing": "Предварително измиване", "washing": "Пране", "rinsing": "Изплакване", "drying": "Сушене", "air_purging": "Пречистване на въздуха", "anti_freeze": "Ан<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "close": "Затвори", "monitor": "Монитор", "working": "Работи", "warning": "Предупреждение", "starting": "Стартира<PERSON>е", "emptying": "Изтощаване", "resetting": "Нулиране", "reverse": "Обратно", "full": "Пълен", "empty": "Празен", "missing": "Липсва", "formatting": "Форматиране", "unformatted": "Неформатиран"}}, "time_remaining": {"name": "Оставащо време"}, "time_remaining_x": {"name": "Оставащо време {x}"}, "cooking_status": {"name": "Състояние", "state": {"wait": "Изчакване за готвене", "reservation": "В резервация", "cooking": "Готвене", "cancel": "Готвенето е отменено", "done": "Готвенето завърши", "pause": "Пауза за готвене"}}, "water_level": {"name": "Ниво на водата", "state": {"full": "Пълна", "high": "Висока", "medium": "Средна", "low": "Ниска", "empty": "Празна"}}, "energy_produced": {"name": "Произведена енергия"}, "energy_consumed": {"name": "Консумирана енергия"}, "energy_produced_x": {"name": "Произведена енергия {x}"}, "energy_consumed_x": {"name": "Консумирана енергия {x}"}, "current_x": {"name": "Текущ {x}"}, "voltage_x": {"name": "Напрежение {x}"}, "power_x": {"name": "Мощност {x}"}}, "switch": {"anti_frost": {"name": "Анти-замръзване"}, "evaporator_cleaning": {"name": "Почистване изпарител"}, "ionizer": {"name": "Йонизатор"}, "keytone": {"name": "Клавишен тон"}, "outlet_x": {"name": "Изход {x}"}, "sleep": {"name": "Сън"}, "switch_x": {"name": "Превключване {x}"}, "electrolytic_sterilization": {"name": "Електролизна стерилизация"}, "uv_sterilization": {"name": "UV стерилизация"}}, "text": {"scene": {"name": "Сцена"}}, "time": {"timer": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "timer_x": {"name": "Та<PERSON>мер {x}"}}, "valve": {"valve_x": {"name": "<PERSON>ла<PERSON>ан {x}"}}, "water_heater": {"water_air": {"name": "Нагревател на вода"}, "kettle": {"name": "Кана"}}}}