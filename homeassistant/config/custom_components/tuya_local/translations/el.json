{"title": "Tuya Local", "config": {"step": {"user": {"title": "Ρυθμίστε τη συσκευή σας <PERSON>ya Local", "description": "Συσκευές μπορούν να προστεθούν είτε χειροκίνητα, είτε υποβοηθούμενα από το cloud με τη βοήθεια της εφαρμογής SmartLife.", "data": {"setup_mode": "Επιλογή εγκατάστασης:"}}, "cloud": {"title": "Σύνδεση στο Tuya", "description": "Εισάγετε τον κωδικό χρήστη του SmartLife ή του Tuya.\n\nΜπορείτε να βρείτε αυτόν τον κωδικό στην εφαρμογή SmartLife ή στην εφαρμογή Tuya στην οθόνη **Settings** > **Account and Security**, και θα εισάγετε τον κωδικό που φαίνεται στο πεδίο **User Code**. Ο κωδικός χρήστη χρησιμοποιεί διαχωρισμό κεφαλαίων/πεζών, παρα<PERSON><PERSON><PERSON><PERSON> σιγουρευτείτε ότι τον εισάγατε ακριβώς όπως φαίνεται στην εφαρμογή.", "data": {"user_code": "<PERSON>ω<PERSON><PERSON><PERSON><PERSON><PERSON> Χρήστη:"}}, "scan": {"title": "Ολοκλήρωση της σύνδεσης", "description": "Χρησιμοποιήστε τις εφαρμογές SmartLife ή Tuya για να σαρώσετε τον ακόλουθο κωδικό QR και να ολοκληρώσετε τη σύνδεση.\n\nΣυνεχίστε στο επόμενο βήμα μόλις έχετε ολοκληρώσει αυτό το βήμα στην εφαρμογή."}, "choose_device": {"title": "Επιλέξτε τη συσκευή για προσθήκη", "description": "Παρα<PERSON><PERSON><PERSON><PERSON> επιλέξτε τη συσκευή για προσθήκη από την πρώτη λίστα επιλογής. Συσκευές που έχουν ήδη προστεθεί δεν εμφανίζονται.\n\nΑν η συσκευή συνδέεται μέσω κάποιου gateway, επιλέξτε το από τη λίστα gateway, αλλιώς επιλέξτε Κανένα (None).", "data": {"device_id": "Επιλέξτε συσκευή:", "hub_id": "Επιλέξτε gateway:"}}, "search": {"title": "Βρείτε τη διεύθυνση IP της συσκευής", "description": "Το Tuya cloud δεν παρέχει τοπικές διευθύνσεις IP, οπότε τώρα πρέπει να ψάξουμε στο τοπικό σας δίκτυο για να βρούμε τη συσκευή. Αυτό μπορεί να διαρκέσει μέχρι και 20 δευτερόλεπτα.\n\nΑν αποτύχει θα πρέπει να παρέχετε εσείς τη διεύθυνση IP με κάποιο άλλο τρόπο, όπως για παράδειγμα από το DHCP assignment του ρούτερ σας.\n\nΓια αυτό το βήμα και την προσθήκη συσκευής που ακολουθεί πρέπει να κλείσετε την εφαρμογή του κινητού σας, αλλιώς οι συνδέσεις του προς τις συσκευές συχνά εμποδίζουν το Tuya Local να επικοινωνήσει μαζί τους."}, "local": {"title": "Ρυθμίστε τη συσκευή σας <PERSON>ya Local", "description": "[Ακολουθήστε αυτές τις οδηγίες για να βρείτε το device id και το local key.](https://github.com/make-all/tuya-local#finding-your-device-id-and-local-key)", "data": {"host": "Διεύθυνση IP ή όνομα", "device_id": "Device ID (ή device_id της gateway για συσκευές συνδεδεμένες μέσω gateway)", "local_key": "Local key", "protocol_version": "Έκδοση Πρωτοκόλλου (δοκιμάστε αυτόματο (auto) αν δεν το γνωρίζετε)", "poll_only": "Ανίχνευση μόνο (δοκιμάστε αυτό αν η συσκευή δε λειτουργεί πλήρως)", "device_cid": "Sub device node_id ή uuid (για συσκευές συνδεμένες μέσω gateway)"}}, "select_type": {"title": "Επιλέξτε τον τύπο της συσκευής", "description": "Επιλέξτε τον τύπο που ταιριάζει στη συσκευή σας", "data": {"type": "Τύ<PERSON><PERSON> συσκευής"}}, "choose_entities": {"title": "Λεπτομέρειες συσκευής", "description": "Επιλέξτε ένα όνομα για αυτή τη συσκευή", "data": {"name": "Όνομα"}}}, "abort": {"already_configured": "Έχει ήδη προστεθεί μια συσκευή με αυτό το ID.", "not_supported": "Λυπ<PERSON>ύμαστε, αυτή η συσκευή δεν υποστηρίζεται.", "no_devices": "Αδυναμία εύρεσης μη εγγεγραμμένων συσκευών για αυτό το λογαριασμό."}, "error": {"connection": "Αδυναμία σύνδεσης στη συσκευή σας με αυτά τα στοιχεία. Ίσως είναι προσωρινό πρόβλημα, ή τα στοιχεία που καταχωρίσατε είναι λάθος.", "does_not_need_hub": "Η συσκευή δεν χρειάζεται κάποιο gateway και επιλέχτηκε ένα. Παρακαλώ ελέγξτε τις επιλογές.", "needs_hub": "Η συσκευή χρειάζεται gateway και δεν επιλέχτηκε κάποιο."}}, "selector": {"setup_mode": {"options": {"cloud": "Εγκατάσταση συσκευής υποβοηθούμενη απο το SmartLife cloud.", "manual": "Εισάγετε χειροκίνητα τις πληροφορίες για τη σύνδεση της συσκευής.", "cloud_fresh_login": "Εγκατάστα<PERSON>η συσκευής υποβοηθούμενη απο το SmartLife cloud με νέο λογαριασμό."}}}, "options": {"step": {"user": {"title": "Ρυθμίστε τη συσκευή σας <PERSON>ya Local", "description": "[Ακολουθήστε αυτές τις οδηγίες για να βρείτε το local key.](https://github.com/make-all/tuya-local#finding-your-device-id-and-local-key)", "data": {"host": "Διεύθυνση IP ή όνομα", "local_key": "Local key", "protocol_version": "Έκδοση Πρωτοκόλλου (δοκιμάστε αυτόματο (auto) αν δεν το γνωρίζετε)", "poll_only": "Ανίχνευση μόνο (δοκιμάστε αυτό αν η συσκευή δε λειτουργεί πλήρως)"}}}, "error": {"connection": "Αδυναμία σύνδεσης στη συσκευή σας με αυτές τα στοιχεία. Ίσως είναι προσωρινό πρόβλημα, ή τα στοιxεία που καταχωρίσατε είναι λάθος."}, "abort": {"not_supported": "Λυπ<PERSON>ύμαστε, αυτή η συσκευή δεν υποστηρίζεται."}}, "entity": {"binary_sensor": {"direction": {"name": "Κατεύθυνση", "state": {"off": "Εισερχόμενο", "on": "Εξερχόμενο"}}, "defrost": {"name": "Απόψυξη", "state": {"off": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "on": "Γίνεται απόψυξη"}}, "tank_empty": {"name": "Δοχείο", "state": {"off": "OK", "on": "Χαμη<PERSON>ή στάθμη"}}, "tank_full": {"name": "Δοχείο", "state": {"off": "OK", "on": "Γεμάτο"}}, "wake": {"name": "Ξύπνημα", "state": {"off": "Αναμονή", "on": "Ξύπνημα"}}, "casdon_oven_fault": {"state_attributes": {"description": {"state": {"e1": "E1: Πρ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> υπερθέρμαν<PERSON>ης θαλάμου (υπερβαίνει τους 290°C)", "e2": "E2: Πρ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> υπερθέρμαν<PERSON>ης δίσκου εξάτμισης (υπερβαίνει τους 200°C)", "e3": "E3: Πρ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> χαμηλής θερμοκρασίας θαλάμου (κάτω από 35°C)", "e4": "E4: Πρ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> χαμηλής θερμοκρασίας δίσκου εξάτμισης (κάτω από 35°C)", "e5": "E5: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> σύνδεσης αισθητήρα (Ανοικτό κύκλωμα)", "e6": "E6: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> αισθητήρα (Κλ<PERSON>ιστ<PERSON> κύκλωμα)", "e7": "E7: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> επικοινων<PERSON>ας πίνακα εμφάνισης", "e8": "E8: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> νερού ανοιχτ<PERSON> (CN7) ή Ξηρό δίσκο εξάτμισης (CN3)"}}}}}, "button": {"factory_reset": {"name": "Επαναφορ<PERSON> εργοστασιακών ρυθμίσεων"}, "filter_reset": {"name": "Επαναφορ<PERSON> φίλτρου"}}, "climate": {"aircon_extra": {"name": "Κλιματιστικό", "state_attributes": {"fan_mode": {"state": {"health": "Υγιεινό", "medhigh": "Μεσαίο-Υψηλό", "medlow": "Μεσαίο-Χαμηλό", "natural": "Φυσικό", "quiet": "Ήσυχο", "sleep": "Αναμονή", "strong": "Δυνατό"}}, "swing_mode": {"state": {"topmost": "Υψηλότερο δυνατόν", "top": "Υψηλό", "middle": "Μεσαίο", "down": "Χαμηλό", "downmost": "<PERSON><PERSON><PERSON><PERSON>λ<PERSON>τ<PERSON>ρ<PERSON> δυνατόν"}}}}, "thermostat": {"name": "Θερμοστάτης", "state_attributes": {"fan_mode": {"state": {"cycle": "<PERSON><PERSON><PERSON><PERSON>ος"}}, "preset_mode": {"state": {"manual": "Χε<PERSON>ρ<PERSON>κ<PERSON>νητα", "program": "Προγραμματισμός", "temp_override": "Προσωρινή Παράκαμψη", "perm_override": "Μόνιμη Παράκαμψη"}}}}, "combo_floor": {"state_attributes": {"preset_mode": {"state": {"cool": "Ψύξη αέρα", "fan_only": "Ανεμιστήρας", "heat": "Θέρμανση αέρα", "floor_cool": "Ψύξη πατώματος", "floor_heat": "Θέρμανση πατώματος"}}}}, "swing_as_powerlevel": {"name": "Θέρμανση", "state_attributes": {"swing_mode": {"name": "Επίπεδο θέρμανσης", "state": {"stop": "Διακοπή", "auto": "Αυτόματο"}}}}, "pool_heatpump": {"state_attributes": {"preset_mode": {"state": {"smart_heat": "Έξυπνη θέρμανση", "quick_heat": "Γρήγορη θέρμανση", "quiet_heat": "Ήσυχη θέρμανση", "smart_cool": "Έξυπνη ψύξη", "quick_cool": "Γρήγορη ψύξη", "quiet_cool": "Ήσυχη ψύξη", "auto": "Αυτόματο", "smart": "Έξυπνο", "quick": "Γρήγ<PERSON><PERSON><PERSON>", "quiet": "Ήσυχο"}}}}, "heater": {"name": "Θέρμανση"}, "thermo_switch": {"state_attributes": {"hvac_mode": {"state": {"fan_only": "Παύση"}}}}, "oven": {"state_attributes": {"preset_mode": {"state": {"healthy_steam": "Υγιεινή ατμός", "fresh_steam": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ατμός", "high_temp_steam": "Υψηλή θερμοκρασία ατμού", "stew": "Στοιχείο", "bake_up_and_down": "Ψήσιμο πάνω και κάτω", "bbq": "Ψήσιμο στη σχάρα", "bottom_hot_air": "Κάτω ζεστός αέρας", "on_strong_roast": "Στον δυνατό ψητό", "3d_hot_air": "3D ζεστός αέρας", "air_fry": "Ψήσι<PERSON>ο στον αέρα", "steam_frying": "Ατμ<PERSON>ς και τηγάνισμα", "one_click_bread": "Ένα κλικ ψωμί", "quick_heat": "Γρήγορη θέρμανση", "keep_warm": "Διατήρηση θερμοκρασίας", "unfreeze": "Απόψυξη", "fermentation": "Ζύμωση", "descale": "Αφαίρεση αλάτων", "local_recipes": "Τοπικές συνταγές", "drying": "Στέγνωμα", "custom": "Προσαρμοσμένο", "low_steaming": "Χαμη<PERSON><PERSON> ατμός", "medium_steaming": "Μεσαία ατμός", "high_steaming": "Υψηλή ατμός"}}}}}, "humidifier": {"dehumidifier": {"state_attributes": {"mode": {"state": {"laundry": "Στέγνωμα ρούχων", "purify": "Καθαρισμός"}}}}}, "fan": {"aroma_diffuser": {"name": "Αρωμα<PERSON>ι<PERSON><PERSON>ς διαχύτης", "state_attributes": {"preset_mode": {"state": {"low": "Χαμηλή", "medium": "Μεσαία", "high": "Υψηλή", "continuous": "Συν<PERSON><PERSON><PERSON>ς", "intermittent": "Διακεκομμένη", "timer": "Χρονο<PERSON><PERSON><PERSON>κ<PERSON>πτης"}}}}, "dehumidifier": {"name": "Αφυγραντ<PERSON><PERSON><PERSON>ς", "state_attributes": {"preset_mode": {"state": {"purify": "Καθαρισμός", "dehumidify": "Αφυγραντ<PERSON><PERSON><PERSON>ς"}}}}, "fan_with_presets": {"name": "Ανεμιστήρας", "state_attributes": {"preset_mode": {"state": {"baby": "Μωρό", "fresh": "Φρέσ<PERSON><PERSON>", "nature": "Φυσικό", "normal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sleep": "Αναμονή", "smart": "Έξυπνο", "strong": "Δυνατό", "custom": "Προσαρμοσμένο", "high": "Υψηλό", "medium": "Μεσαίο", "low": "Χαμηλό", "displayoff": "Σβήσιμο οθόνης", "off": "Ανενεργό"}}}}, "ventilation": {"name": "Αερισμός", "state_attributes": {"preset_mode": {"state": {"fresh": "<PERSON>α<PERSON><PERSON><PERSON><PERSON>ς αέρας", "circulate": "Κυκλοφορία αέρα", "sleep": "Αναμονή", "auto": "Αυτόματο", "eco": "Οικονομικό", "anti-condensation": "Αποφυγή υγρασίας", "extractor": "Εξαγωγή", "heat_recovery": "Ανάκτηση θερμότητας", "timer": "Χρονο<PERSON><PERSON><PERSON>κ<PERSON>πτης", "on": "Ενεργοποίηση", "off": "Απενεργοποίηση"}}}}}, "light": {"backlight": {"name": "Οπίσθιος φωτισμός"}, "display": {"name": "Οθόνη"}, "embers": {"name": "Χόβολη"}, "flame": {"name": "Φωτιά"}, "indicator": {"name": "Ένδειξη"}, "laser": {"name": "Λέιζ<PERSON>ρ"}, "logs": {"name": "Κούτσουρα"}, "nightlight": {"name": "Νυχτεριν<PERSON>ς φωτισμός"}}, "lock": {"child_lock": {"name": "Παιδικό κλείδωμα"}}, "number": {"timer": {"name": "Χρονο<PERSON><PERSON><PERSON>κ<PERSON>πτης"}, "timer_x": {"name": "Χρονοδιακ<PERSON>πτης {x}"}}, "select": {"currency": {"name": "Νόμισμα", "state": {"usd": "Δολάριο ΗΠΑ", "eur": "Ευρ<PERSON>", "cny": "Γ<PERSON>υ<PERSON><PERSON>", "cad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gbp": "Λίρ<PERSON>τε<PERSON>λίνα"}}, "heat_pump_mode": {"name": "Λειτουργ<PERSON>α αντλίας θερμότητας", "state": {"heat": "Θέρμανση", "cool": "Ψύξη", "auto": "Αυτόματο", "floor_heat": "Θέρμανση πατώματος", "off": "Απενεργοποίηση", "hotwater": "Ζεστό νερό", "hotwater_cool": "Ζεστό νερό και ψύξη", "hotwater_heat": "Ζεστό νερό και θέρμανση", "hotwater_auto": "Αυτόματο ζεστό νερό", "hotwater_floor_heat": "Ζεστό νερό και θέρμανση πατώματος"}}, "initial_state": {"name": "Αρχική κατάσταση", "state": {"on": "Ενεργοποίηση", "off": "Απενεργοποίηση", "memory": "Μνήμη"}}, "kettle_mode": {"name": "Λειτουργία βραστήρα", "state": {"off": "Απενεργοποίηση", "heat": "Θέρμανση", "boil": "Βράσιμο", "quick_heat": "Γρήγορη θέρμανση", "quick_boil": "Γρήγορο βράσιμο", "keep_warm": "Διατήρηση θερμοκρασίας", "custom": "Προσαρμοσμένο", "dechlorinate": "Αποχλωρίωση", "black_tea": "Μαύρο τσάι", "green_tea": "Πράσινο τσάι", "coffee": "<PERSON><PERSON><PERSON><PERSON>ς", "honey_water": "Νερό με μέλι", "infant_formula": "Γάλα για μωρά", "white_tea": "Λευκό τσάι", "oolong_tea": "Oolong τσάι"}}, "language": {"name": "Γλώσσα", "state": {"chinese": "中文", "chinese_traditional": "中文(繁體)", "english": "English", "french": "Français", "german": "De<PERSON>ch", "italian": "Italiano", "japanese": "日本語", "korean": "한국어", "latin": "Lingua Latina", "portuguese": "Português", "russian": "Русский", "spanish": "Español", "turkish": "Türkçe"}}, "light_mode": {"name": "Λειτουργ<PERSON><PERSON> φωτισμού", "state": {"off": "Απενεργοποίηση", "on": "Ενεργοποίηση", "state": "Κατάσταση", "locator": "Εντοπισμός"}}, "mopping": {"name": "Σφουγγάρισμα", "state": {"off": "Απενεργοποίηση", "auto": "Αυτόματο", "low": "Χαμηλή", "medium": "Μεσαία", "high": "Υψηλή"}}, "recipe": {"name": "Συνταγή", "state": {"pizza": "Πίτσα", "fries": "Πατάτες", "chicken": "Κοτόπουλο", "shrimp": "Γα<PERSON><PERSON><PERSON><PERSON>ς", "fish": "Ψάρι", "chicken_drumsticks": "Μπατ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> κοτόπουλου", "vegetables": "Λαχανικά", "desserts": "Γλυκά", "none": "Καμία", "chicken_wings": "Φτερ<PERSON>ύ<PERSON><PERSON><PERSON> κοτόπουλου", "steak": "Μπριζόλα", "onion_rings": "Κρεμμύδια", "bacon": "Μπέικον", "cake": "Κέικ", "bread": "Ψωμί", "toast": "Ψωμί", "sausage": "Λουκάνικ<PERSON>", "dry_fruit": "Ξηροί καρποί", "custom": "Προσαρμοσμένο", "cloud_recipe": "Συνταγή από το cloud", "default": "Προεπιλογή", "keep_warm": "Διατήρηση θερμοκρασίας", "preheat": "Προθέρμανση"}}, "scene": {"name": "Σκηνή", "state": {"relax": "Χαλάρω<PERSON>η", "movie": "Ταινία", "party": "Π<PERSON>ρ<PERSON>ι", "romantic": "Ρομαντικό", "night": "Νύχτα", "morning": "Πρωί", "working": "Εργασία", "leisure": "Αναψυχή", "vacation": "Διακ<PERSON><PERSON><PERSON>ς", "reading": "Ανάγνωση", "twinkle": "Λάμψη", "gaming": "Παιχνίδι", "none": "Καμία"}}, "timer": {"name": "Χρονο<PERSON><PERSON><PERSON>κ<PERSON>πτης", "state": {"cancel": "Ακύρωση", "continuous": "Συν<PERSON><PERSON><PERSON>ς", "30s": "30 δευτερόλεπτα", "1m": "1 λεπτό", "2m": "2 λεπτά", "5m": "5 λεπτά", "10m": "10 λεπτά", "20m": "20 λεπτά", "30m": "30 λεπτά", "40m": "40 λεπτά", "1h": "1 ώρα", "1h30m": "1 ώρα 30 λεπτά", "2h": "2 ώρες", "2h30m": "2 ώρες 30 λεπτά", "3h": "3 ώρες", "3h30m": "3 ώρες 30 λεπτά", "4h": "4 ώρες", "4h30m": "4 ώρες 30 λεπτά", "5h": "5 ώρες", "5h30m": "5 ώρες 30 λεπτά", "6h": "6 ώρες", "6h30m": "6 ώρες 30 λεπτά", "7h": "7 ώρες", "7h30m": "7 ώρες 30 λεπτά", "8h": "8 ώρες", "8h30m": "8 ώρες 30 λεπτά", "9h": "9 ώρες", "9h30m": "9 ώρες 30 λεπτά", "10h": "10 ώρες", "11h": "11 ώρες", "12h": "12 ώρες", "13h": "13 ώρες", "14h": "14 ώρες", "15h": "15 ώρες", "16h": "16 ώρες", "17h": "17 ώρες", "18h": "18 ώρες", "19h": "19 ώρες", "20h": "20 ώρες", "21h": "21 ώρες", "22h": "22 ώρες", "23h": "23 ώρες", "24h": "24 ώρες", "36h": "36 ώρες", "48h": "48 ώρες", "72h": "72 ώρες", "1d": "1 ημέρα", "2d": "2 ημέρες", "3d": "3 ημέρες", "4d": "4 ημέρες", "5d": "5 ημέρες", "6d": "6 ημέρες", "7d": "7 ημέρες"}}, "temperature_unit": {"name": "Μονάδα μέτρησης θερμοκρασίας", "state": {"celsius": "Κελσίου", "fahrenheit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>τ"}}, "oven_built_in_recipe": {"name": "Ενσωματωμένη συνταγή", "state": {"none": "Καμία", "steamed_egg_with_okra": "Αυγό με οκρα", "steamed_sea_bass": "Ατλαντι<PERSON><PERSON><PERSON> λαβράκι", "steamed_prawns": "Γα<PERSON><PERSON><PERSON><PERSON>ς", "handmade_steamed_bread": "Χειροπ<PERSON>ίητο ψωμί", "fan_steamed_baby_vegetables": "Ατμός με λαχανικά", "braised_pork": "Χοιρινό", "snow_fungus_and_bird_s_nest": "Μανιτάρια και φωλιά πουλιών", "crab_pot": "Κατσαρίδα", "potato_ribs": "Πατάτες με ριπς", "coconut_chicken_soup": "Σούπα κοτόπουλο με καρύδα", "snack_platter": "Πιάτο με σνακ", "chicken_skewers": "Κοτόπουλο σε σουβλάκι", "roasted_pork_knuckle": "Ψητό χοιρινό", "dried_lemon": "Ξερό λεμόνι", "pork_jerky": "Χοιρινό τζέρκι", "crispy_hairtail": "Τραγανό ψαρονέφρι", "spicy_grilled_fish": "Καυτερό ψάρι στη σχάρα", "roasted_sweet_potatoes": "Ψητ<PERSON><PERSON> γλυκοπατάτες", "roasted_chicken_wings": "Ψητ<PERSON>ς φτερούγες κοτόπουλου", "cumin_lamb_chops": "Μπριζόλες αρνιού με κύμινο", "honey_grilled_chicken": "Κοτό<PERSON><PERSON>υλο στη σχάρα με μέλι", "garlic_eggplant": "Μελιτ<PERSON><PERSON><PERSON><PERSON>ς με σκόρδο", "portuguese_egg_tart": "Πορτογαλική τάρτα με αυγά", "creme_brulee": "Κρέμα μπριγιέ", "cocoa_chips": "Τσί<PERSON>ς κακάο", "butter_cookies": "Μπισκότα βουτύρου", "chiffon_cake": "Κέικ σιφόν", "puff_pastry": "Φύλλο κρούστας", "red_bean_bread": "Ψωμί με κόκκινα φασόλια", "milk_toast": "Ψωμί με γάλα"}}}, "sensor": {"air_quality": {"name": "Ποιότητα αέρα", "state": {"excellent": "Εξαιρετική", "good": "Καλή", "moderate": "Μέτρια", "poor": "Κακή", "severe": "Σοβαρή"}}, "status": {"name": "Κατάσταση", "state": {"available": "Διαθέσιμο", "plugged_in": "Συνδεδεμένο", "fault_unplugged": "Σφάλμα (αποσυνδεδεμένο)", "charging": "Φόρτιση", "discharging": "Εκφόρτιση", "waiting": "Αναμονή", "charged": "Φορτισμένο", "fault": "Σφάλμα", "paused": "Παύση", "waiting_for_authorization": "Αναμονή για έγκριση", "standby": "Αναμονή", "heating": "Θέρμανση", "cooling": "Ψύξη", "keeping_warm": "Διατήρηση θερμοκρασίας", "no_water": "<PERSON><PERSON><PERSON><PERSON><PERSON> νερό", "boiling": "Βράσιμο", "reserve_only": "Μόνο κράτηση", "unknown": "Άγνωστο", "idle": "Αδράνεια", "auto": "Αυτόματο", "manual": "Χ<PERSON><PERSON>ρ<PERSON><PERSON><PERSON>νητο", "rain_delay": "Καθυστέρηση λόγω βροχής", "off": "Απενεργοποίηση", "on": "Ενεργοποίηση", "cooking": "Μαγείρεμα", "done": "Ολοκληρώθηκε", "door_open": "Ανοιχτή πόρτα", "setting": "Ρύθμιση", "pre_heating": "Προθέρμανση", "scheduled": "Προγραμματισμένο", "at_temperature": "Στη θερμοκρασία", "done_stage_1": "Ολοκληρώθηκε στάδιο 1", "done_stage_2": "Ολοκληρώθηκε στάδιο 2", "done_stage_3": "Ολοκληρώθηκε στάδιο 3", "done_stage_4": "Ολοκληρώθηκε στάδιο 4", "done_stage_5": "Ολοκληρώθηκε στάδιο 5", "done_stage_6": "Ολο<PERSON>ληρώθηκε στάδιο 6", "done_stage_7": "Ολοκληρώθηκε στάδιο 7", "done_stage_8": "Ολοκληρώθηκε στάδιο 8", "done_stage_9": "Ολοκληρώθηκε στάδιο 9", "done_stage_10": "Ολοκληρώθηκε στάδιο 10", "no_food": "<PERSON><PERSON><PERSON><PERSON><PERSON> φαγητό", "jammed": "Κολλημένο", "blocked": "Αποκλεισμένο", "feeding": "Τροφοδοσία", "feeding_complete": "Τροφοδο<PERSON>ία ολοκληρώθηκε", "caking": "Πήξιμο", "cleaning": "Καθαρισμός", "sleep": "Αναμονή", "sterilizing": "Αποστείρωση", "deodorizing": "Αποσμητικό", "occupied": "Κατειλημμένο", "normal": "Κανονικό", "low": "Χαμηλό", "high": "Υψηλό", "unwashed": "Ανεπεξέργαστο", "pre_washing": "Προπλύση", "washing": "Πλύση", "rinsing": "Ξέβγαλμα", "drying": "Στέγνωμα", "air_purging": "Αερισμός", "anti_freeze": "Αντιπαγωτικό", "close": "Κλείσιμο", "monitor": "Παρα<PERSON><PERSON>λούθηση", "working": "Λειτουργία", "warning": "Προειδοποίηση", "starting": "Εκκίνηση", "emptying": "Αδειάζει", "resetting": "Επαναφορά", "reverse": "Αντίστροφο", "full": "<PERSON>λ<PERSON><PERSON><PERSON><PERSON>", "empty": "Άδειο", "missing": "Ελλιπές", "formatting": "Μορφοποίηση", "unformatted": "Μη μορφοποιημένο"}}, "time_remaining": {"name": "<PERSON>ρ<PERSON><PERSON><PERSON> που απομένει"}, "time_remaining_x": {"name": "<PERSON>ρ<PERSON><PERSON><PERSON> που απομένει {x}"}, "cooking_status": {"name": "Κατάσταση", "state": {"wait": "Αναμονή για μαγείρεμα", "reservation": "Κράτηση", "cooking": "Σε εξέλιξη", "cancel": "Ακύρωση μαγειρέματος", "done": "Ολοκληρώθηκε", "pause": "Παύση μαγειρέματος"}}, "water_level": {"name": "Επίπεδο νερού", "state": {"full": "<PERSON>λ<PERSON><PERSON><PERSON><PERSON>", "high": "Υψηλό", "medium": "Μεσαίο", "low": "Χαμηλό", "empty": "Άδειο"}}, "energy_produced": {"name": "Ενέργεια που παράχθηκε"}, "energy_consumed": {"name": "Ενέργεια που καταναλώθηκε"}, "energy_produced_x": {"name": "Ενέργεια που παράχθηκε {x}"}, "energy_consumed_x": {"name": "Ενέργεια που καταναλώθηκε {x}"}, "current_x": {"name": "Ρεύμα {x}"}, "voltage_x": {"name": "Τάση {x}"}, "power_x": {"name": "Ισχύς {x}"}}, "switch": {"anti_frost": {"name": "Απόψυξη"}, "evaporator_cleaning": {"name": "Καθα<PERSON>ισ<PERSON><PERSON>ς αποστακτήρα"}, "ionizer": {"name": "Ιονιστής"}, "keytone": {"name": "Ήχος κουμπιών"}, "outlet_x": {"name": "Πρίζα {x}"}, "sleep": {"name": "Αναμονή"}, "switch_x": {"name": "Διακόπτης {x}"}, "electrolytic_sterilization": {"name": "Ηλεκτρολυτική αποστείρωση"}, "uv_sterilization": {"name": "Αποστείρωση UV"}}, "text": {"scene": {"name": "Σκηνή"}}, "time": {"timer": {"name": "Χρονο<PERSON><PERSON><PERSON>κ<PERSON>πτης"}, "timer_x": {"name": "Χρονοδιακ<PERSON>πτης {x}"}}, "valve": {"valve_x": {"name": "Βαλβίδα {x}"}}, "water_heater": {"water_air": {"name": "Θερμοσίφωνας"}, "kettle": {"name": "Βραστ<PERSON><PERSON><PERSON>ς"}}}}