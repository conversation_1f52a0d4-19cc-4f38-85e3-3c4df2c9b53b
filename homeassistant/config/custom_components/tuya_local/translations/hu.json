{"title": "Tuya Local", "config": {"step": {"user": {"title": "Állítsd be a helyi Tuya eszközöd", "description": "Az eszközök manuálisan vagy fel<PERSON> is hozzáadhatók a SmartLife alkalmazás segítségével.", "data": {"setup_mode": "Beállítás választás:"}}, "cloud": {"title": "Jelentkezzen be Tuyába", "description": "Adja meg SmartLife vagy Tuya felhasználói kódját.\n\nEzt a kódot a SmartLife alkalmazásban vagy a Tuya alkalmazásban találja meg a **Beállítások** > **Fiók és biztonság** képernyőn", "data": {"user_code": "Felhasználói kód:"}}, "scan": {"title": "Töltse ki a bejelentkezést", "description": "Használja a SmartLife alkalmazást vagy a Tuya alkalmazást a következő QR-kód beolvasásához a bejelentkezés befejezéséhez.\n\nHa befejezte ezt a lépést az alkalmazásban"}, "choose_device": {"title": "Válassza ki a hozzáadni kívánt eszközt", "description": "Kérjük", "data": {"device_id": "Válasszon eszközt:", "hub_id": "<PERSON><PERSON><PERSON><PERSON> átjárót:"}}, "search": {"title": "Keresse meg az eszköz IP-címét", "description": "A Tuya felhő nem biztosít helyi IP-címeket"}, "local": {"title": "Állítsd be a helyi Tuya eszközöd", "description": "[Kövesd ezeket az utasít<PERSON>t, hogy megtaláld az eszköz azonosítóját (device_id) és a helyi kulcsot (local_key).](https://github.com/make-all/tuya-local#finding-your-device-id-and-local-key)", "data": {"host": "IP cím vagy <PERSON>", "or device_id of gateway": "<PERSON><PERSON><PERSON><PERSON><PERSON> eszköz azonosítója (device_id átj<PERSON><PERSON><PERSON> kereszt<PERSON>l használt eszközök esetén)", "device_id": "Eszköz <PERSON> (device_id, vagy <PERSON> device_id)", "local_key": "<PERSON><PERSON><PERSON> (local_key)", "protocol_version": "Protokol verzió (hagyd auto-n, ha nem tudod)", "poll_only": "Csak olvasás (pr<PERSON><PERSON><PERSON><PERSON> ki e<PERSON>t, ha az eszköz nem működik rendesen)", "device_cid": "Aleszköz node_id vagy uuid (átjárón keresztül használt eszközök esetén)"}}, "select_type": {"title": "Válaszd ki az eszköz típusát", "description": "Válaszd ki azt a típust, amelyik legjobban illik az eszközödhöz", "data": {"type": "Eszköz típus"}}, "choose_entities": {"title": "Eszköz részletei", "description": "Válasz egy nevet az eszköznek", "data": {"name": "Név"}}}, "abort": {"already_configured": "Ezzel az azonosítóval már lett hozzáadva eszköz.", "not_supported": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ez az eszköz nem támogatott.", "no_devices": "<PERSON><PERSON> a fiókhoz nem regisztrált eszköz."}, "error": {"connection": "A megadott adatokkal nem sikerült kapcsolódni az eszközhöz. Lehet ideiglenes a probléma, vagy nem megfelelőek a megadott adatok.", "does_not_need_hub": "Az eszköznek nincs szüksége átjáróra", "needs_hub": "Az eszköznek átjáróra van szüksége"}}, "selector": {"setup_mode": {"options": {"cloud": "SmartLife felhő által támogatott eszközbeállítás.", "manual": "Adja meg manuális<PERSON> az eszköz csatlakozási adatait.", "cloud_fresh_login": "SmartLife felhő által támogatott eszközbeállítás, friss be<PERSON>."}}}, "options": {"step": {"user": {"title": "Állítsd be a helyi Tuya eszközödet", "description": "[Kövesd ezeket az utasításokat, hogy megtal<PERSON>lt a helyi kul<PERSON>ot (local_key).](https://github.com/make-all/tuya-local#finding-your-device-id-and-local-key)", "data": {"host": "IP cím vagy <PERSON>", "local_key": "<PERSON><PERSON><PERSON> (local_key)", "protocol_version": "Protokol verzió (hagyd auto-n, ha nem tudod)", "poll_only": "Csak olvasás (pr<PERSON><PERSON><PERSON><PERSON> ki e<PERSON>t, ha az eszköz nem működik rendesen)"}}}, "error": {"connection": "A megadott adatokkal nem sikerült kapcsolódni az eszközhöz. Lehet ideiglenes a probléma, vagy nem megfelelőek a megadott adatok."}, "abort": {"not_supported": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ez az eszköz nem támogatott."}}, "entity": {"binary_sensor": {"direction": {"name": "<PERSON><PERSON><PERSON><PERSON>", "state": {"off": "Bejö<PERSON><PERSON>", "on": "<PERSON><PERSON><PERSON>"}}, "defrost": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "state": {"off": "<PERSON><PERSON><PERSON><PERSON>", "on": "Leolvasztás"}}, "tank_empty": {"name": "<PERSON><PERSON><PERSON><PERSON>", "state": {"off": "OK", "on": "Alacsony"}}, "tank_full": {"name": "<PERSON><PERSON><PERSON><PERSON>", "state": {"off": "OK", "on": "<PERSON><PERSON><PERSON>"}}, "wake": {"name": "Ébresztés", "state": {"off": "<PERSON><PERSON><PERSON><PERSON>", "on": "Ébresztés"}}, "casdon_oven_fault": {"state_attributes": {"description": {"state": {"e1": "E1: <PERSON><PERSON><PERSON> védelem (túllépi a 290°C-ot)", "e2": "E2: <PERSON><PERSON><PERSON> védelem (túllépi a 290°C-ot)", "e3": "E3: <PERSON><PERSON><PERSON> hőmérséklet védelem (35°C alatt)", "e4": "E4: Elpárologtató tálcán alacsony hőmérséklet védelem (35°C alatt)", "e5": "E5: <PERSON><PERSON><PERSON> c<PERSON> hiba (Nyitott áramkör)", "e6": "E6: <PERSON><PERSON><PERSON> hiba (Rövidzárlat)", "e7": "E7: Kijelző panel kommunikációs hiba", "e8": "E8: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (CN7) v<PERSON><PERSON> tálcán <PERSON>z<PERSON>raz (CN3)"}}}}}, "button": {"factory_reset": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "filter_reset": {"name": "Szűrő visszaállítás"}}, "climate": {"aircon_extra": {"name": "Légkondicionáló", "state_attributes": {"fan_mode": {"state": {"health": "Egészséges", "medhigh": "Közepesen magas", "medlow": "Közepes-alacsony", "natural": "Természetes", "quiet": "Csendes", "sleep": "Alvás", "strong": "<PERSON><PERSON><PERSON><PERSON>"}}, "swing_mode": {"state": {"topmost": "Legmagasabb", "top": "<PERSON><PERSON>", "middle": "Középen", "down": "<PERSON><PERSON>", "downmost": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}}, "thermostat": {"name": "Hő<PERSON><PERSON><PERSON><PERSON><PERSON>", "state_attributes": {"fan_mode": {"state": {"cycle": "<PERSON><PERSON><PERSON>"}}, "preset_mode": {"state": {"manual": "<PERSON><PERSON><PERSON><PERSON>", "program": "<PERSON><PERSON><PERSON>", "temp_override": "Ideiglenes felülbírálás", "perm_override": "<PERSON><PERSON><PERSON>"}}}}, "combo_floor": {"state_attributes": {"preset_mode": {"state": {"cool": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fan_only": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heat": "Meleg levegő", "floor_cool": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "floor_heat": "<PERSON><PERSON>"}}}}, "swing_as_powerlevel": {"name": "Fűtő", "state_attributes": {"swing_mode": {"name": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t", "state": {"stop": "Stop", "auto": "Auto"}}}}, "pool_heatpump": {"state_attributes": {"preset_mode": {"state": {"smart_heat": "<PERSON><PERSON>", "quick_heat": "<PERSON><PERSON><PERSON>", "quiet_heat": "Csendes fűtés", "smart_cool": "<PERSON><PERSON>", "quick_cool": "g<PERSON><PERSON>", "quiet_cool": "<PERSON><PERSON><PERSON>", "auto": "Auto", "smart": "<PERSON><PERSON>", "quick": "Gyors", "quiet": "Csendes"}}}}, "heater": {"name": "Fűtő"}, "thermo_switch": {"state_attributes": {"hvac_mode": {"state": {"fan_only": "Szünet"}}}}, "oven": {"state_attributes": {"preset_mode": {"state": {"healthy_steam": "Egészséges gőz", "fresh_steam": "<PERSON><PERSON>", "high_temp_steam": "Magas hőmérsékletű gőz", "stew": "<PERSON><PERSON><PERSON><PERSON>", "bake_up_and_down": "Sütés fel és le", "bbq": "BBQ", "bottom_hot_air": "<PERSON>só <PERSON>ó leveg<PERSON>", "on_strong_roast": "<PERSON><PERSON><PERSON><PERSON>", "3d_hot_air": "3D forró levegő", "air_fry": "Légfritőzés", "steam_frying": "Gőzpirítás", "one_click_bread": "<PERSON><PERSON> katti<PERSON> ken<PERSON>", "quick_heat": "<PERSON><PERSON><PERSON>", "keep_warm": "Melegen tartás", "unfreeze": "Felolvasztás", "fermentation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "descale": "Vízkőtelenítés", "local_recipes": "<PERSON><PERSON><PERSON> receptek", "drying": "Szárítás", "custom": "<PERSON><PERSON><PERSON><PERSON>", "low_steaming": "Alacsony Steaming", "medium_steaming": "Közepes Steaming", "high_steaming": "Magas Steaming"}}}}}, "humidifier": {"dehumidifier": {"state_attributes": {"mode": {"state": {"laundry": "<PERSON><PERSON><PERSON><PERSON>", "purify": "Tisztítás"}}}}}, "fan": {"aroma_diffuser": {"name": "Aroma diffúzor", "state_attributes": {"preset_mode": {"state": {"low": "Alacsony", "medium": "Közepes", "high": "Magas", "continuous": "<PERSON><PERSON><PERSON><PERSON>", "intermittent": "Időszak<PERSON>", "timer": "Időzítő"}}}}, "dehumidifier": {"name": "Párátlanító", "state_attributes": {"preset_mode": {"state": {"purify": "Tisztítás", "dehumidify": "Párátlanítás"}}}}, "fan_with_presets": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "state_attributes": {"preset_mode": {"state": {"normal": "<PERSON><PERSON><PERSON><PERSON>", "nature": "Természetes", "sleep": "Alvás", "baby": "Baba", "fresh": "<PERSON><PERSON>", "smart": "<PERSON><PERSON>", "strong": "<PERSON><PERSON><PERSON><PERSON>", "custom": "<PERSON><PERSON><PERSON><PERSON>", "high": "Magas", "medium": "Közepes", "low": "Alacsony", "displayoff": "Kijelző kikapcsolva", "off": "<PERSON>"}}}}, "ventilation": {"name": "Szellőzés", "state_attributes": {"preset_mode": {"state": {"fresh": "<PERSON>iss levegő", "circulate": "Keringetés", "sleep": "Alvás", "auto": "Auto", "eco": "Eco", "anti-condensation": "<PERSON><PERSON><PERSON>", "extractor": "<PERSON><PERSON><PERSON>", "heat_recovery": "Hővisszanyerés", "timer": "Időzítő", "on": "Be", "off": "<PERSON>"}}}}}, "light": {"backlight": {"name": "Háttérvilágítás"}, "display": {"name": "Kijelző"}, "embers": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "flame": {"name": "<PERSON><PERSON><PERSON>"}, "indicator": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "laser": {"name": "<PERSON><PERSON><PERSON>"}, "logs": {"name": "<PERSON><PERSON><PERSON>"}, "nightlight": {"name": "<PERSON><PERSON><PERSON><PERSON>"}}, "lock": {"child_lock": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "number": {"timer": {"name": "Időzítő"}, "timer_x": {"name": "Időzítő {x}"}}, "select": {"currency": {"name": "Pénznem", "state": {"usd": "USD", "eur": "EUR", "cny": "CNY", "cad": "CAD", "gbp": "GBP"}}, "heat_pump_mode": {"name": "Hőszivattyú mód", "state": {"heat": "<PERSON><PERSON><PERSON><PERSON>", "cool": "<PERSON><PERSON><PERSON><PERSON>", "auto": "Auto", "floor_heat": "Padlófűtés", "off": "<PERSON>", "hotwater": "Meleg víz", "hotwater_cool": "Meleg víz és hűtés", "hotwater_heat": "Meleg víz és fűtés", "hotwater_auto": "Meleg víz és automatikus", "hotwater_floor_heat": "Meleg víz és padlófűtés"}}, "initial_state": {"name": "<PERSON><PERSON><PERSON><PERSON>", "state": {"off": "<PERSON>", "on": "Be", "memory": "Memória"}}, "kettle_mode": {"name": "Vízforraló mód", "state": {"off": "<PERSON>", "heat": "<PERSON><PERSON><PERSON><PERSON>", "boil": "Forralás", "quick_heat": "<PERSON><PERSON><PERSON>", "quick_boil": "Gyors forralás", "keep_warm": "Melegen tartás", "custom": "<PERSON><PERSON><PERSON><PERSON>", "dechlorinate": "Klórmentesítés", "black_tea": "Fekete tea", "green_tea": "Zöld tea", "coffee": "<PERSON><PERSON><PERSON><PERSON>", "honey_water": "Mézes víz", "infant_formula": "Csecsem<PERSON><PERSON>", "white_tea": "Fehér tea", "oolong_tea": "Oolong tea"}}, "language": {"name": "Nyelv", "state": {"chinese": "中文", "chinese_traditional": "中文(繁體)", "english": "English", "french": "Français", "german": "De<PERSON>ch", "italian": "Italiano", "japanese": "日本語", "korean": "한국어", "latin": "Lingua Latina", "portuguese": "Português", "russian": "Русский", "spanish": "Español", "turkish": "Türkçe"}}, "light_mode": {"name": "<PERSON><PERSON><PERSON>ó<PERSON>", "state": {"off": "<PERSON>", "on": "Be", "state": "<PERSON><PERSON><PERSON>", "locator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "mopping": {"name": "<PERSON><PERSON><PERSON><PERSON>", "state": {"off": "<PERSON>", "auto": "Auto", "low": "Alacsony", "medium": "Közepes", "high": "Magas"}}, "recipe": {"name": "Recept", "state": {"pizza": "Pizza", "fries": "<PERSON><PERSON><PERSON> krumpli", "chicken": "<PERSON><PERSON><PERSON>", "shrimp": "<PERSON><PERSON>", "fish": "<PERSON>", "chicken_drumsticks": "C<PERSON><PERSON><PERSON>", "vegetables": "Zöldségek", "desserts": "<PERSON><PERSON><PERSON>", "none": "<PERSON><PERSON><PERSON>", "chicken_wings": "Csirkeszárnyak", "steak": "Steak", "onion_rings": "Hagymakarikák", "bacon": "<PERSON><PERSON><PERSON><PERSON>", "cake": "Sü<PERSON>ény", "bread": "<PERSON><PERSON><PERSON><PERSON>", "toast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sausage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dry_fruit": "Szárított g<PERSON>ü<PERSON>", "custom": "<PERSON><PERSON><PERSON><PERSON>", "cloud_recipe": "<PERSON><PERSON><PERSON>ő recept", "default": "Alap<PERSON><PERSON><PERSON><PERSON><PERSON>", "keep_warm": "Melegen tartás", "preheat": "Előmelegítés"}}, "scene": {"name": "<PERSON><PERSON><PERSON>", "state": {"relax": "Pihenés", "movie": "Film", "party": "<PERSON><PERSON>", "romantic": "<PERSON><PERSON><PERSON>", "night": "<PERSON><PERSON><PERSON><PERSON>", "morning": "<PERSON><PERSON>", "working": "<PERSON><PERSON>", "leisure": "Szabadidő", "vacation": "Szabadság", "reading": "<PERSON><PERSON><PERSON><PERSON>", "twinkle": "Csillogás", "gaming": "<PERSON><PERSON><PERSON><PERSON>", "none": "<PERSON><PERSON><PERSON>"}}, "timer": {"name": "Időzítő", "state": {"cancel": "<PERSON><PERSON><PERSON><PERSON>", "continuous": "<PERSON><PERSON><PERSON><PERSON>", "30s": "30 másodperc", "1m": "1 perc", "2m": "2 perc", "5m": "5 perc", "10m": "10 perc", "20m": "20 perc", "30m": "30 perc", "40m": "40 perc", "1h": "1 óra", "1h30m": "1 óra 30 perc", "2h": "2 óra", "2h30m": "2 óra 30 perc", "3h": "3 óra", "3h30m": "3 óra 30 perc", "4h": "4 óra", "4h30m": "4 óra 30 perc", "5h": "5 óra", "5h30m": "5 óra 30 perc", "6h": "6 óra", "6h30m": "6 óra 30 perc", "7h": "7 óra", "7h30m": "7 óra 30 perc", "8h": "8 óra", "8h30m": "8 óra 30 perc", "9h": "9 óra", "9h30m": "9 óra 30 perc", "10h": "10 óra", "11h": "11 óra", "12h": "12 óra", "13h": "13 óra", "14h": "14 óra", "15h": "15 óra", "16h": "16 óra", "17h": "17 óra", "18h": "18 óra", "19h": "19 óra", "20h": "20 óra", "21h": "21 óra", "22h": "22 óra", "23h": "23 óra", "24h": "24 óra", "36h": "36 óra", "48h": "48 óra", "72h": "72 óra", "1d": "1 nap", "2d": "2 nap", "3d": "3 nap", "4d": "4 nap", "5d": "5 nap", "6d": "6 nap", "7d": "7 nap"}}, "temperature_unit": {"name": "Hőmérséklet mértékegysége", "state": {"celsius": "<PERSON><PERSON><PERSON>", "fahrenheit": "Fahrenheit"}}, "oven_built_in_recipe": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> recept", "state": {"none": "<PERSON><PERSON><PERSON>", "steamed_egg_with_okra": "Gőzölt to<PERSON>", "steamed_sea_bass": "Gőzölt tengeri sügér", "steamed_prawns": "<PERSON><PERSON><PERSON><PERSON><PERSON> garn<PERSON>", "handmade_steamed_bread": "Kézzel készített gőzölt kenyér", "fan_steamed_baby_vegetables": "Ventilátoros gőzölt zöldségek", "braised_pork": "<PERSON><PERSON><PERSON><PERSON>", "snow_fungus_and_bird_s_nest": "<PERSON><PERSON> gomba és madárfészek", "crab_pot": "Rákf<PERSON><PERSON><PERSON> edény", "potato_ribs": "<PERSON><PERSON><PERSON> bord<PERSON>", "coconut_chicken_soup": "Kókuszty<PERSON><PERSON><PERSON> leves", "snack_platter": "Snack tál", "chicken_skewers": "Csirke n<PERSON>á<PERSON>", "roasted_pork_knuckle": "<PERSON><PERSON><PERSON>", "dried_lemon": "Szárított citrom", "pork_jerky": "Sertéshús rágcsálnivaló", "crispy_hairtail": "<PERSON><PERSON><PERSON><PERSON>", "spicy_grilled_fish": "Fűszeres grillezett hal", "roasted_sweet_potatoes": "Sült édesburgonya", "roasted_chicken_wings": "<PERSON><PERSON><PERSON> csir<PERSON>zárnyak", "cumin_lamb_chops": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "honey_grilled_chicken": "<PERSON><PERSON><PERSON> grille<PERSON> c<PERSON>ke", "garlic_eggplant": "Fokhagymás padlizsán", "portuguese_egg_tart": "Portug<PERSON><PERSON>", "creme_brulee": "Crème brûlée", "cocoa_chips": "Kakaós chips", "butter_cookies": "Vajas k<PERSON>", "chiffon_cake": "Chiffon torta", "puff_pastry": "<PERSON><PERSON>", "red_bean_bread": "<PERSON><PERSON><PERSON><PERSON><PERSON> babos ken<PERSON>", "milk_toast": "<PERSON><PERSON><PERSON>"}}}, "sensor": {"air_quality": {"name": "Levegő minőség", "state": {"excellent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "good": "<PERSON><PERSON>", "moderate": "Közepes", "poor": "<PERSON><PERSON>", "severe": "<PERSON><PERSON><PERSON><PERSON>"}}, "status": {"name": "<PERSON><PERSON><PERSON>", "state": {"available": "Elérhető", "plugged_in": "Csatlakoztatva", "fault_unplugged": "<PERSON><PERSON> (nincs <PERSON>)", "charging": "Töltés", "discharging": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "waiting": "Várakozás", "charged": "Töltve", "fault": "Hiba", "paused": "Szüneteltetve", "waiting_for_authorization": "Várakozás az engedélyezésre", "standby": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heating": "<PERSON><PERSON><PERSON><PERSON>", "cooling": "<PERSON><PERSON><PERSON><PERSON>", "keeping_warm": "Melegen tartás", "no_water": "Nincs víz", "boiling": "<PERSON><PERSON><PERSON>", "reserve_only": "Csak foglalás", "unknown": "Ismeretlen", "idle": "Té<PERSON>n", "auto": "Auto", "manual": "<PERSON><PERSON><PERSON><PERSON>", "rain_delay": "Eső késleltetés", "off": "<PERSON>", "on": "Be", "cooking": "Főzés", "done": "<PERSON><PERSON><PERSON>", "door_open": "<PERSON><PERSON><PERSON><PERSON>", "setting": "Be<PERSON>llít<PERSON>", "pre_heating": "Előmelegítés", "scheduled": "<PERSON><PERSON><PERSON><PERSON>", "at_temperature": "Hő<PERSON><PERSON><PERSON><PERSON><PERSON>", "done_stage_1": "Kész 1. szakasz", "done_stage_2": "Kész 2. szakasz", "done_stage_3": "Kész 3. szakasz", "done_stage_4": "Kész 4. szakasz", "done_stage_5": "Kész 5. szakasz", "done_stage_6": "Kész 6. szak<PERSON>z", "done_stage_7": "Kész 7. szakasz", "done_stage_8": "Kész 8. szakasz", "done_stage_9": "Kész 9. szakasz", "done_stage_10": "Kész 10. szakasz", "no_food": "<PERSON><PERSON><PERSON>", "jammed": "<PERSON><PERSON><PERSON><PERSON>", "blocked": "Blokkolt", "feeding": "Etetés", "feeding_complete": "Etetés be<PERSON>", "caking": "Tortázás", "cleaning": "Tisztítás", "sleep": "Alvás", "sterilizing": "Szterilizálás", "deodorizing": "Légfrissítés", "occupied": "<PERSON><PERSON><PERSON><PERSON>", "normal": "<PERSON><PERSON><PERSON><PERSON>", "low": "Alacsony", "high": "Magas", "unwashed": "<PERSON><PERSON> mosott", "pre_washing": "Előmosás", "washing": "Mo<PERSON>ás", "rinsing": "Öblítés", "drying": "Szárítás", "air_purging": "Levegő tisztítás", "anti_freeze": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "close": "Bezárás", "monitor": "<PERSON><PERSON><PERSON><PERSON>", "working": "Működés", "warning": "Figyelmeztetés", "starting": "Indítás", "emptying": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "resetting": "Visszaállítás", "reverse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "full": "<PERSON><PERSON><PERSON>", "empty": "Üres", "missing": "Hiányzik", "formatting": "Formázás", "unformatted": "<PERSON><PERSON>"}}, "time_remaining": {"name": "Hátralevő idő"}, "time_remaining_x": {"name": "Hátralevő idő {x}"}, "cooking_status": {"name": "Főzési <PERSON>", "state": {"wait": "Várakozás főzésre", "reservation": "Foglalásban", "cooking": "Főzés folyamatban", "cancel": "Főzés megszakítva", "done": "Főzés befejezve", "pause": "Főzés szüneteltetve"}}, "water_level": {"name": "Vízszint", "state": {"full": "<PERSON><PERSON><PERSON>", "empty": "Üres", "medium": "Közepes", "low": "Alacsony", "high": "Magas"}}, "energy_produced": {"name": "Termelt energia"}, "energy_consumed": {"name": "Fogyasztott energia"}, "energy_produced_x": {"name": "Termelt energia {x}"}, "energy_consumed_x": {"name": "Fogyasztott energia {x}"}, "current_x": {"name": "<PERSON><PERSON> {x}"}, "voltage_x": {"name": "Feszültség {x}"}, "power_x": {"name": "Teljesítmény {x}"}}, "switch": {"anti_frost": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "evaporator_cleaning": {"name": "Elpárologtató tisztítása"}, "ionizer": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "keytone": {"name": "<PERSON><PERSON><PERSON>"}, "outlet_x": {"name": "Csatlakozó {x}"}, "sleep": {"name": "Alvás"}, "switch_x": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> {x}"}, "electrolytic_sterilization": {"name": "Elektrolitikus szteriliz<PERSON>ás"}, "uv_sterilization": {"name": "UV sterilizálás"}}, "text": {"scene": {"name": "<PERSON><PERSON><PERSON>"}}, "time": {"timer": {"name": "Időzítő"}, "timer_x": {"name": "Időzítő {x}"}}, "valve": {"valve_x": {"name": "Szelep {x}"}}, "water_heater": {"water_air": {"name": "Vízmelegítő"}, "kettle": {"name": "Vízforraló"}}}}