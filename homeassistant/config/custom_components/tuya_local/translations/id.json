{"title": "Tuya Local", "config": {"step": {"user": {"title": "Konfigurasikan perangkat Tuya Local Anda", "description": "Perangkat dapat ditambahkan secara manual atau dibantu cloud dengan bantuan aplikasi SmartLife.", "data": {"setup_mode": "<PERSON><PERSON><PERSON> pen<PERSON>:"}}, "cloud": {"title": "<PERSON><PERSON><PERSON> ke Tuya", "description": "Masukkan kode pengguna SmartLife atau Tuya Anda.\n\n<PERSON>a dapat menemukan kode ini di aplikasi SmartLife atau aplikasi Tuya di layar **Setelan** > **Akun dan <PERSON>**", "data": {"user_code": "Kode pengguna:"}}, "scan": {"title": "<PERSON><PERSON><PERSON><PERSON> login", "description": "Gunakan aplikasi SmartLife atau aplikasi Tuya untuk memindai kode QR berikut untuk menyelesaikan login.\n\nLanjutkan ke langkah berikutnya setelah Anda menyelesaikan langkah ini di aplikasi."}, "choose_device": {"title": "<PERSON><PERSON><PERSON> perangkat yang akan ditambahkan", "description": "<PERSON><PERSON>an pilih perangkat yang akan ditambahkan dari daftar drop-down pertama. ", "data": {"device_id": "<PERSON><PERSON><PERSON>:", "hub_id": "<PERSON><PERSON><PERSON> gerbang:"}}, "search": {"title": "Temukan alamat IP perangkat", "description": "Tuya cloud tidak menyediakan alamat IP lokal jadi sekarang kami harus mencari di jaringan lokal Anda untuk menemukan perangkat. "}, "local": {"title": "Konfigurasikan perangkat Tuya Local Anda", "description": "[<PERSON><PERSON><PERSON> petunjuk ini untuk menemukan ID perangkat dan kunci lokal.](https://github.com/make-all/tuya-local#finding-your-device-id-and-local-key)", "data": {"host": "Alamat IP atau nama hos", "device_id": "ID Perangkat (device_id, atau device_id dari gateway untuk perangkat yang tersambung via gateway)", "local_key": "<PERSON><PERSON><PERSON> lokal (local_key)", "protocol_version": "Versi protokol (coba otomatis bila tidak diketahui)", "poll_only": "Hanya poll (Coba ini bila perangkat Anda tidak berfungsi secara penuh)", "device_cid": "node_id atau uuid sub perangkat (untuk perangkat yang tersambung via gateway)"}}, "select_type": {"title": "<PERSON><PERSON><PERSON> tipe perangkat", "description": "<PERSON><PERSON>h tipe yang cocok dengan perangkat Anda", "data": {"type": "<PERSON><PERSON><PERSON>"}}, "choose_entities": {"title": "Detail per<PERSON>kat", "description": "Buat nama untuk perangkat ini", "data": {"name": "<PERSON><PERSON>"}}}, "abort": {"already_configured": "Perangkat dengan ID tersebut pernah ditambahkan.", "not_supported": "<PERSON><PERSON>, perangkat ini belum didukung.", "no_devices": "Tidak dapat menemukan perangkat yang tidak terdaftar untuk akun tersebut."}, "error": {"connection": "Tidak dapat terkoneksi ke perangkat tersebut. Bisa jadi sementara, atau ada kes<PERSON>han.", "does_not_need_hub": "Perangkat tidak memerlukan gateway dan satu telah dipilih. ", "needs_hub": "Perangkat memerlukan gateway dan tidak ada yang dipilih."}}, "selector": {"setup_mode": {"options": {"cloud": "Penyiapan perangkat berbantuan cloud SmartLife.", "manual": "Berikan informasi koneksi perangkat secara manual.", "cloud_fresh_login": "Penyiapan perangkat berbantuan cloud SmartLife dengan login baru."}}}, "options": {"step": {"user": {"title": "Konfigurasikan perangkat Tuya Local Anda", "description": "[<PERSON><PERSON><PERSON> petunjuk ini untuk menemukan ID perangkat dan kunci lokal.](https://github.com/make-all/tuya-local#finding-your-device-id-and-local-key)", "data": {"host": "Alamat IP atau nama hos", "local_key": "<PERSON><PERSON><PERSON> lokal (local_key)", "protocol_version": "Versi protokol (coba otomatis bila tidak diketahui)", "poll_only": "Hanya poll (Coba ini bila perangkat Anda tidak berfungsi secara penuh)"}}}, "error": {"connection": "Tidak dapat terkoneksi ke perangkat tersebut. Bisa jadi sementara, atau ada kes<PERSON>han."}, "abort": {"not_supported": "<PERSON><PERSON>, perangkat ini belum didukung."}}, "entity": {"binary_sensor": {"direction": {"name": "<PERSON><PERSON>", "state": {"off": "<PERSON><PERSON><PERSON>", "on": "<PERSON><PERSON><PERSON>"}}, "defrost": {"name": "<PERSON><PERSON><PERSON><PERSON> beku", "state": {"off": "Normal", "on": "Pencairan bunga es"}}, "tank_empty": {"name": "<PERSON><PERSON>", "state": {"off": "<PERSON>e", "on": "Rendah"}}, "tank_full": {"name": "<PERSON><PERSON>", "state": {"off": "<PERSON>e", "on": "<PERSON><PERSON>"}}, "wake": {"name": "<PERSON><PERSON>", "state": {"off": "<PERSON><PERSON><PERSON>", "on": "<PERSON><PERSON>"}}, "casdon_oven_fault": {"state_attributes": {"description": {"state": {"e1": "E1: <PERSON><PERSON><PERSON><PERSON> kele<PERSON>han panas ruang (melebihi 290°C)", "e2": "E2: <PERSON><PERSON><PERSON><PERSON> kelebihan panas nampan penguapan (melebihi 200°C)", "e3": "E3: <PERSON><PERSON><PERSON><PERSON> suhu rendah ruang (di bawah 35°C)", "e4": "E4: <PERSON><PERSON><PERSON><PERSON> suhu rendah nampan pen<PERSON> (di bawah 35°C)", "e5": "E5: <PERSON><PERSON><PERSON> koneksi sensor (Sirkuit terbuka)", "e6": "E6: <PERSON><PERSON> (Korsleting)", "e7": "E7: <PERSON><PERSON><PERSON> komunikasi papan tampilan", "e8": "E8: Tangki air terbuka (CN7) atau Nampan penguapan kering (CN3)"}}}}}, "button": {"factory_reset": {"name": "Reset pabrik"}, "filter_reset": {"name": "Reset filter"}}, "climate": {"aircon_extra": {"name": "AC", "state_attributes": {"fan_mode": {"state": {"health": "<PERSON><PERSON>", "medhigh": "Sedang-Cepat", "medlow": "Sedang-Lambat", "natural": "Natural", "quiet": "<PERSON><PERSON><PERSON>", "sleep": "<PERSON><PERSON><PERSON>", "strong": "<PERSON><PERSON><PERSON>"}}, "swing_mode": {"state": {"topmost": "Paling atas", "top": "Atas", "middle": "Tengah", "down": "<PERSON>wa<PERSON>", "downmost": "<PERSON><PERSON> bawah"}}}}, "thermostat": {"name": "Termostat", "state_attributes": {"fan_mode": {"state": {"cycle": "Siklus"}}, "preset_mode": {"state": {"manual": "Manual", "program": "Program", "temp_override": "<PERSON>bil alih sementara", "perm_override": "<PERSON><PERSON> alih permanen"}}}}, "combo_floor": {"state_attributes": {"preset_mode": {"state": {"cool": "<PERSON><PERSON><PERSON> u<PERSON>a", "fan_only": "<PERSON><PERSON> k<PERSON>", "heat": "<PERSON><PERSON><PERSON>", "floor_cool": "<PERSON><PERSON><PERSON> lantai", "floor_heat": "<PERSON><PERSON><PERSON> la<PERSON>"}}}}, "swing_as_powerlevel": {"name": "<PERSON><PERSON><PERSON>", "state_attributes": {"swing_mode": {"name": "Level Pemanasan", "state": {"stop": "Stop", "auto": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "pool_heatpump": {"state_attributes": {"preset_mode": {"state": {"smart_heat": "Pemanasan pintar", "quick_heat": "Pemanasan cepat", "quiet_heat": "<PERSON><PERSON><PERSON><PERSON> senyap", "smart_cool": "Pendinginan pintar", "quick_cool": "Pendinginan cepat", "quiet_cool": "Pendinginan senyap", "auto": "<PERSON><PERSON><PERSON><PERSON>", "smart": "Pintar", "quick": "Cepat", "quiet": "<PERSON><PERSON><PERSON>"}}}}, "heater": {"name": "<PERSON><PERSON><PERSON>"}, "thermo_switch": {"state_attributes": {"hvac_mode": {"state": {"fan_only": "<PERSON><PERSON>"}}}}, "oven": {"state_attributes": {"preset_mode": {"state": {"healthy_steam": "<PERSON><PERSON> sehat", "fresh_steam": "<PERSON><PERSON> segar", "high_temp_steam": "<PERSON><PERSON> suhu tinggi", "stew": "Rebus", "bake_up_and_down": "Panggang atas dan bawah", "bbq": "BBQ", "bottom_hot_air": "<PERSON><PERSON><PERSON> panas bawah", "on_strong_roast": "Panggang kuat", "3d_hot_air": "Udara panas 3D", "air_fry": "Panggang udara", "steam_frying": "Panggang uap", "one_click_bread": "Roti satu klik", "quick_heat": "Pemanasan cepat", "keep_warm": "Tetap hangat", "unfreeze": "Pencairan", "fermentation": "<PERSON><PERSON><PERSON><PERSON>", "descale": "Descale", "local_recipes": "Resep lokal", "drying": "<PERSON><PERSON><PERSON>", "custom": "Kustom", "low_steaming": "Perebusan rendah", "medium_steaming": "Perebusan sedang", "high_steaming": "Perebusan tinggi"}}}}}, "humidifier": {"dehumidifier": {"state_attributes": {"mode": {"state": {"laundry": "<PERSON><PERSON><PERSON>", "purify": "<PERSON><PERSON><PERSON><PERSON>"}}}}}, "fan": {"aroma_diffuser": {"name": "Pengharum", "state_attributes": {"preset_mode": {"state": {"low": "Rendah", "medium": "Sedang", "high": "Tingg<PERSON>", "continuous": "<PERSON><PERSON> men<PERSON>", "intermittent": "Intermiten", "timer": "Pen<PERSON><PERSON> waktu"}}}}, "dehumidifier": {"name": "Pengering", "state_attributes": {"preset_mode": {"state": {"purify": "<PERSON><PERSON><PERSON><PERSON>", "dehumidify": "Pengering"}}}}, "fan_with_presets": {"name": "<PERSON><PERSON>", "state_attributes": {"preset_mode": {"state": {"normal": "Normal", "nature": "Natural", "sleep": "<PERSON><PERSON><PERSON>", "baby": "Bay<PERSON>", "fresh": "Segar", "smart": "Pintar", "strong": "<PERSON><PERSON><PERSON>", "custom": "Kustom", "high": "Tingg<PERSON>", "medium": "Sedang", "low": "Rendah", "displayoff": "Layar mati", "off": "<PERSON><PERSON>"}}}}, "ventilation": {"name": "Ventila<PERSON>", "state_attributes": {"preset_mode": {"state": {"fresh": "<PERSON><PERSON><PERSON>", "circulate": "<PERSON><PERSON><PERSON><PERSON>", "sleep": "<PERSON><PERSON><PERSON>", "auto": "<PERSON><PERSON><PERSON><PERSON>", "eco": "<PERSON><PERSON>", "anti-condensation": "<PERSON> kondensasi", "extractor": "Ekstraktor", "heat_recovery": "<PERSON><PERSON><PERSON><PERSON> panas", "timer": "Pen<PERSON><PERSON> waktu", "on": "<PERSON><PERSON><PERSON>", "off": "<PERSON><PERSON>"}}}}}, "light": {"backlight": {"name": "<PERSON><PERSON> latar"}, "display": {"name": "Tampilan"}, "embers": {"name": "Bara"}, "flame": {"name": "Api"}, "indicator": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "laser": {"name": "Laser"}, "logs": {"name": "Log"}, "nightlight": {"name": "<PERSON><PERSON><PERSON> malam"}}, "lock": {"child_lock": {"name": "<PERSON><PERSON><PERSON> anak"}}, "number": {"timer": {"name": "Pen<PERSON><PERSON> waktu"}, "timer_x": {"name": "Pengatur waktu {x}"}}, "select": {"currency": {"name": "<PERSON>", "state": {"usd": "USD", "eur": "EUR", "cny": "CNY", "cad": "CAD", "gbp": "GBP"}}, "heat_pump_mode": {"name": "Mode pompa panas", "state": {"heat": "<PERSON><PERSON><PERSON>", "cool": "Pendingin", "auto": "<PERSON><PERSON><PERSON><PERSON>", "floor_heat": "<PERSON><PERSON><PERSON> la<PERSON>", "off": "<PERSON><PERSON>", "hotwater": "Air panas", "hotwater_cool": "Pendingin air panas", "hotwater_heat": "Pemanas air panas", "hotwater_auto": "Otomatis air panas", "hotwater_floor_heat": "Pemanas lantai air panas"}}, "initial_state": {"name": "Status awal", "state": {"off": "<PERSON><PERSON>", "on": "<PERSON><PERSON><PERSON>", "memory": "<PERSON><PERSON><PERSON>"}}, "kettle_mode": {"name": "Mode ketel", "state": {"off": "<PERSON><PERSON>", "heat": "<PERSON><PERSON><PERSON>", "boil": "<PERSON><PERSON><PERSON><PERSON>", "quick_heat": "Pemanasan cepat", "quick_boil": "<PERSON><PERSON><PERSON><PERSON> cepat", "keep_warm": "Tetap hangat", "custom": "Kustom", "dechlorinate": "Deklorinasi", "black_tea": "<PERSON><PERSON> hitam", "green_tea": "<PERSON><PERSON> hijau", "coffee": "<PERSON><PERSON>", "honey_water": "Air madu", "infant_formula": "Susu formula bayi", "white_tea": "<PERSON><PERSON> <PERSON>ih", "oolong_tea": "<PERSON><PERSON> oolong"}}, "language": {"name": "Bahasa", "state": {"chinese": "中文", "chinese_traditional": "中文(繁體)", "english": "English", "french": "Français", "german": "De<PERSON>ch", "italian": "Italiano", "japanese": "日本語", "korean": "한국어", "latin": "Lingua Latina", "portuguese": "Português", "russian": "Русский", "spanish": "Español", "turkish": "Türkçe"}}, "light_mode": {"name": "Mode lampu", "state": {"off": "<PERSON><PERSON>", "on": "<PERSON><PERSON><PERSON>", "state": "Status", "locator": "<PERSON><PERSON>"}}, "mopping": {"name": "Pel", "state": {"off": "<PERSON><PERSON>", "auto": "<PERSON><PERSON><PERSON><PERSON>", "low": "Rendah", "medium": "Sedang", "high": "Tingg<PERSON>"}}, "recipe": {"name": "<PERSON><PERSON><PERSON>", "state": {"pizza": "Pizza", "fries": "<PERSON><PERSON> goreng", "chicken": "<PERSON><PERSON>", "shrimp": "Udang", "fish": "<PERSON><PERSON>", "chicken_drumsticks": "<PERSON><PERSON> ayam", "vegetables": "<PERSON><PERSON><PERSON>", "desserts": "<PERSON><PERSON><PERSON>", "none": "Tidak ada", "chicken_wings": "<PERSON><PERSON> ayam", "steak": "Steak", "onion_rings": "<PERSON><PERSON><PERSON><PERSON> bawang", "bacon": "<PERSON>", "cake": "<PERSON><PERSON>", "bread": "R<PERSON><PERSON>", "toast": "<PERSON><PERSON><PERSON>ang", "sausage": "<PERSON><PERSON>", "dry_fruit": "<PERSON><PERSON><PERSON> kering", "custom": "Kustom", "cloud_recipe": "<PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON><PERSON>", "keep_warm": "Tetap hangat", "preheat": "<PERSON><PERSON><PERSON><PERSON> awal"}}, "scene": {"name": "<PERSON><PERSON>", "state": {"relax": "<PERSON><PERSON><PERSON>", "movie": "Film", "party": "Pesta", "romantic": "<PERSON><PERSON>", "night": "Malam", "morning": "<PERSON><PERSON>", "working": "<PERSON><PERSON><PERSON>", "leisure": "Santai", "vacation": "Liburan", "reading": "Membaca", "twinkle": "<PERSON><PERSON><PERSON>", "gaming": "Bermain game", "none": "Tidak ada"}}, "timer": {"name": "Pen<PERSON><PERSON> waktu", "state": {"cancel": "<PERSON><PERSON>", "continuous": "<PERSON><PERSON> men<PERSON>", "30s": "30 detik", "1m": "1 menit", "2m": "2 menit", "5m": "5 menit", "10m": "10 menit", "20m": "20 menit", "30m": "30 menit", "40m": "40 menit", "1h": "1 jam", "1h30m": "1 jam 30 menit", "2h": "2 jam", "2h30m": "2 jam 30 menit", "3h": "3 jam", "3h30m": "3 jam 30 menit", "4h": "4 jam", "4h30m": "4 jam 30 menit", "5h": "5 jam", "5h30m": "5 jam 30 menit", "6h": "6 jam", "6h30m": "6 jam 30 menit", "7h": "7 jam", "7h30m": "7 jam 30 menit", "8h": "8 jam", "8h30m": "8 jam 30 menit", "9h": "9 jam", "9h30m": "9 jam 30 menit", "10h": "10 jam", "11h": "11 jam", "12h": "12 jam", "13h": "13 jam", "14h": "14 jam", "15h": "15 jam", "16h": "16 jam", "17h": "17 jam", "18h": "18 jam", "19h": "19 jam", "20h": "20 jam", "21h": "21 jam", "22h": "22 jam", "23h": "23 jam", "24h": "24 jam", "36h": "36 jam", "48h": "48 jam", "72h": "72 jam", "1d": "1 hari", "2d": "2 hari", "3d": "3 hari", "4d": "4 hari", "5d": "5 hari", "6d": "6 hari", "7d": "7 hari"}}, "temperature_unit": {"name": "<PERSON><PERSON><PERSON>", "state": {"celsius": "<PERSON><PERSON><PERSON>", "fahrenheit": "Fahrenheit"}}, "oven_built_in_recipe": {"name": "<PERSON><PERSON><PERSON> b<PERSON>an", "state": {"none": "Tidak ada", "steamed_egg_with_okra": "Telur rebus dengan okra", "steamed_sea_bass": "<PERSON><PERSON> laut rebus", "steamed_prawns": "Udang rebus", "handmade_steamed_bread": "Roti rebus buatan tangan", "fan_steamed_baby_vegetables": "Sayuran bayi rebus dengan kipas", "braised_pork": "Babi rebus", "snow_fungus_and_bird_s_nest": "<PERSON><PERSON> salju dan sarang burung", "crab_pot": "Potongan kepiting", "potato_ribs": "<PERSON><PERSON> dan tulang", "coconut_chicken_soup": "<PERSON><PERSON> ayam kelapa", "snack_platter": "<PERSON><PERSON><PERSON> makanan ringan", "chicken_skewers": "<PERSON>te ayam", "roasted_pork_knuckle": "<PERSON><PERSON> babi <PERSON>", "dried_lemon": "<PERSON> kering", "pork_jerky": "Daging babi kering", "crispy_hairtail": "<PERSON><PERSON> ekor rambut renyah", "spicy_grilled_fish": "<PERSON><PERSON> panggang pedas", "roasted_sweet_potatoes": "<PERSON><PERSON> jalar pan<PERSON>ang", "roasted_chicken_wings": "<PERSON>ap ayam panggang", "cumin_lamb_chops": "Potongan daging kambing jintan", "honey_grilled_chicken": "<PERSON><PERSON> panggang madu", "garlic_eggplant": "<PERSON><PERSON> bawang putih", "portuguese_egg_tart": "<PERSON><PERSON> telu<PERSON>", "creme_brulee": "<PERSON>rim brulee", "cocoa_chips": "Cokelat cokelat", "butter_cookies": "<PERSON><PERSON> kering mentega", "chiffon_cake": "<PERSON><PERSON> chiffon", "puff_pastry": "<PERSON>e puff", "red_bean_bread": "Roti kacang merah", "milk_toast": "<PERSON><PERSON><PERSON> susu"}}}, "sensor": {"air_quality": {"name": "<PERSON><PERSON><PERSON> u<PERSON>a", "state": {"excellent": "Sangat baik", "good": "Baik", "moderate": "Sedang", "poor": "Buruk", "severe": "<PERSON><PERSON>"}}, "status": {"name": "Status", "state": {"available": "Tersedia", "plugged_in": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fault_unplugged": "<PERSON><PERSON><PERSON> (tidak terhubung)", "charging": "<PERSON><PERSON><PERSON> daya", "discharging": "Mengeluarkan daya", "waiting": "<PERSON><PERSON><PERSON>", "charged": "<PERSON><PERSON><PERSON>", "fault": "<PERSON><PERSON><PERSON>", "paused": "<PERSON><PERSON><PERSON>", "waiting_for_authorization": "<PERSON><PERSON><PERSON>", "standby": "Siaga", "heating": "<PERSON><PERSON><PERSON><PERSON>", "cooling": "Pendinginan", "keeping_warm": "Tetap hangat", "no_water": "Tidak ada air", "boiling": "<PERSON><PERSON><PERSON><PERSON>", "reserve_only": "<PERSON><PERSON>", "unknown": "Tidak diketahui", "idle": "Idle", "auto": "<PERSON><PERSON><PERSON><PERSON>", "manual": "Manual", "rain_delay": "<PERSON><PERSON><PERSON> hujan", "off": "<PERSON><PERSON>", "on": "<PERSON><PERSON><PERSON>", "cooking": "<PERSON><PERSON><PERSON>", "done": "Se<PERSON><PERSON>", "door_open": "<PERSON><PERSON><PERSON> te<PERSON>", "setting": "<PERSON><PERSON><PERSON><PERSON>", "pre_heating": "<PERSON><PERSON><PERSON><PERSON> awal", "scheduled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "at_temperature": "<PERSON><PERSON> suhu", "done_stage_1": "Selesai tahap 1", "done_stage_2": "Selesai tahap 2", "done_stage_3": "Selesai tahap 3", "done_stage_4": "Selesai tahap 4", "done_stage_5": "Selesai tahap 5", "done_stage_6": "Se<PERSON>ai tahap 6", "done_stage_7": "Selesai tahap 7", "done_stage_8": "Selesai tahap 8", "done_stage_9": "Selesai tahap 9", "done_stage_10": "Selesai tahap 10", "no_food": "Tidak ada makanan", "jammed": "<PERSON><PERSON>", "blocked": "<PERSON><PERSON><PERSON><PERSON>", "feeding": "Pemberian makanan", "feeding_complete": "Pemberian makanan se<PERSON>ai", "caking": "<PERSON><PERSON><PERSON><PERSON>", "cleaning": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sleep": "<PERSON><PERSON><PERSON>", "sterilizing": "<PERSON><PERSON><PERSON><PERSON>", "deodorizing": "Penghilangan bau", "occupied": "<PERSON><PERSON><PERSON>", "normal": "Normal", "low": "Rendah", "high": "Tingg<PERSON>", "unwashed": "Belum dicuci", "pre_washing": "<PERSON><PERSON><PERSON> awal", "washing": "<PERSON><PERSON><PERSON>", "rinsing": "<PERSON><PERSON><PERSON>", "drying": "<PERSON><PERSON><PERSON>", "air_purging": "Pengudaraan", "anti_freeze": "Anti beku", "close": "<PERSON><PERSON><PERSON>", "monitor": "Pemantauan", "working": "<PERSON><PERSON><PERSON>", "warning": "Peringatan", "starting": "<PERSON><PERSON><PERSON>", "emptying": "Mengosongkan", "resetting": "<PERSON><PERSON><PERSON>", "reverse": "Mundur", "full": "<PERSON><PERSON>", "empty": "Kosong", "missing": "Hilang", "formatting": "Memformat", "unformatted": "Belum diformat"}}, "time_remaining": {"name": "<PERSON><PERSON><PERSON> yang tersisa"}, "time_remaining_x": {"name": "<PERSON><PERSON><PERSON> yang tersisa {x}"}, "cooking_status": {"name": "<PERSON><PERSON><PERSON>", "state": {"wait": "<PERSON><PERSON><PERSON> memasak", "reservation": "<PERSON><PERSON>", "cooking": "Sedang berl<PERSON>g", "cancel": "<PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON>", "pause": "<PERSON><PERSON><PERSON>"}}, "water_level": {"name": "Tingkat air", "state": {"full": "<PERSON><PERSON>", "high": "Tingg<PERSON>", "medium": "Sedang", "low": "Rendah", "empty": "Kosong"}}, "energy_produced": {"name": "<PERSON><PERSON><PERSON> yang di<PERSON>"}, "energy_consumed": {"name": "<PERSON><PERSON><PERSON> yang di<PERSON>i"}, "energy_produced_x": {"name": "Energi yang di<PERSON> {x}"}, "energy_consumed_x": {"name": "Energi yang di<PERSON> {x}"}, "current_x": {"name": "Arus {x}"}, "voltage_x": {"name": "Tegangan {x}"}, "power_x": {"name": "<PERSON><PERSON> {x}"}}, "switch": {"anti_frost": {"name": "Anti beku"}, "evaporator_cleaning": {"name": "Pembersihan evaporator"}, "ionizer": {"name": "Pengion"}, "keytone": {"name": "Nada tombol"}, "outlet_x": {"name": "Stopkontak {x}"}, "sleep": {"name": "<PERSON><PERSON><PERSON>"}, "switch_x": {"name": "Sa<PERSON>r {x}"}, "electrolytic_sterilization": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "uv_sterilization": {"name": "<PERSON><PERSON><PERSON><PERSON>"}}, "text": {"scene": {"name": "<PERSON><PERSON>"}}, "time": {"timer": {"name": "Pen<PERSON><PERSON> waktu"}, "timer_x": {"name": "Pengatur waktu {x}"}}, "valve": {"valve_x": {"name": "Katup {x}"}}, "water_heater": {"water_air": {"name": "Pemanas air"}, "kettle": {"name": "Ketel"}}}}