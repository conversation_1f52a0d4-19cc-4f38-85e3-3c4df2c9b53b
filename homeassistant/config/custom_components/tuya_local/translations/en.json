{"title": "Tuya Local", "config": {"step": {"user": {"title": "Configure your Tuya Local device", "description": "Devices can either be added manually or cloud-assisted with the help of the SmartLife app.", "data": {"setup_mode": "Setup choice:"}}, "cloud": {"title": "Login to Tuya", "description": "Enter your SmartLife or Tuya user code.\n\nYou can find this code in the SmartLife app or Tuya app in **Settings** > **Account and Security** screen, and enter the code shown on the **User Code** field. The user code is case sensitive, please be sure to enter it exactly as shown in the app.", "data": {"user_code": "User code:"}}, "scan": {"title": "Complete the login", "description": "Use SmartLife app or Tuya app to scan the following QR-code to complete the login.\n\nContinue to the next step once you have completed this step in the app."}, "choose_device": {"title": "Choose the device to add", "description": "Please pick the device to add from the first drop-down list. Already added devices are not shown.\n\nIf the device connects through a gateway, select that from the gateway list otherwise choose None.", "data": {"device_id": "Choose device:", "hub_id": "Choose gateway:"}}, "search": {"title": "Locate the device IP address", "description": "Tuya cloud does not provide local IP addresses so we must now search your local network to find the device. This takes up to 20 seconds.\n\nIf unsuccessful you will need to provide the IP address yourself some other way such as from router DHCP assignment.\n\nFor this step and the following device addition to succeed you must shut down the mobile app or its connections to the device will often block Tuya Local communication with them."}, "local": {"title": "Configure your Tuya Local device", "description": "[Follow these instructions to find your device id and local key.](https://github.com/make-all/tuya-local#finding-your-device-id-and-local-key)", "data": {"host": "IP address or hostname", "device_id": "Device ID (or the hub's Device ID for devices connected via gateway)", "local_key": "Local key", "protocol_version": "Protocol version (try auto if not known)", "poll_only": "Poll only (try this if your device does not work fully)", "device_cid": "Sub device node_id or uuid (for devices connected via gateway)"}}, "select_type": {"title": "Choose the type of device", "description": "Choose the type that matches your device", "data": {"type": "Device type"}}, "choose_entities": {"title": "Device details", "description": "Choose a name for this device", "data": {"name": "Name"}}}, "abort": {"already_configured": "A device with that ID has already been added.", "not_supported": "Sorry, there is no support for this device.", "no_devices": "Unable to find any unregistered devices for the account."}, "error": {"connection": "Unable to connect to your device with those details. It could be an intermittent issue, or they may be incorrect.", "does_not_need_hub": "<PERSON><PERSON> does not need a gateway and one was selected. Please review your choices.", "needs_hub": "<PERSON><PERSON> needs a gateway and none was selected."}}, "selector": {"setup_mode": {"options": {"cloud": "SmartLife cloud-assisted device setup.", "manual": "Manually provide device connection information.", "cloud_fresh_login": "SmartLife cloud-assisted device setup with fresh login."}}}, "options": {"step": {"user": {"title": "Configure your Tuya Local device", "description": "[Follow these instructions to find your local key.](https://github.com/make-all/tuya-local#finding-your-device-id-and-local-key)", "data": {"host": "IP address or hostname", "local_key": "Local key", "protocol_version": "Protocol version (try auto if not known)", "poll_only": "Poll only (try this if your device does not work fully)"}}}, "error": {"connection": "Unable to connect to your device with those details. It could be an intermittent issue, or they may be incorrect."}, "abort": {"not_supported": "Sorry, there is no support for this device."}}, "entity": {"binary_sensor": {"direction": {"name": "Direction", "state": {"off": "In", "on": "Out"}}, "defrost": {"name": "<PERSON>f<PERSON>t", "state": {"off": "Normal", "on": "Defrosting"}}, "tank_empty": {"name": "Tank", "state": {"off": "OK", "on": "Low"}}, "tank_full": {"name": "Tank", "state": {"off": "OK", "on": "Full"}}, "wake": {"name": "Wake", "state": {"off": "Sleeping", "on": "Awake"}}, "casdon_oven_fault": {"state_attributes": {"description": {"state": {"e1": "E1: Chamber overheat protection (exceeds 290°C)", "e2": "E2: Evaporation tray overheat protection (exceeds 200°C)", "e3": "E3: Chamber low temperature protection (below 35°C)", "e4": "E4: Evaporation tray low temperature protection (below 35°C)", "e5": "E5: Sensor connection error (Open circuit)", "e6": "E6: Sensor malfunction (Short circuit)", "e7": "E7: Display board communication error", "e8": "E8: Water tank open (CN7) or Evaporation tray dry (CN3)"}}}}}, "button": {"factory_reset": {"name": "Factory reset"}, "filter_reset": {"name": "Filter reset"}}, "climate": {"aircon_extra": {"name": "Air conditioner", "state_attributes": {"fan_mode": {"state": {"health": "Healthy", "medhigh": "Medium-High", "medlow": "Medium-Low", "natural": "Natural", "quiet": "Quiet", "sleep": "Sleep", "strong": "Strong"}}, "swing_mode": {"state": {"topmost": "Topmost", "top": "Top", "middle": "Middle", "down": "Down", "downmost": "Downmost"}}}}, "thermostat": {"name": "Thermostat", "state_attributes": {"fan_mode": {"state": {"cycle": "Cycle"}}, "preset_mode": {"state": {"manual": "Manual", "program": "Program", "temp_override": "Temporary Override", "perm_override": "Permanent Override"}}}}, "combo_floor": {"state_attributes": {"preset_mode": {"state": {"cool": "Cool air", "fan_only": "Fan", "heat": "Heat air", "floor_cool": "Cool floor", "floor_heat": "Heat floor"}}}}, "swing_as_powerlevel": {"name": "Heater", "state_attributes": {"swing_mode": {"name": "Heating level", "state": {"stop": "Stop", "auto": "Auto"}}}}, "pool_heatpump": {"state_attributes": {"preset_mode": {"state": {"smart_heat": "Smart heat", "quick_heat": "Quick heat", "quiet_heat": "Quiet heat", "smart_cool": "Smart cool", "quick_cool": "Quick cool", "quiet_cool": "Quiet cool", "auto": "Auto", "smart": "Smart", "quick": "Quick", "quiet": "Quiet"}}}}, "heater": {"name": "Heater"}, "thermo_switch": {"state_attributes": {"hvac_mode": {"state": {"fan_only": "Pause"}}}}, "oven": {"state_attributes": {"preset_mode": {"state": {"healthy_steam": "Healthy Steam", "fresh_steam": "Fresh Steam", "high_temp_steam": "High Temp Steam", "stew": "<PERSON><PERSON>", "bake_up_and_down": "Bake up and down", "bbq": "BBQ", "bottom_hot_air": "Bottom Hot Air", "on_strong_roast": "On Strong Roast", "3d_hot_air": "3D Hot Air", "air_fry": "Air Fry", "steam_frying": "Steam Frying", "one_click_bread": "One-click Bread", "quick_heat": "Quick Heat", "keep_warm": "Keep Warm", "unfreeze": "Unfreeze", "fermentation": "Fermentation", "descale": "Descale", "local_recipes": "Local Recipes", "drying": "Drying", "custom": "Custom", "low_steaming": "Low Steaming", "medium_steaming": "Medium Steaming", "high_steaming": "High Steaming"}}}}}, "humidifier": {"dehumidifier": {"state_attributes": {"mode": {"state": {"laundry": "Dry clothes", "purify": "Purify"}}}}}, "fan": {"aroma_diffuser": {"name": "Aroma diffuser", "state_attributes": {"preset_mode": {"state": {"low": "Low", "medium": "Medium", "high": "High", "continuous": "Continuous", "intermittent": "Intermittent", "timer": "Timer"}}}}, "dehumidifier": {"name": "Dehumidifier", "state_attributes": {"preset_mode": {"state": {"purify": "Purify", "dehumidify": "Dehumidify"}}}}, "fan_with_presets": {"name": "Fan", "state_attributes": {"preset_mode": {"state": {"baby": "Baby", "fresh": "Fresh", "nature": "Natural", "normal": "Normal", "sleep": "Sleep", "smart": "Smart", "strong": "Strong", "custom": "Custom", "high": "High", "medium": "Medium", "low": "Low", "displayoff": "Display off", "off": "Off"}}}}, "ventilation": {"name": "Ventilation", "state_attributes": {"preset_mode": {"state": {"fresh": "Fresh air", "circulate": "Circulate", "sleep": "Sleep", "auto": "Auto", "eco": "Eco", "anti-condensation": "Anti-condensation", "extractor": "Extractor", "heat_recovery": "Heat recovery", "timer": "Timer", "on": "On", "off": "Off"}}}}}, "light": {"backlight": {"name": "Backlight"}, "display": {"name": "Display"}, "embers": {"name": "Embers"}, "flame": {"name": "Flame"}, "indicator": {"name": "Indicator"}, "laser": {"name": "Laser"}, "logs": {"name": "Logs"}, "nightlight": {"name": "Nightlight"}}, "lock": {"child_lock": {"name": "Child lock"}}, "number": {"timer": {"name": "Timer"}, "timer_x": {"name": "Timer {x}"}}, "select": {"currency": {"name": "<PERSON><PERSON><PERSON><PERSON>", "state": {"usd": "USD", "eur": "EUR", "cny": "CNY", "cad": "CAD", "gbp": "GBP"}}, "heat_pump_mode": {"name": "Heat pump mode", "state": {"off": "Off", "heat": "Heat", "cool": "Cool", "floor_heat": "Floor heat", "auto": "Auto", "hotwater": "Hot water", "hotwater_cool": "Hot water & Cool", "hotwater_heat": "Hot water & Heat", "hotwater_auto": "Hot water & Auto", "hotwater_floor_heat": "Hot water & Floor heat"}}, "initial_state": {"name": "Initial state", "state": {"off": "Off", "on": "On", "memory": "Memory"}}, "kettle_mode": {"name": "Kettle mode", "state": {"off": "Off", "heat": "Heat", "boil": "Boil", "quick_heat": "Quick heat", "quick_boil": "Quick boil", "keep_warm": "Keep warm", "custom": "Custom", "dechlorinate": "Dechlorinate", "black_tea": "Black tea", "green_tea": "Green tea", "coffee": "Coffee", "honey_water": "Honey water", "infant_formula": "Infant formula", "white_tea": "White tea", "oolong_tea": "Oolong tea"}}, "language": {"name": "Language", "state": {"chinese": "中文", "chinese_traditional": "中文(繁體)", "english": "English", "french": "Français", "german": "De<PERSON>ch", "italian": "Italiano", "japanese": "日本語", "korean": "한국어", "latin": "Lingua Latina", "portuguese": "Português", "russian": "Русский", "spanish": "Español", "turkish": "Türkçe"}}, "light_mode": {"name": "Light mode", "state": {"off": "Off", "on": "On", "state": "State", "locator": "Locator"}}, "mopping": {"name": "<PERSON><PERSON>", "state": {"off": "Off", "auto": "Auto", "low": "Low", "medium": "Medium", "high": "High"}}, "recipe": {"name": "Recipe", "state": {"pizza": "Pizza", "fries": "<PERSON><PERSON>", "chicken": "Chicken", "shrimp": "<PERSON>mp", "fish": "Fish", "chicken_drumsticks": "Chicken Drumsticks", "vegetables": "Vegetables", "desserts": "Desserts", "none": "None", "chicken_wings": "Chicken Wings", "steak": "Steak", "onion_rings": "Onion Rings", "bacon": "<PERSON>", "cake": "Cake", "bread": "Bread", "toast": "Toast", "sausage": "Sausage", "dry_fruit": "Dry Fruit", "custom": "Custom", "cloud_recipe": "Cloud Recipe", "default": "<PERSON><PERSON><PERSON>", "keep_warm": "Keep Warm", "preheat": "Preheat"}}, "scene": {"name": "Scene", "state": {"relax": "Relax", "movie": "Movie", "party": "Party", "romantic": "Romantic", "night": "Night", "morning": "Morning", "working": "Working", "leisure": "Leisure", "vacation": "Vacation", "reading": "Reading", "twinkle": "Twinkle", "gaming": "Gaming", "none": "None"}}, "timer": {"name": "Timer", "state": {"cancel": "Cancel", "continuous": "Continuous", "30s": "30 seconds", "1m": "1 minute", "2m": "2 minutes", "5m": "5 minutes", "10m": "10 minutes", "20m": "20 minutes", "30m": "30 minutes", "40m": "40 minutes", "1h": "1 hour", "1h30m": "1 hour 30 minutes", "2h": "2 hours", "2h30m": "2 hours 30 minutes", "3h": "3 hours", "3h30m": "3 hours 30 minutes", "4h": "4 hours", "4h30m": "4 hours 30 minutes", "5h": "5 hours", "5h30m": "5 hours 30 minutes", "6h": "6 hours", "6h30m": "6 hours 30 minutes", "7h": "7 hours", "7h30m": "7 hours 30 minutes", "8h": "8 hours", "8h30m": "8 hours 30 minutes", "9h": "9 hours", "9h30m": "9 hours 30 minutes", "10h": "10 hours", "11h": "11 hours", "12h": "12 hours", "13h": "13 hours", "14h": "14 hours", "15h": "15 hours", "16h": "16 hours", "17h": "17 hours", "18h": "18 hours", "19h": "19 hours", "20h": "20 hours", "21h": "21 hours", "22h": "22 hours", "23h": "23 hours", "24h": "24 hours", "36h": "36 hours", "48h": "48 hours", "72h": "72 hours", "1d": "1 day", "2d": "2 days", "3d": "3 days", "4d": "4 days", "5d": "5 days", "6d": "6 days", "7d": "7 days"}}, "temperature_unit": {"name": "Temperature unit", "state": {"celsius": "<PERSON><PERSON><PERSON>", "fahrenheit": "Fahrenheit"}}, "oven_built_in_recipe": {"name": "Built-in recipe", "state": {"none": "None", "steamed_egg_with_okra": "Steamed Egg with Okra", "steamed_sea_bass": "Steamed Sea Bass", "steamed_prawns": "Steamed prawns", "handmade_steamed_bread": "Handmade Steamed Bread", "fan_steamed_baby_vegetables": "Fan Steamed Baby Vegetables", "braised_pork": "Braised Pork", "snow_fungus_and_bird_s_nest": "Snow fungus and bird's nest", "crab_pot": "<PERSON><PERSON>", "potato_ribs": "Potato Ribs", "coconut_chicken_soup": "Coconut Chicken Soup", "snack_platter": "Snack Platter", "chicken_skewers": "Chicken Skewers", "roasted_pork_knuckle": "Roasted Pork Knuckle", "dried_lemon": "<PERSON><PERSON>", "pork_jerky": "Pork Jerky", "crispy_hairtail": "Crispy Hairtail", "spicy_grilled_fish": "Spicy Grilled Fish", "roasted_sweet_potatoes": "Roasted Sweet Potatoes", "roasted_chicken_wings": "Roasted Chicken Wings", "cumin_lamb_chops": "<PERSON><PERSON><PERSON>", "honey_grilled_chicken": "Honey Grilled Chicken", "garlic_eggplant": "<PERSON><PERSON><PERSON>", "portuguese_egg_tart": "Portuguese Egg Tart", "creme_brulee": "Creme Brulee", "cocoa_chips": "Cocoa Chips", "butter_cookies": "Butter Cookies", "chiffon_cake": "Chiffon Cake", "puff_pastry": "Puff pastry", "red_bean_bread": "Red Bean Bread", "milk_toast": "Milk Toast"}}}, "sensor": {"air_quality": {"name": "Air quality", "state": {"excellent": "Excellent", "good": "Good", "moderate": "Moderate", "poor": "Poor", "severe": "Severe"}}, "status": {"name": "Status", "state": {"available": "Available", "plugged_in": "Plugged in", "fault_unplugged": "Fault (unplugged)", "charging": "Charging", "discharging": "Discharging", "waiting": "Waiting", "charged": "Charged", "fault": "<PERSON><PERSON>", "paused": "Paused", "waiting_for_authorization": "Waiting for authorization", "standby": "Standby", "heating": "Heating", "cooling": "Cooling", "keeping_warm": "Keeping warm", "no_water": "No water", "boiling": "Boiling", "reserve_only": "Reserve only", "unknown": "Unknown", "idle": "Idle", "auto": "Auto", "manual": "Manual", "rain_delay": "Rain delay", "off": "Off", "on": "On", "cooking": "Cooking", "done": "Done", "door_open": "Door open", "setting": "Setting", "pre_heating": "Pre-heating", "scheduled": "Scheduled", "at_temperature": "At temperature", "done_stage_1": "Done stage 1", "done_stage_2": "Done stage 2", "done_stage_3": "Done stage 3", "done_stage_4": "Done stage 4", "done_stage_5": "Done stage 5", "done_stage_6": "Done stage 6", "done_stage_7": "Done stage 7", "done_stage_8": "Done stage 8", "done_stage_9": "Done stage 9", "done_stage_10": "Done stage 10", "no_food": "No food", "jammed": "<PERSON><PERSON>", "blocked": "Blocked", "feeding": "Feeding", "feeding_complete": "Feeding complete", "caking": "Caking", "cleaning": "Cleaning", "sleep": "Sleep", "sterilizing": "Sterilizing", "deodorizing": "Deodorizing", "occupied": "Occupied", "normal": "Normal", "low": "Low", "high": "High", "unwashed": "Unwashed", "pre_washing": "Pre-washing", "washing": "Washing", "rinsing": "Rinsing", "drying": "Drying", "air_purging": "Air purging", "anti_freeze": "Anti-freeze", "close": "Close", "monitor": "Monitor", "working": "Working", "warning": "Warning", "starting": "Starting", "emptying": "Emptying", "resetting": "Resetting", "reverse": "Reverse", "full": "Full", "empty": "Empty", "missing": "Missing", "formatting": "Formatting", "unformatted": "Unformatted"}}, "time_remaining": {"name": "Time remaining"}, "time_remaining_x": {"name": "Time remaining {x}"}, "cooking_status": {"name": "Status", "state": {"wait": "Waiting for Cooking", "reservation": "In Reservation", "cooking": "In Progress", "cancel": "Cooking Canceled", "done": "Cooking Completed", "pause": "Cooking Paused"}}, "water_level": {"name": "Water Level", "state": {"empty": "Empty", "low": "Low", "medium": "Medium", "high": "High", "full": "Full"}}, "energy_produced": {"name": "Energy produced"}, "energy_consumed": {"name": "Energy consumed"}, "energy_produced_x": {"name": "Energy produced {x}"}, "energy_consumed_x": {"name": "Energy consumed {x}"}, "current_x": {"name": "Current {x}"}, "voltage_x": {"name": "Voltage {x}"}, "power_x": {"name": "Power {x}"}}, "switch": {"anti_frost": {"name": "Anti-frost"}, "evaporator_cleaning": {"name": "Evaporator cleaning"}, "ionizer": {"name": "Ionizer"}, "keytone": {"name": "Keytone"}, "outlet_x": {"name": "Outlet {x}"}, "sleep": {"name": "Sleep"}, "switch_x": {"name": "Switch {x}"}, "uv_sterilization": {"name": "UV sterilization"}, "electrolytic_sterilization": {"name": "Electrolytic sterilization"}}, "text": {"scene": {"name": "Scene"}}, "time": {"timer": {"name": "Timer"}, "timer_x": {"name": "Timer {x}"}}, "valve": {"valve_x": {"name": "Valve {x}"}}, "water_heater": {"water_air": {"name": "Water heater"}, "kettle": {"name": "Kettle"}}}}