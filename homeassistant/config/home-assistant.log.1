2025-08-29 22:59:44.913 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration aqara_gateway which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-29 22:59:44.916 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration bambu_lab which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-29 22:59:44.919 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration tapo which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-29 22:59:44.922 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration tapo_control which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-29 22:59:44.924 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration localtuya which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-29 22:59:44.928 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration smartthinq_sensors which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-29 22:59:44.930 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration sonos_cloud which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-29 22:59:44.933 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration samsungtv_smart which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-29 22:59:44.936 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration dyson_local which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-29 22:59:44.939 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration panasonic_smart_app which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-29 22:59:44.942 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration xiaomi_gateway3 which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-29 22:59:44.945 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration tesla_custom which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-29 22:59:44.948 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration webrtc which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-29 22:59:44.950 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration adaptive_lighting which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-29 22:59:44.954 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration hacs which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-29 22:59:44.957 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration tuya_local which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-29 22:59:44.960 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration wellbeing which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-29 22:59:47.590 WARNING (ImportExecutor_0) [py.warnings] /usr/local/lib/python3.13/site-packages/google/__init__.py:2: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  __import__('pkg_resources').declare_namespace(__name__)

2025-08-29 22:59:48.088 WARNING (ImportExecutor_0) [py.warnings] /usr/local/lib/python3.13/site-packages/miio/miot_device.py:23: FutureWarning: functools.partial will be a method descriptor in future Python versions; wrap it in enum.member() if you want to preserve the old behavior
  Bool = partial(_str2bool)

2025-08-29 22:59:48.477 WARNING (MainThread) [custom_components.tuya_local.helpers.config] The use of number for RGBCW Lightbulb is deprecated and should be replaced by time.timer.
2025-08-29 22:59:48.477 WARNING (MainThread) [custom_components.tuya_local.entity] The use of number for RGBCW Lightbulb is deprecated and should be replaced by time.timer.
2025-08-29 22:59:48.478 WARNING (MainThread) [custom_components.tuya_local.helpers.config] The use of number for RGBCW Lightbulb is deprecated and should be replaced by time.timer.
2025-08-29 22:59:48.478 WARNING (MainThread) [custom_components.tuya_local.entity] The use of number for RGBCW Lightbulb is deprecated and should be replaced by time.timer.
2025-08-29 22:59:51.246 ERROR (MainThread) [homeassistant.components.mqtt.entity] Error 'The unit of measurement `%` is not valid together with device class `illuminance`' when processing MQTT discovery message topic: 'homeassistant/sensor/0xf84477fffe4d19bd/illuminance/config', message: '{'availability': [{'topic': 'zigbee2mqtt/bridge/state', 'value_template': '{{ value_json.state }}'}], 'device': {'hw_version': 1, 'identifiers': ['zigbee2mqtt_0xf84477fffe4d19bd'], 'manufacturer': 'Tuya', 'model': 'Cover motor', 'model_id': 'TS0601_cover_6', 'via_device': 'zigbee2mqtt_bridge_0x00124b002e112d47', 'name': '窗簾右'}, 'device_class': 'illuminance', 'enabled_by_default': True, 'object_id': '窗簾右_illuminance', 'origin': {'support_url': 'https://www.zigbee2mqtt.io', 'name': 'Zigbee2MQTT', 'sw_version': '2.6.0'}, 'state_class': 'measurement', 'state_topic': 'zigbee2mqtt/窗簾右', 'unique_id': '0xf84477fffe4d19bd_illuminance_zigbee2mqtt', 'unit_of_measurement': '%', 'value_template': '{{ value_json.illuminance }}'}'
2025-08-29 22:59:51.247 ERROR (MainThread) [homeassistant.components.mqtt.entity] Error 'The unit of measurement `%` is not valid together with device class `illuminance`' when processing MQTT discovery message topic: 'homeassistant/sensor/0xf84477fffe40747a/illuminance/config', message: '{'availability': [{'topic': 'zigbee2mqtt/bridge/state', 'value_template': '{{ value_json.state }}'}], 'device': {'hw_version': 1, 'identifiers': ['zigbee2mqtt_0xf84477fffe40747a'], 'manufacturer': 'Tuya', 'model': 'Cover motor', 'model_id': 'TS0601_cover_6', 'via_device': 'zigbee2mqtt_bridge_0x00124b002e112d47', 'name': '窗簾左'}, 'device_class': 'illuminance', 'enabled_by_default': True, 'object_id': '窗簾左_illuminance', 'origin': {'support_url': 'https://www.zigbee2mqtt.io', 'name': 'Zigbee2MQTT', 'sw_version': '2.6.0'}, 'state_class': 'measurement', 'state_topic': 'zigbee2mqtt/窗簾左', 'unique_id': '0xf84477fffe40747a_illuminance_zigbee2mqtt', 'unit_of_measurement': '%', 'value_template': '{{ value_json.illuminance }}'}'
2025-08-29 22:59:53.345 ERROR (MainThread) [pyelectroluxgroup.auth] Failed to get access token: Failed to get access token: 401, message='Unauthorized', url='https://api.developer.electrolux.one/api/v1/token/refresh'
2025-08-29 22:59:53.828 ERROR (MainThread) [homeassistant.components.lg_thinq.coordinator] Error fetching lg_thinq_095ab94995bba665113c13404faa609c6ca4235aa1fec7b4b1a3d4c3ab165fad data: ThinQAPIException: NOT_CONNECTED_DEVICE (1222) - Not connected device
2025-08-29 22:59:53.917 ERROR (MainThread) [homeassistant.components.lg_thinq.coordinator] Error fetching lg_thinq_2646248e5270f24bcffdcde890cc10afc9472ee3d18407e82b2c7333dc067335 data: ThinQAPIException: NOT_CONNECTED_DEVICE (1222) - Not connected device
2025-08-29 22:59:59.652 ERROR (MainThread) [homeassistant.components.template.cover] Received invalid cover is_on state:  for entity cover.garage. Expected: open, opening, closed, closing, true, false, none
2025-08-29 23:00:01.077 ERROR (MainThread) [pyelectroluxgroup.auth] Failed to get access token: Failed to get access token: 401, message='Unauthorized', url='https://api.developer.electrolux.one/api/v1/token/refresh'
2025-08-29 23:00:11.742 ERROR (MainThread) [pyelectroluxgroup.auth] Failed to get access token: Failed to get access token: 401, message='Unauthorized', url='https://api.developer.electrolux.one/api/v1/token/refresh'
2025-08-29 23:00:32.979 ERROR (MainThread) [pyelectroluxgroup.auth] Failed to get access token: Failed to get access token: 401, message='Unauthorized', url='https://api.developer.electrolux.one/api/v1/token/refresh'
2025-08-29 23:00:57.418 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* for modules 'Time, Cloud, DeviceModule' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:01:03.418 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* individually for module query 'get_device_time' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:01:03.619 WARNING (MainThread) [kasa.smart.smartdevice] Error processing Time for device *************, module will be unavailable: get_device_time for Time (error_code=INTERNAL_QUERY_ERROR)
2025-08-29 23:01:14.418 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* for modules 'DeviceModule' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:01:20.418 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* individually for module query 'get_device_info' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:01:23.821 ERROR (MainThread) [custom_components.tesla_custom] Timeout fetching tesla_custom data
2025-08-29 23:01:26.417 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* individually for module query 'get_device_usage' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:01:26.418 ERROR (MainThread) [homeassistant.components.tplink.coordinator] Error fetching ************* data: Unable to communicate with the device update: get_device_info not found in {'get_device_info': <SmartErrorCode.INTERNAL_QUERY_ERROR: -100001>, 'get_device_usage': <SmartErrorCode.INTERNAL_QUERY_ERROR: -100001>} for device *************
2025-08-29 23:01:42.419 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* for modules 'Time, Led' on first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:01:46.418 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* for modules 'Time, DeviceModule' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:01:48.420 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* individually for module query 'get_device_time' on first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:01:52.417 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* individually for module query 'get_device_time' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:01:56.418 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* individually for module query 'get_led_info' on first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:01:56.419 WARNING (MainThread) [kasa.smart.smartdevice] Error processing Time for device *************, module will be unavailable: get_device_time for Time (error_code=INTERNAL_QUERY_ERROR)
2025-08-29 23:01:56.419 WARNING (MainThread) [kasa.smart.smartdevice] Error processing Led for device *************, module will be unavailable: get_led_info for Led (error_code=INTERNAL_QUERY_ERROR)
2025-08-29 23:01:56.431 WARNING (MainThread) [homeassistant.components.tplink.entity] Unable to read data for <DeviceType.Plug at ************* - Daisy 香氛 (P100)> None: get_device_time for Time (error_code=INTERNAL_QUERY_ERROR)
2025-08-29 23:01:56.431 WARNING (MainThread) [homeassistant.components.tplink.entity] Unable to read data for <DeviceType.Plug at ************* - Daisy 香氛 (P100)> None: get_device_time for Time (error_code=INTERNAL_QUERY_ERROR)
2025-08-29 23:01:56.433 WARNING (MainThread) [homeassistant.components.tplink.entity] Unable to read data for <DeviceType.Plug at ************* - Daisy 香氛 (P100)> None: get_led_info for Led (error_code=INTERNAL_QUERY_ERROR)
2025-08-29 23:01:58.422 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* individually for module query 'get_device_info' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:02:03.689 ERROR (MainThread) [homeassistant.components.tplink.coordinator] Error fetching ************* data: Unable to communicate with the device update: get_device_info not found in {'get_device_time': <SmartErrorCode.INTERNAL_QUERY_ERROR: -100001>, 'get_device_info': <SmartErrorCode.INTERNAL_QUERY_ERROR: -100001>, 'get_device_usage': {'time_usage': {'today': 65, 'past7': 579, 'past30': 2170}}} for device *************
2025-08-29 23:02:03.760 ERROR (MainThread) [pyelectroluxgroup.auth] Failed to get access token: Failed to get access token: 401, message='Unauthorized', url='https://api.developer.electrolux.one/api/v1/token/refresh'
2025-08-29 23:02:10.419 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* for modules 'Time, DeviceModule, Led' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:02:14.418 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* for modules 'Time, Cloud, DeviceModule' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:02:16.418 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* individually for module query 'get_device_time' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:02:27.423 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* individually for module query 'get_device_info' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:02:30.424 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* for modules 'Time, DeviceModule' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:02:33.420 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* individually for module query 'get_device_usage' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:02:39.423 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* individually for module query 'get_led_info' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:02:39.424 ERROR (MainThread) [homeassistant.components.tplink.coordinator] Error fetching ************* data: Unable to communicate with the device update: get_device_info not found in {'get_device_time': <SmartErrorCode.INTERNAL_QUERY_ERROR: -100001>, 'get_device_info': <SmartErrorCode.INTERNAL_QUERY_ERROR: -100001>, 'get_device_usage': <SmartErrorCode.INTERNAL_QUERY_ERROR: -100001>, 'get_led_info': <SmartErrorCode.INTERNAL_QUERY_ERROR: -100001>} for device *************
2025-08-29 23:02:39.432 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* individually for module query 'get_device_usage' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:02:50.418 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* for modules 'Time, Cloud, DeviceModule, Led' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:02:56.417 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* individually for module query 'get_device_time' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:02:58.417 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* for modules 'Time, DeviceModule' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:03:02.419 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* individually for module query 'get_connect_cloud_state' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:03:05.822 ERROR (MainThread) [custom_components.tesla_custom] Timeout fetching tesla_custom data
2025-08-29 23:03:09.419 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* individually for module query 'get_device_info' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:03:14.418 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* for modules 'Time, DeviceModule' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:03:15.420 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* individually for module query 'get_device_usage' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:03:19.092 ERROR (MainThread) [custom_components.tapo_control] HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /stok=g~Uu1c11ff(r~I1oUtt9MIo6~MM2q.kh/ds (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0xe3b11ad12490>, 'Connection to ************* timed out. (connect timeout=None)'))
2025-08-29 23:03:20.417 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* individually for module query 'get_device_time' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:03:20.418 ERROR (MainThread) [custom_components.panasonic_smart_app] Error fetching Panasonic Smart Application data: Failed while updating device status
2025-08-29 23:03:21.418 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* individually for module query 'get_led_info' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:03:23.188 ERROR (MainThread) [custom_components.tapo_control] HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: /stok=xUUxRZTnVnPGx0YPkoYuj*zTVNVULHUY/ds (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0xe3b0f678aad0>, 'Connection to ************ timed out. (connect timeout=None)'))
2025-08-29 23:03:23.209 ERROR (MainThread) [custom_components.tapo_control] HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: /stok=yWcd!tJGQAf6.JrGuc.)80ty)gvjWVxL/ds (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0xe3b0f6f59810>, 'Connection to ************ timed out. (connect timeout=None)'))
2025-08-29 23:03:26.418 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* individually for module query 'get_device_info' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:03:32.417 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* individually for module query 'get_device_usage' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:03:32.418 ERROR (MainThread) [homeassistant.components.tplink.coordinator] Error fetching ************* data: Unable to communicate with the device update: get_device_info not found in {'get_device_time': <SmartErrorCode.INTERNAL_QUERY_ERROR: -100001>, 'get_device_info': <SmartErrorCode.INTERNAL_QUERY_ERROR: -100001>, 'get_device_usage': <SmartErrorCode.INTERNAL_QUERY_ERROR: -100001>} for device *************
2025-08-29 23:03:32.420 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* for modules 'Time, Cloud, DeviceModule, Led' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:03:42.418 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* individually for module query 'get_device_info' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:03:43.420 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* for modules 'Time, Cloud, DeviceModule' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:03:48.419 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* individually for module query 'get_device_usage' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:03:55.419 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* individually for module query 'get_device_usage' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:03:59.418 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* for modules 'Time, Cloud, DeviceModule, Led' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:04:05.418 ERROR (MainThread) [kasa.smart.smartdevice] Error querying ************* individually for module query 'get_device_time' after first update: ('Unable to query the device: *************: ', TimeoutError())
2025-08-29 23:04:12.948 WARNING (MainThread) [kasa.smart.smartdevice] Error processing Time for device *************, module will be unavailable: get_device_time for Time (error_code=INTERNAL_QUERY_ERROR)
2025-08-29 23:04:14.836 ERROR (MainThread) [pyelectroluxgroup.auth] Failed to get access token: Failed to get access token: 401, message='Unauthorized', url='https://api.developer.electrolux.one/api/v1/token/refresh'
