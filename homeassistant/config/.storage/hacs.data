{"version": "6", "minor_version": 1, "key": "hacs.data", "data": {"repositories": {"integration": [{"id": "233090507", "full_name": "kodi1/meteoalarm"}, {"id": "995461456", "new": true, "full_name": "microteq/email_notifier"}, {"id": "326352749", "full_name": "giachello/mlgw"}, {"id": "209855666", "full_name": "custom-components/ups"}, {"id": "224560492", "full_name": "claudegel/sinope-130"}, {"id": "250488711", "full_name": "marcelwestrahome/home-assistant-niu-component"}, {"id": "269665267", "full_name": "mvdwetering/yamaha_ynca"}, {"id": "658788776", "new": true, "full_name": "timlaing/modbus_local_gateway"}, {"id": "581579759", "full_name": "RogerSelwyn/mqtt_discoverystream_ha"}, {"id": "670246526", "new": true, "full_name": "HarvsG/ha-glinet4-integration"}, {"id": "420701401", "full_name": "klacol/homeassistant-clage_homeserver"}, {"id": "437278224", "full_name": "kongo09/hass-dell-printer"}, {"id": "248462859", "full_name": "pyalarmdotcom/alarmdotcom"}, {"id": "201740996", "full_name": "custom-components/readme"}, {"id": "974180133", "new": true, "full_name": "cyr-ius/hass-reolink-thumbs"}, {"id": "691766596", "full_name": "Michsior14/ha-venta"}, {"id": "791347643", "new": true, "full_name": "peteS-UK/sonyavr"}, {"id": "186347733", "full_name": "audiconnect/audi_connect_ha"}, {"id": "303793543", "full_name": "to<PERSON><PERSON>rich/home-assistant-skydance"}, {"id": "905048996", "new": true, "full_name": "mickeyschwab/haven-hass"}, {"id": "296946072", "full_name": "boralyl/kodi-recently-added"}, {"id": "853018420", "new": true, "full_name": "PrimeAutomation/petnovations"}, {"id": "146660369", "full_name": "custom-components/media_player.braviatv_psk"}, {"id": "441294260", "full_name": "c-st/auto_areas"}, {"id": "651444976", "new": true, "full_name": "MiguelAngelLV/ha-gas-station-spain"}, {"id": "169641362", "full_name": "elad-bar/ha-blueiris"}, {"id": "847258214", "new": true, "full_name": "ankohanse/hass-studer-xcom"}, {"id": "260410453", "full_name": "sockless-coding/panasonic_cc"}, {"id": "754835075", "full_name": "ablyler/home-assistant-bradford-white-connect"}, {"id": "583896361", "full_name": "Mr-Groch/HA-ESA-NASK-Air-Quality"}, {"id": "570508248", "full_name": "rrooggiieerr/homeassistant-axaremote"}, {"id": "1020452819", "new": true, "full_name": "ia74/roomba_rest980"}, {"id": "413798425", "full_name": "UI-Lovelace-Minimalist/UI"}, {"id": "868369716", "new": true, "full_name": "cvele/playnite_web_mqtt"}, {"id": "443140011", "full_name": "tlskinneriv/awnet_local"}, {"id": "816162723", "new": true, "full_name": "script0803/BituoPMD"}, {"id": "820411079", "new": true, "full_name": "amaximus/mnb_rates"}, {"id": "750769225", "full_name": "ross-w/emerald-hws-ha"}, {"id": "573233876", "full_name": "<PERSON><PERSON><PERSON><PERSON>-SA/Home-Assistant-Switch-Manager"}, {"id": "509121113", "full_name": "seven-io/home-assistant"}, {"id": "817948318", "new": true, "full_name": "remmob/itho_amber"}, {"id": "272140589", "full_name": "ualex73/monitor_docker"}, {"id": "531686897", "full_name": "0xAlon/dolphin"}, {"id": "1026194410", "new": true, "full_name": "golles/ha-aquatlantis-ori"}, {"id": "596993645", "full_name": "Se<PERSON>-Schneider/ha-personio"}, {"id": "262140617", "full_name": "eyalcha/read_your_meter"}, {"id": "356030453", "full_name": "TheByteStuff/RemoteSyslog_Service"}, {"id": "989876961", "new": true, "full_name": "snell-evan-itt/Kidde-HomeSafe"}, {"id": "989077527", "new": true, "full_name": "cavefire/hass-openid"}, {"id": "1001311866", "new": true, "full_name": "hitchin999/protector_net"}, {"id": "248046910", "full_name": "sermayoral/ha-samsungtv-encrypted"}, {"id": "949884394", "new": true, "full_name": "rickmoonex/hass-lighting-zone"}, {"id": "432522624", "full_name": "Aohzan/ipx800v5"}, {"id": "201445202", "full_name": "Cadsters/acv-hass-component"}, {"id": "646875198", "full_name": "nim<PERSON><PERSON><PERSON>/chime_tts"}, {"id": "952530188", "new": true, "full_name": "silvan<PERSON><PERSON>/tuya_sensors"}, {"id": "229060565", "full_name": "fineemb/Xiaomi-Smart-Multipurpose-Kettle"}, {"id": "854347601", "new": true, "full_name": "XiaoMi/ha_xiaomi_home"}, {"id": "334076222", "full_name": "mawinkler/astroweather"}, {"id": "986754305", "new": true, "full_name": "samspade21/vacasa-ha"}, {"id": "1008640764", "new": true, "full_name": "klokku/klokku-home-assistant-integration"}, {"id": "774260466", "new": true, "full_name": "yohaybn/HomeAssistant-EPG"}, {"id": "739466799", "full_name": "Patrick762/hassio-solvis-modbus"}, {"id": "768996945", "full_name": "vingerha/ha_adsb_lol"}, {"id": "886601291", "new": true, "full_name": "sebr/inception-home-assistant"}, {"id": "675748333", "full_name": "popeen/Home-Assistant-Custom-Component-Hemglass"}, {"id": "336054515", "full_name": "lbbrhzn/ocpp"}, {"id": "199313405", "full_name": "Limych/ha-beward"}, {"id": "376390299", "full_name": "golles/ha-knmi"}, {"id": "228604799", "full_name": "cyberjunky/home-assistant-arpscan_tracker"}, {"id": "627970137", "full_name": "toringer/home-assistant-heru"}, {"id": "975619460", "new": true, "full_name": "Vegetronix-Inc/ha-vegehub"}, {"id": "371474642", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/consul"}, {"id": "893895163", "new": true, "full_name": "kclif9/hassactronneo"}, {"id": "337228671", "full_name": "CJNE/ha-porscheconnect"}, {"id": "911371839", "new": true, "full_name": "cnecrea/hidroelectrica"}, {"id": "462430932", "full_name": "amaximus/fire_protection_hu"}, {"id": "273333188", "full_name": "jeroenterheerdt/HADailySensor"}, {"id": "732728235", "full_name": "jdejaegh/irm-kmi-ha"}, {"id": "808922461", "new": true, "full_name": "persuader72/silla-prism-integration"}, {"id": "469351480", "full_name": "IgnacioHR/de-dietrich-c230-ha"}, {"id": "760156009", "new": true, "full_name": "delphiki/hass-tarif-edf"}, {"id": "307503425", "full_name": "caiosweet/Home-Assistant-custom-components-INGV"}, {"id": "373101151", "full_name": "CJNE/ha-sunspec"}, {"id": "517429793", "full_name": "Breina/PowerTagGateway"}, {"id": "228299254", "full_name": "rsnodgrass/hass-lunos"}, {"id": "1012435949", "new": true, "full_name": "maginawin/ha-dali-center"}, {"id": "534317237", "full_name": "DeerMaximum/QR-Code-Generator"}, {"id": "196057008", "full_name": "pilotak/homeassistant-attributes"}, {"id": "781491042", "new": true, "full_name": "andrew-codechimp/HA-Hive-Local-Thermostat"}, {"id": "912488434", "new": true, "full_name": "hiteule/rte-jours-signales"}, {"id": "237880993", "full_name": "fineemb/Smartmi-smart-heater"}, {"id": "534848317", "full_name": "juicejuice/homeassistant_redback"}, {"id": "398739214", "full_name": "<PERSON><PERSON><PERSON>/<PERSON>hook-Service-home-assistant"}, {"id": "717546529", "full_name": "azerty9971/xtend_tuya"}, {"id": "158194879", "full_name": "danobot/entity-controller"}, {"id": "872778892", "new": true, "full_name": "dermotduffy/hass-web-proxy-integration"}, {"id": "686741369", "full_name": "yinyang17/pvpc_energy"}, {"id": "675105585", "full_name": "werthdavid/homeassistant-pulsatrix-local-mqtt"}, {"id": "441920613", "full_name": "klejejs/ha-thermia-heat-pump-integration"}, {"id": "512169290", "full_name": "tinuva/ha-coct-loadshedding"}, {"id": "384704004", "full_name": "dylandoamaral/trakt-integration"}, {"id": "220685552", "full_name": "freakshock88/hass-populartimes"}, {"id": "1033126999", "new": true, "full_name": "J-shw/ha-airvpn"}, {"id": "427773030", "full_name": "spycle/tuneblade"}, {"id": "659517452", "new": true, "full_name": "josh-sanders/home-assistant-omnik-trannergy-pv-inverter"}, {"id": "174809046", "full_name": "custom-components/sensor.avanza_stock"}, {"id": "800610523", "new": true, "full_name": "marq24/ha-evcc"}, {"id": "680907810", "full_name": "Cmajda/ha_golemio"}, {"id": "723707293", "full_name": "gillesvs/librelink"}, {"id": "840302332", "new": true, "full_name": "goruck/home-generative-agent"}, {"id": "354515979", "full_name": "Mr-<PERSON><PERSON><PERSON>/ambihue"}, {"id": "952306880", "new": true, "full_name": "zhheo/ha_honghui_climate"}, {"id": "232424544", "full_name": "tschamm/boschshc-hass"}, {"id": "818044379", "new": true, "full_name": "Amateur-God/home-assistant-techni<PERSON><PERSON>s"}, {"id": "909023229", "new": true, "full_name": "zubir2k/homeassistant-esolattakwim"}, {"id": "856774196", "new": true, "full_name": "Maxou44/ha-tiko-component"}, {"id": "687451320", "full_name": "pantherale0/ha-nintendoparentalcontrols"}, {"id": "655762463", "full_name": "<PERSON><PERSON><PERSON><PERSON>/home-assistant-i<PERSON>i"}, {"id": "397776105", "full_name": "HandyHat/ha-hildebrandglow-dcc"}, {"id": "257634153", "full_name": "rob196/home-assistant-fx<PERSON><PERSON>i"}, {"id": "776540606", "full_name": "kgn3400/state_updated"}, {"id": "933415439", "new": true, "full_name": "mattbratt/pc_ships"}, {"id": "231824299", "full_name": "ha-warmup/warmup"}, {"id": "211393677", "full_name": "piitaya/home-assistant-qubino-wire-pilot"}, {"id": "233289477", "full_name": "linsvensson/sensor.greenely"}, {"id": "790814197", "full_name": "sangvikh/hass-pontos"}, {"id": "582608844", "full_name": "redlukas/emu_mbus_center"}, {"id": "620909192", "full_name": "TheHolyRoger/hass-cryptoinfo"}, {"id": "950348884", "new": true, "full_name": "wisedeer2022/ha_wisecloud_home"}, {"id": "605379507", "full_name": "dknowles2/ha-pitboss"}, {"id": "*********", "full_name": "troykelly/homeassistant-au-nsw-covid"}, {"id": "*********", "full_name": "custom-components/sensor.stadtreinigung_hamburg"}, {"id": "*********", "full_name": "aegjoyce/custom-ambilight"}, {"id": "*********", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/ytube_music_player"}, {"id": "*********", "full_name": "bertbert72/HomeAssistant_VirginTivo"}, {"id": "1037967283", "new": true, "full_name": "golles/ha-tomtom-travel-time"}, {"id": "*********", "new": true, "full_name": "daikin-br/ha-custom-integration"}, {"id": "*********", "full_name": "SecKatie/ha-wyzeapi"}, {"id": "*********", "new": true, "full_name": "bcpearce/homeassistant-gtfs-realtime"}, {"id": "*********", "full_name": "andrew-codechimp/HA-<PERSON>-<PERSON>"}, {"id": "*********", "new": true, "full_name": "mkuthan/solis-cloud-control"}, {"id": "*********", "new": true, "full_name": "custom-components/sensor.radarr_upcoming_media"}, {"id": "*********", "full_name": "thisisthetechie/home-assistant-sickgear"}, {"id": "*********", "full_name": "bkbilly/oralb_ble"}, {"id": "*********", "full_name": "einToast/openai_stt_ha"}, {"id": "*********", "full_name": "syssi/xiaomi_raw"}, {"id": "*********", "full_name": "marq24/ha-senec-v3"}, {"id": "*********", "new": true, "full_name": "faizpuru/ha-pilot-wire-climate"}, {"id": "*********", "full_name": "astrandb/weatherlink"}, {"id": "*********", "new": true, "full_name": "remuslazar/homeassistant-carwings"}, {"id": "635745672", "full_name": "kcofoni/ha-netro-watering"}, {"id": "192664631", "full_name": "sander1988/Indego"}, {"id": "564355840", "full_name": "Anrolosia/Shopping-List-with-G<PERSON>cy"}, {"id": "663974882", "new": true, "full_name": "MateoGreil/homeassistant-comwatt"}, {"id": "888543132", "new": true, "full_name": "smkrv/ha-text-ai"}, {"id": "426814988", "full_name": "aneeshd/schedule_state"}, {"id": "862581210", "new": true, "full_name": "hexCut/irsap-ha"}, {"id": "944941827", "new": true, "full_name": "perosb/qvantum_custom_component"}, {"id": "793739179", "full_name": "jasperslits/haithowifi"}, {"id": "760679476", "full_name": "marq24/ha-waterkotte"}, {"id": "325755578", "full_name": "ReneNulschDE/mbapi2020"}, {"id": "203244705", "full_name": "Mofeywalker/openmensa-hass-component"}, {"id": "403243434", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>/home-assistant-vaillant-vsmart"}, {"id": "919736981", "new": true, "full_name": "FaserF/ha-db_infoscreen"}, {"id": "1020393065", "new": true, "full_name": "tarikbc/ha-ppa-contatto"}, {"id": "380367845", "full_name": "fuatakgun/eufy_security"}, {"id": "713459678", "full_name": "MatthewOnTour/BUT_blinds_time_control"}, {"id": "327695137", "full_name": "jtbgroup/kodi-media-sensors"}, {"id": "524730333", "full_name": "vasqued2/ha-teamtracker"}, {"id": "929627070", "new": true, "full_name": "stevesinchak/ha-weatherlink-live"}, {"id": "256928684", "repository_manifest": {"name": "Tuya Local", "homeassistant": "2025.1.0", "hacs": "2.0.0"}, "full_name": "make-all/tuya-local", "authors": ["@make-all"], "category": "integration", "description": "Local support for Tuya devices in Home Assistant", "domain": "tuya_local", "etag_repository": "W/\"069e85d417b0f9381ae75a17d698cc4085c67772471a2bf26f4a20531a0024b9\"", "last_updated": "2025-08-28T10:28:51Z", "stargazers_count": 2099, "topics": ["climate", "dehumidifiers", "fans", "heaters", "humidifiers", "lights", "pool-heaters", "switches", "tuya"], "installed_commit": "5ed40cf", "installed": true, "last_commit": "5ed40cf", "last_version": "2025.8.0", "manifest_name": "Tuya Local", "releases": true, "version_installed": "2025.8.0", "last_fetched": 1756461228.871888}, {"id": "282509738", "full_name": "GuilleGF/hassio-ovh"}, {"id": "222687548", "full_name": "dwain<PERSON><PERSON><PERSON>/dwains-lovelace-dashboard"}, {"id": "874256077", "new": true, "full_name": "timniklas/hass-wellyou"}, {"id": "306822538", "full_name": "exxamalte/home-assistant-custom-components-nsw-rural-fire-service-fire-danger"}, {"id": "512213802", "full_name": "alemuro/ha-cecotec-conga"}, {"id": "407205510", "full_name": "alryaz/hass-pik-intercom"}, {"id": "933146676", "new": true, "full_name": "matfroh/sax_battery_ha"}, {"id": "871341186", "new": true, "full_name": "cobryan05/ha-color-notify"}, {"id": "763820896", "full_name": "NinDTendo/tobi"}, {"id": "566085483", "full_name": "LaggAt/ha-jokes"}, {"id": "845066507", "new": true, "full_name": "koenhendriks/ha-eplucon"}, {"id": "220678749", "full_name": "shogunxam/Home-Assistant-custom-components-cfr-toscana"}, {"id": "980751292", "new": true, "full_name": "PhantomPhoton/S3-Compatible"}, {"id": "319820836", "full_name": "RobertD502/home-assistant-i<PERSON><PERSON>"}, {"id": "296028613", "full_name": "ayavilevich/homeassistant-dlink-presence"}, {"id": "560848165", "full_name": "RonnyWinkler/homeassistant.homey"}, {"id": "701792186", "full_name": "krasnoukhov/homeassistant-oncharger"}, {"id": "686408627", "full_name": "leranp/HomeAssistant-galatz-news"}, {"id": "480281490", "full_name": "Lektrico/ha_lektrico"}, {"id": "309192571", "full_name": "ma<PERSON><PERSON>-o<PERSON><PERSON>-swier<PERSON>ynski/tech-controllers"}, {"id": "758227347", "full_name": "chkuendig/hass-ab_ble_gateway"}, {"id": "474183846", "full_name": "sangh<PERSON><PERSON><PERSON><PERSON>/ha-mila"}, {"id": "808833115", "full_name": "BJReplay/ha-solcast-solar"}, {"id": "661544389", "new": true, "full_name": "Makr91/ha_easgen"}, {"id": "532263303", "full_name": "toringer/home-assistant-met-next-6-hours-forecast"}, {"id": "504225349", "new": true, "full_name": "FaserF/ha-foodsharing"}, {"id": "970435939", "new": true, "full_name": "uiotlink/ha_uiot_home"}, {"id": "751371393", "full_name": "faizpuru/ha-ambeo_soundbar"}, {"id": "222292912", "full_name": "gcobb321/icloud3"}, {"id": "265587564", "full_name": "golles/Home-Assistant-Sensor-MC66C"}, {"id": "906146079", "new": true, "full_name": "stijnpiron/parking_gent"}, {"id": "693064759", "full_name": "tetele/hvac_group"}, {"id": "783555879", "new": true, "full_name": "BJReplay/EPA_AirQuality_HA"}, {"id": "988471285", "new": true, "full_name": "c1pher-cn/ha-mcp-for-xiaozhi"}, {"id": "498861412", "full_name": "bmcclure/ha-aquanta"}, {"id": "366713850", "full_name": "Pirate-Weather/pirate-weather-ha"}, {"id": "224073673", "full_name": "pinkywafer/Calendarific"}, {"id": "186605347", "full_name": "bosch-thermostat/home-assistant-bosch-custom-component"}, {"id": "200927325", "full_name": "rsnodgrass/hass-poolmath"}, {"id": "597799922", "full_name": "jvitkauskas/homeassistant_blauberg_s21"}, {"id": "441942093", "full_name": "jugla/keyatome"}, {"id": "974025191", "new": true, "full_name": "Syonix/ha-wiser-by-feller"}, {"id": "323152128", "full_name": "GuyLewin/home-assistant-crunch-o-meter"}, {"id": "381052530", "full_name": "georgezhao2010/fordpass_china"}, {"id": "283243425", "full_name": "definitio/ha-sox"}, {"id": "344446335", "full_name": "RogerSelwyn/O365-HomeAssistant"}, {"id": "555221653", "full_name": "astrandb/viva"}, {"id": "900293200", "new": true, "full_name": "thomas-svrts/blossom_be"}, {"id": "1017521072", "new": true, "full_name": "smarthomeblack/zalo_bot"}, {"id": "363203831", "full_name": "benle<PERSON>/sureha"}, {"id": "602776380", "new": true, "full_name": "MiguelAngelLV/ha-balance-neto"}, {"id": "993980538", "new": true, "full_name": "eseverson/hass-balena"}, {"id": "235915302", "full_name": "ryanmac8/Home-Assistant-<PERSON>"}, {"id": "862580935", "new": true, "full_name": "cowboyrushforth/home-assistant-mole<PERSON><PERSON>"}, {"id": "818512053", "new": true, "full_name": "wbyoung/watersmart"}, {"id": "455217528", "full_name": "brian<PERSON>ge/home-assistant-sdnotify"}, {"id": "563077911", "full_name": "CubicPill/china_southern_power_grid_stat"}, {"id": "634513270", "full_name": "enes-o<PERSON><PERSON>/Home-Assistant-Helium-Integration"}, {"id": "356827073", "full_name": "openrgb-ha/openrgb-ha"}, {"id": "780005646", "full_name": "LarsK1/hass_solvis_control"}, {"id": "905249709", "new": true, "full_name": "bramstroker/homeassistant-state-webhook"}, {"id": "792495621", "new": true, "full_name": "superrob/genvexconnect"}, {"id": "452737694", "new": true, "full_name": "MvdDonk/brewfather"}, {"id": "167885769", "full_name": "twrecked/hass-aarlo"}, {"id": "198794376", "full_name": "toringer/home-assistant-metnowcast"}, {"id": "812010915", "full_name": "kamaradclimber/datadog-integration-ha"}, {"id": "222118751", "full_name": "AlexxIT/SonoffLAN"}, {"id": "646115401", "full_name": "samjsmart/ha-zone4"}, {"id": "589348474", "full_name": "epaulsen/energytariff"}, {"id": "871829086", "new": true, "full_name": "ITSpecialist111/ai_automation_suggester"}, {"id": "694160639", "full_name": "jampez77/Ryanair"}, {"id": "593780777", "full_name": "archef2000/homeassistant-upsplus"}, {"id": "738164752", "new": true, "full_name": "Mallonbacka/custom-component-digitransit"}, {"id": "748983135", "full_name": "albaintor/homeassistant_electrolux_status"}, {"id": "263443413", "full_name": "julcollas/hass-vigicrues"}, {"id": "842988630", "new": true, "full_name": "Tvalley71/pluggit"}, {"id": "348835574", "full_name": "popeen/Home-Assistant-Custom-Component-MotalaVattenAvfall"}, {"id": "228685436", "full_name": "cyberjunky/home-assistant-toon_<PERSON><PERSON>us"}, {"id": "289579468", "full_name": "swartjean/ha-eskom-loadshedding"}, {"id": "1004468763", "new": true, "full_name": "rknightion/meraki-dashboard-ha"}, {"id": "822294639", "new": true, "full_name": "ruchoff/homeassistant-citibike"}, {"id": "934186529", "new": true, "full_name": "kirill-k2/hass-guk-krasnodar"}, {"id": "295123287", "full_name": "rt400/Jewish-Sabbaths-Holidays"}, {"id": "815785865", "full_name": "xiaodong-lx/tplink-ipc-control"}, {"id": "695521993", "full_name": "myTselection/MyEnergy"}, {"id": "204700563", "full_name": "twrecked/hass-momentary"}, {"id": "372058588", "full_name": "amaximus/met_alerts_hu"}, {"id": "323923603", "repository_manifest": {"name": "Tapo Controller", "hacs": "1.6.0", "render_readme": true, "homeassistant": "2025.1.0"}, "full_name": "petretiandrea/home-assistant-tapo-p100", "authors": ["@petretiandrea"], "category": "integration", "description": "A custom integration to control Tapo devices from home assistant.", "domain": "tapo", "etag_repository": "W/\"18d6161bd38ca0d7b57274819e00339b4bf92eb1c0e7ad058a4a525f5a31c7af\"", "last_updated": "2025-08-13T17:34:41Z", "stargazers_count": 891, "topics": ["energy", "l510", "l530", "l900", "monitoring", "p100", "p105", "p110", "smart-plug", "tapo", "tapo-device", "tapo-light-bulb", "tp-link", "tplink"], "installed_commit": "8f09560", "installed": true, "last_commit": "8f09560", "last_version": "3.2.1", "manifest_name": "Tapo Controller", "releases": true, "version_installed": "3.2.1", "last_fetched": **********.412313}, {"id": "716211438", "full_name": "JanGiese/notion_todo"}, {"id": "747328039", "full_name": "kamaradclimber/geovelo-homeassistant"}, {"id": "894629553", "new": true, "full_name": "Chiralistic/home-assistant-schu<PERSON><PERSON>n"}, {"id": "247566230", "full_name": "Verbalinsurection/next_rocket_launch"}, {"id": "201963665", "full_name": "custom-components/healthchecksio"}, {"id": "975150499", "new": true, "full_name": "dckiller51/bodypetscale"}, {"id": "229519365", "full_name": "bacco007/sensor.waternsw"}, {"id": "191831638", "full_name": "albertogeniola/meross-homeassistant"}, {"id": "271398374", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/Home-Assistant-custom-components-Saver"}, {"id": "299967654", "full_name": "willhold<PERSON>y/hifiberry"}, {"id": "794705746", "new": true, "full_name": "kgn3400/carousel"}, {"id": "408074547", "full_name": "sugoi-wada/acer-air-monitor-2018"}, {"id": "254347436", "full_name": "mampfes/hacs_waste_collection_schedule"}, {"id": "283518438", "full_name": "definitio/ha-rhvoice"}, {"id": "953416417", "new": true, "full_name": "BottlecapDave/HomeAssistant-TargetTimeframes"}, {"id": "**********", "new": true, "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>/ha-simple-timer"}, {"id": "452272431", "full_name": "larry-wong/bemfa"}, {"id": "485971293", "full_name": "drakhart/ha-super-soco-custom"}, {"id": "668476421", "full_name": "kaechele/napoleon-efire"}, {"id": "868197401", "new": true, "full_name": "GuyKh/ims-envista-custom-component"}, {"id": "924171807", "new": true, "full_name": "microteq/whatsigram_messenger"}, {"id": "989019229", "new": true, "full_name": "Korkuttum/tuya_scale"}, {"id": "356655356", "full_name": "tofuSCHNITZEL/home-assistant-w<PERSON><PERSON><PERSON><PERSON>"}, {"id": "897551595", "new": true, "full_name": "bendikrb/ha-politikontroller"}, {"id": "1005215052", "new": true, "full_name": "eduwardpost/aviation-weather"}, {"id": "336798340", "full_name": "tomaae/homeassistant-truenas"}, {"id": "749900391", "full_name": "SoftXperience/home-assistant-foxess-api"}, {"id": "474172189", "full_name": "adamoutler/anycubic-homeassistant"}, {"id": "527414830", "full_name": "amosyuen/ha-eight-sleep-climate"}, {"id": "255662264", "full_name": "MTrab/landroid_cloud"}, {"id": "938940887", "new": true, "full_name": "cleveroom-code/ha-cleveroom-home"}, {"id": "507038522", "full_name": "Breina/nad_controller"}, {"id": "885151514", "new": true, "full_name": "DavidBilodeau1/saguenay_collection"}, {"id": "425931056", "full_name": "dahlb/ha_kia_hyundai"}, {"id": "137126619", "full_name": "davesmeghead/visonic"}, {"id": "644033469", "full_name": "jayde<PERSON><PERSON>/Home-Assistant-weatherdotcom"}, {"id": "349455097", "full_name": "<PERSON><PERSON><PERSON><PERSON>/ha-ubee"}, {"id": "1016586430", "new": true, "full_name": "mac8005/xiaozhi-mcp-ha"}, {"id": "312080478", "full_name": "LaggAt/hacs-govee"}, {"id": "849004226", "new": true, "full_name": "ronnnnnnnnnnnnn/etekcity_fitness_scale_ble"}, {"id": "622582764", "full_name": "AndreaTomatis/loex-xsmart-integration"}, {"id": "937289117", "new": true, "full_name": "andrejs2/arso_potresi"}, {"id": "538235457", "full_name": "juacas/zte_tracker"}, {"id": "379781545", "full_name": "rroller/netgear"}, {"id": "471478227", "full_name": "mbillow/ha-chargepoint"}, {"id": "401145616", "full_name": "CJNE/ha-myenergi"}, {"id": "582143144", "full_name": "robbinjanssen/home-assistant-ojmicroline-thermostat"}, {"id": "200035037", "full_name": "LordBoos/discord_game"}, {"id": "612652228", "full_name": "NiaoBlush/impc_energy"}, {"id": "922731494", "new": true, "full_name": "braytonstafford/grok_conversation"}, {"id": "912032750", "new": true, "full_name": "myhades/ha-clash-controller"}, {"id": "520066480", "full_name": "vmakeev/huawei_mesh_router"}, {"id": "857668092", "new": true, "full_name": "OStrama/weishaupt_modbus"}, {"id": "177469955", "full_name": "dlarrick/hass-kumo"}, {"id": "281956859", "full_name": "postlund/dlink_hnap"}, {"id": "882473463", "new": true, "full_name": "bbr111/byd_hvs"}, {"id": "424574671", "full_name": "roslovets/SP110E-HASS"}, {"id": "289550686", "full_name": "raman325/ha-zoom-automation"}, {"id": "820375572", "new": true, "full_name": "DominikWrobel/airmusic"}, {"id": "849014350", "new": true, "full_name": "FaserF/ha-boulderwelt"}, {"id": "443529332", "full_name": "ardevd/ha-bobcatminer"}, {"id": "700780425", "full_name": "jekalmin/extended_openai_conversation"}, {"id": "552555459", "full_name": "Sholofly/lghorizon"}, {"id": "591813159", "new": true, "full_name": "rstrouse/ESPSomfy-RTS-HA"}, {"id": "807540393", "full_name": "RogerSelwyn/MS365-Calendar"}, {"id": "987889626", "new": true, "full_name": "djlactose/smartslydr"}, {"id": "637707710", "new": true, "full_name": "iKaew/hass-lifesmart-addon"}, {"id": "893011665", "new": true, "full_name": "Hankanman/Area-Occupancy-Detection"}, {"id": "256709811", "full_name": "Limych/ha-jq300"}, {"id": "726281564", "full_name": "Pho3niX90/solis_modbus"}, {"id": "899977910", "new": true, "full_name": "thomasddn/ha-volvo-cars"}, {"id": "742612421", "full_name": "geeks-r-us/maxstorage_ultimate"}, {"id": "984732939", "new": true, "full_name": "eXPerience83/pollenlevels"}, {"id": "623127338", "new": true, "full_name": "MiguelAngelLV/ha-octopus-spain"}, {"id": "954834466", "new": true, "full_name": "Jems22/ha_star_rennes"}, {"id": "603119944", "new": true, "full_name": "MiguelAngelLV/ha-tarifa-20td"}, {"id": "595789498", "new": true, "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>/wiheat"}, {"id": "993648469", "new": true, "full_name": "barneyonline/groq_tts"}, {"id": "269113518", "full_name": "plamish/xcomfort"}, {"id": "1002963969", "new": true, "full_name": "nnnlog/homeassistant-cvnet-smarthome"}, {"id": "898093129", "new": true, "full_name": "timniklas/hass-neakasa"}, {"id": "228063780", "full_name": "Limych/ha-iaquk"}, {"id": "293488791", "full_name": "Anton<PERSON>-Czaplicki/vulcan-for-hassio"}, {"id": "535287543", "full_name": "hardbyte/ha-evnex"}, {"id": "822730719", "new": true, "full_name": "da<PERSON><PERSON><PERSON>/ha-solarman"}, {"id": "514391925", "full_name": "fapfaff/homeassistant-appwash"}, {"id": "1035037589", "new": true, "full_name": "ha-termoweb/ha-termoweb"}, {"id": "523250759", "full_name": "d<PERSON><PERSON><PERSON>/hass-seafile"}, {"id": "531349329", "full_name": "sh00t2kill/dolphin-robot"}, {"id": "420504770", "full_name": "amaximus/water_quality_fvm"}, {"id": "494322135", "new": true, "full_name": "craibo/ha_strava"}, {"id": "463624702", "full_name": "dahlb/ha_hatch"}, {"id": "192086849", "full_name": "bouwew/sems2mqtt"}, {"id": "945419767", "new": true, "full_name": "andrew-codechimp/HA-Periodic-Min-Max"}, {"id": "297379398", "full_name": "<PERSON><PERSON><PERSON><PERSON>/home_assistant_tractive"}, {"id": "768540017", "new": true, "full_name": "hacf-fr/hass-ecoledirecte"}, {"id": "745837766", "full_name": "dan-r/HomeAssistant-NissanConnect"}, {"id": "912818566", "new": true, "full_name": "rhanekom/hass-qwikswitch-api"}, {"id": "661020642", "full_name": "odya/hass-ina219-ups-hat"}, {"id": "894700381", "new": true, "full_name": "Knifa/led-matrix-zmq-hass"}, {"id": "801511156", "new": true, "full_name": "nexhome-org/nexhome-homeassistant-component"}, {"id": "918224983", "new": true, "full_name": "daniel<PERSON><PERSON><PERSON>/elegoo-homeassistant"}, {"id": "181743867", "full_name": "tefinger/hass-brematic"}, {"id": "718875576", "full_name": "<PERSON><PERSON><PERSON><PERSON>/hass-unfoldedci<PERSON>"}, {"id": "441369133", "full_name": "ZacheryThomas/homeassistant-smartrent"}, {"id": "490422137", "full_name": "d<PERSON><PERSON><PERSON>/home-assistant-i<PERSON><PERSON><PERSON><PERSON>"}, {"id": "422931599", "full_name": "KartoffelToby/better_thermostat"}, {"id": "832462542", "new": true, "full_name": "hyperb1iss/signalrgb-homeassistant"}, {"id": "250345421", "full_name": "eifinger/hass-foldingathomecontrol"}, {"id": "651580675", "full_name": "ufozone/ha-unifi-voucher"}, {"id": "917804126", "new": true, "full_name": "ExMacro/hass_ensto_ble"}, {"id": "797137425", "new": true, "full_name": "ambientika/HomeAssistant-integration-for-Ambientika"}, {"id": "637172632", "full_name": "elden1337/hass-peaqnext"}, {"id": "850391229", "new": true, "full_name": "FernandoZueet/messages_store"}, {"id": "658497013", "new": true, "full_name": "adammc<PERSON>agh/home-assistant-powervault"}, {"id": "755320495", "full_name": "andrewjswan/SwatchTime"}, {"id": "828506116", "new": true, "full_name": "diego7marques/ha-aws-cost"}, {"id": "401282856", "full_name": "BottlecapDave/HomeAssistant-OctopusEnergy"}, {"id": "577981941", "full_name": "avolmensky/panasonic_eolia"}, {"id": "417802358", "full_name": "Chouffy/home_assistant_tgtg"}, {"id": "656992550", "full_name": "JeffSteinbok/hass-dreo"}, {"id": "326367724", "full_name": "johnnybe<PERSON>/ha-ksenia-lares"}, {"id": "160728801", "full_name": "asantaga/lightwaverf_HA_EnergySensor"}, {"id": "427902632", "full_name": "<PERSON><PERSON><PERSON><PERSON>/home_assistant_solarman"}, {"id": "124688531", "full_name": "rytilahti/homeassistant-upnp-availability"}, {"id": "286554328", "full_name": "KoljaWindeler/kaco"}, {"id": "418810115", "full_name": "travis<PERSON>sen/hass-pfsense"}, {"id": "698783816", "full_name": "ablyler/home-assistant-a<PERSON><PERSON>"}, {"id": "247070270", "repository_manifest": {"name": "SamsungTV Smart", "zip_release": true, "filename": "samsungtv_smart.zip", "homeassistant": "2025.6.0"}, "full_name": "ollo69/ha-samsungtv-smart", "authors": ["@jaruba", "@ollo69", "@screwdgeh"], "category": "integration", "description": "📺 Home Assistant SamsungTV Smart integration with SmartThings API Support.", "domain": "samsungtv_smart", "downloads": 9802, "etag_repository": "W/\"a5e56cb9f80847f24c4b76af8a6af32634aa9600107d885b5a724af732fc9382\"", "last_updated": "2025-08-24T09:57:03Z", "stargazers_count": 560, "topics": ["samsung", "samsung-smart-tv", "samsung-tv", "smartthings"], "installed_commit": "623f521", "installed": true, "last_commit": "623f521", "last_version": "v0.14.4", "manifest_name": "SamsungTV Smart", "releases": true, "version_installed": "v0.14.4", "last_fetched": 1756413158.682974}, {"id": "183222061", "full_name": "lichtteil/local_luftdaten"}, {"id": "527278013", "full_name": "stephan192/hochwasserportal"}, {"id": "503045365", "full_name": "<PERSON><PERSON><PERSON>/hass-senertec"}, {"id": "926087986", "new": true, "full_name": "Nicxe/krisinformation"}, {"id": "943574315", "new": true, "full_name": "Antoxa1081/home-assistant-dess-monitor"}, {"id": "963218814", "new": true, "full_name": "SavageNL/home-assistant-volcano-hybrid"}, {"id": "544947025", "full_name": "functionpointer/home-assistant-chargecloud-integration"}, {"id": "701510734", "full_name": "Andre0512/speedport"}, {"id": "586066332", "full_name": "pink88/Tuiss2HA"}, {"id": "811492560", "new": true, "full_name": "elboletaire/ha-weatherxm"}, {"id": "374763546", "full_name": "xilense/aimp_custom_component"}, {"id": "940823323", "new": true, "full_name": "sokolovs/wda-sensor"}, {"id": "181480967", "full_name": "basschipper/homeassistant-generic-hygrostat"}, {"id": "1005447526", "new": true, "full_name": "Elwinmage/ha-reefbeat-component"}, {"id": "786482294", "full_name": "dasshubham762/atomberg-integration"}, {"id": "517642950", "full_name": "itchannel/apex-ha"}, {"id": "993010795", "new": true, "full_name": "hitchin999/YidCal"}, {"id": "317051290", "full_name": "eyalcha/kan_program"}, {"id": "947623322", "new": true, "full_name": "lewei50/ha_iammeter_link"}, {"id": "625887812", "full_name": "joselcaguilar/azure-openai-ha"}, {"id": "250866164", "full_name": "azogue/eventsensor"}, {"id": "742895532", "full_name": "egmen/moscow_transport"}, {"id": "1034068486", "new": true, "full_name": "MrSjodin/HomeAssistant_Trafiklab_Integration"}, {"id": "921203953", "new": true, "full_name": "niktest/solaredge_ev_charger_au"}, {"id": "996848599", "new": true, "full_name": "connorgallopo/Superior-Plus-Propane"}, {"id": "1008725569", "new": true, "full_name": "ikifar2012/neosmartblue-ha"}, {"id": "451209586", "full_name": "J-Lindvig/Flagdays_DK"}, {"id": "183989659", "full_name": "JayBlackedOut/hass-nhlapi"}, {"id": "261614146", "full_name": "youdroid/home-assistant-<PERSON><PERSON><PERSON>"}, {"id": "590773910", "new": true, "full_name": "yohaybn/HA_aliexpress_package_tracker_sensor"}, {"id": "920228400", "new": true, "full_name": "andrejs2/slovenian_weather_integration"}, {"id": "196055705", "full_name": "pilotak/homeassistant-clientraw"}, {"id": "864464666", "new": true, "full_name": "3p3v/berluf_selen_2"}, {"id": "568186049", "full_name": "duhow/hass-aigues-barcelona"}, {"id": "651597909", "full_name": "sdrapha/home-assistant-custom-components-pfsense-gateways"}, {"id": "636943975", "full_name": "tomaae/homeassistant-portainer"}, {"id": "861799471", "new": true, "full_name": "zbigniew<PERSON><PERSON>a/home-assistant-solplanet"}, {"id": "1010272157", "new": true, "full_name": "pvyleta/xcc-integration"}, {"id": "617699018", "full_name": "hokiebrian/eia_hourly_demand"}, {"id": "373864423", "full_name": "PeteRager/lennoxs30"}, {"id": "146379582", "full_name": "custom-components/sensor.trakt"}, {"id": "440617082", "full_name": "elahd/ha-nyc311"}, {"id": "192604318", "full_name": "mudape/iphonedetect"}, {"id": "340926904", "full_name": "kpoppel/homeassistant-novafos"}, {"id": "279538782", "full_name": "pcourbin/ecodevices_rt2"}, {"id": "246417951", "full_name": "SLG/home-assistant-what<PERSON><PERSON><PERSON>"}, {"id": "233079250", "full_name": "kodi1/darksky_m"}, {"id": "827483338", "new": true, "full_name": "<PERSON><PERSON><PERSON><PERSON>Z<PERSON>-MC-Home-assistant-repo"}, {"id": "585145942", "full_name": "schwarzenbergf/irtrans"}, {"id": "980865675", "new": true, "full_name": "glenndehaan/homeassistant-hdfury"}, {"id": "490231724", "full_name": "jbouwh/ha-elro-connects"}, {"id": "232299868", "full_name": "astrandb/sentio"}, {"id": "659599247", "full_name": "nelbs/solaredge-forecast"}, {"id": "307586942", "full_name": "bremor/bureau_of_meteorology"}, {"id": "242528119", "full_name": "ruuvi-friends/ruuvi-hass.io"}, {"id": "719521685", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/home-assistant-flightradar<PERSON>"}, {"id": "922936587", "new": true, "full_name": "djbios/home-assistant-cat-scale"}, {"id": "200399989", "full_name": "tmonck/clean_up_snapshots"}, {"id": "194140521", "full_name": "thomasloven/hass-browser_mod"}, {"id": "594751789", "full_name": "daxingplay/home-assistant-vaillant-plus"}, {"id": "913039096", "new": true, "full_name": "<PERSON>-<PERSON><PERSON>-bad<PERSON>/home-assistant_u<PERSON>-nas"}, {"id": "528492198", "full_name": "myTselection/telenet_telemeter"}, {"id": "935793147", "new": true, "full_name": "hzjchina/hass-welock"}, {"id": "545025660", "full_name": "JonasJoKuJonas/homeassistant-WebUntis"}, {"id": "724819055", "full_name": "physje/waterinfo"}, {"id": "151580533", "full_name": "custom-components/sensor.unifigateway"}, {"id": "830222631", "new": true, "full_name": "MrBearPresident/JBL_Soundbar"}, {"id": "199306003", "full_name": "gieljnssns/buienalarm-sensor-homeassistant"}, {"id": "419786466", "full_name": "WillCodeForCats/solaredge-modbus-multi"}, {"id": "742903711", "full_name": "lizardsystems/hass-mygas"}, {"id": "625369698", "full_name": "IvanSanchez/homeassistant-freeds"}, {"id": "219363790", "full_name": "lindell/home-assistant-tv4-play"}, {"id": "461802716", "full_name": "Kaptensanders/skolmat"}, {"id": "941653273", "new": true, "full_name": "chris-mc1/homeconnect_local_hass"}, {"id": "271718871", "new": true, "full_name": "alryaz/hass-mosoblgaz"}, {"id": "459336824", "full_name": "regulad/hass-lacrosseview"}, {"id": "263075818", "full_name": "Guy<PERSON><PERSON>/ha-meural"}, {"id": "637182469", "full_name": "g<PERSON><PERSON><PERSON><PERSON><PERSON>/miwa"}, {"id": "341500126", "full_name": "krahabb/meross_lan"}, {"id": "344660161", "full_name": "neggert/hass-egauge"}, {"id": "143340728", "full_name": "herikw/home-assistant-custom-components"}, {"id": "334523683", "full_name": "B5r1oJ0A9G/teufel_raumfeld"}, {"id": "971570599", "new": true, "full_name": "kgn3400/wage_calculator"}, {"id": "816593073", "new": true, "full_name": "Refoss/refoss-homeassistant"}, {"id": "193588612", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/Home-Assistant-custom-components-iMPK"}, {"id": "881363897", "new": true, "full_name": "mguyard/hass-diagral"}, {"id": "740129969", "full_name": "3ll3d00d/jriver_homeassistant"}, {"id": "738508440", "new": true, "full_name": "cyberjunky/home-assistant-shell_recharge"}, {"id": "207620142", "full_name": "caiosweet/Home-Assistant-custom-components-DPC-Alert"}, {"id": "560282866", "full_name": "monty68/uniled"}, {"id": "611184738", "new": true, "full_name": "Strixx76/samsungwam"}, {"id": "676091897", "full_name": "agittins/bermuda"}, {"id": "582589896", "full_name": "jmcollin78/versatile_thermostat"}, {"id": "615769161", "full_name": "Rain1971/V2C_trydant"}, {"id": "920899562", "new": true, "full_name": "<PERSON>-Wendorf/hass-uplift-desk"}, {"id": "279680951", "full_name": "kukulich/home-assistant-j<PERSON><PERSON><PERSON><PERSON>"}, {"id": "358585486", "full_name": "danieldotnl/ha-multiscrape"}, {"id": "888380028", "new": true, "full_name": "juanillo62gm/HA-Panda-PWR"}, {"id": "692701325", "full_name": "briis/weatherflow_forecast"}, {"id": "307098646", "full_name": "nielsfaber/alarmo"}, {"id": "555110808", "full_name": "lewei50/ha_iammeter_modbus"}, {"id": "387116237", "full_name": "hwmland/homeassistant-xmrig"}, {"id": "268722568", "full_name": "Sennevds/media_player.template"}, {"id": "784912901", "new": true, "full_name": "jnalepka/grenton-to-homeassistant"}, {"id": "967681366", "new": true, "full_name": "bairnhard/fishing_assistant"}, {"id": "305188358", "full_name": "midstar/heatmiser_wifi_ha"}, {"id": "745994028", "new": true, "full_name": "mcolyer/home-assistant-anova-nano"}, {"id": "424346523", "full_name": "9a4gl/hass-centrometal-boiler"}, {"id": "360213486", "full_name": "roleoroleo/yi-hack_ha_integration"}, {"id": "590806135", "full_name": "amosyuen/ha-registry"}, {"id": "210966517", "full_name": "snicker/zwift_hass"}, {"id": "210194956", "full_name": "Limych/ha-car_wash"}, {"id": "176018567", "full_name": "custom-components/gpodder"}, {"id": "600178779", "full_name": "Pim<PERSON><PERSON>/ha-sessy"}, {"id": "235943258", "full_name": "jason0x43/hacs-hubitat"}, {"id": "166045890", "full_name": "pippyn/Home-Assistant-<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": "531321012", "full_name": "lewei50/ha_iammeter"}, {"id": "520565579", "full_name": "danielsmith-eu/home-assistant-themeparks-integration"}, {"id": "913052292", "new": true, "full_name": "ad-ha/kidschores-ha"}, {"id": "1035804044", "new": true, "full_name": "markaggar/Water-Monitor"}, {"id": "398767994", "full_name": "luuuis/hass_wibeee"}, {"id": "869393624", "new": true, "full_name": "Mattat01/insnrg_chlorinator"}, {"id": "715306521", "full_name": "deler-aziz/fuel_prices_sweden"}, {"id": "330644825", "full_name": "leikoilja/ha-google-home"}, {"id": "200897141", "full_name": "thomasloven/hass-lovelace_gen"}, {"id": "946381944", "new": true, "full_name": "veista/exsys_usb_hub"}, {"id": "940917147", "new": true, "full_name": "Chris<PERSON><PERSON>/clickpi_garage_door"}, {"id": "542686924", "full_name": "JaccoR/hass-entso-e"}, {"id": "125756318", "full_name": "amaximus/bkk_stop"}, {"id": "292081477", "full_name": "parautenbach/hass-shairport-sync"}, {"id": "900052089", "new": true, "full_name": "kgn3400/trafikmeldinger"}, {"id": "755108684", "full_name": "alexdelprete/ha-4noks-elios4you"}, {"id": "180651910", "full_name": "custom-components/zaptec"}, {"id": "285560672", "full_name": "FL550/dwd_weather"}, {"id": "340759468", "full_name": "muxa/home-assistant-niwa-tides"}, {"id": "965955873", "new": true, "full_name": "Sheep26/huawei_hg659"}, {"id": "657461653", "full_name": "vakio-ru/vakio_atmosphere"}, {"id": "927381685", "new": true, "full_name": "bkbilly/seismoi"}, {"id": "531891521", "full_name": "sh00t2kill/petoneer_custom_component"}, {"id": "356033332", "full_name": "BigNocciolino/CryptoTracker"}, {"id": "936599368", "new": true, "full_name": "jasonwragg/home-assistant-ready<PERSON><PERSON><PERSON>"}, {"id": "800568407", "full_name": "valentinfrlch/ha-llmvision"}, {"id": "206868881", "full_name": "Limych/ha-gismeteo"}, {"id": "464877047", "full_name": "samuolis/brink"}, {"id": "402156016", "full_name": "golles/ha-kamstrup_403"}, {"id": "451416145", "full_name": "jwillemsen/daikin_onecta"}, {"id": "270386127", "full_name": "gicamm/homeassistant-comelit"}, {"id": "682685628", "full_name": "hugobloem/stateful_scenes"}, {"id": "968037902", "new": true, "full_name": "azsaurr/ha_usms"}, {"id": "742459118", "new": true, "full_name": "jordanruthe/homeassistant-phyn"}, {"id": "550121200", "full_name": "Jezza34000/homeassistant_weback_component"}, {"id": "265916869", "full_name": "vanstinator/hass-raincloud"}, {"id": "309195773", "full_name": "gtjadsonsantos/vapix"}, {"id": "731056300", "full_name": "andrea-matti<PERSON>/bticino_x8000_component"}, {"id": "408429126", "full_name": "koying/mqtt_discoverystream_ha"}, {"id": "1005570542", "new": true, "full_name": "M1R4G376/New_bestway_spa"}, {"id": "481763130", "full_name": "dgomes/ha_generic_water_heater"}, {"id": "733525527", "full_name": "jdrozdnovak/ha_pagerduty"}, {"id": "541978646", "full_name": "DeerMaximum/Technische-Alternative-CMI"}, {"id": "940915818", "new": true, "full_name": "DeLuca21/ynab-ha"}, {"id": "390073284", "repository_manifest": {"name": "Sonos Cloud", "country": "US", "homeassistant": "2022.8.0", "render_readme": true}, "full_name": "jjlawren/sonos_cloud", "authors": ["@jjlawren"], "category": "integration", "description": "Sonos cloud API integration for Home Assistant with improved TTS/alerts handling", "domain": "sonos_cloud", "etag_repository": "W/\"34f2257a880e0dd5d97f316dd68eb467e088cd4f1a33c7ccbfb5c3cc24e65dfb\"", "last_updated": "2024-07-18T02:54:28Z", "stargazers_count": 128, "topics": ["sonos"], "installed_commit": "b5daf84", "installed": true, "last_commit": "b5daf84", "last_version": "0.3.5", "manifest_name": "Sonos Cloud", "releases": true, "version_installed": "0.3.5", "last_fetched": 1756197405.970031}, {"id": "382335433", "full_name": "djansen1987/SAJeSolar"}, {"id": "237695750", "full_name": "bacco007/sensor.opennem"}, {"id": "246939713", "full_name": "jonk<PERSON>ian/wasteplan_trv"}, {"id": "325635211", "full_name": "kesterae<PERSON><PERSON>t/dobiss"}, {"id": "268118148", "full_name": "py-smart-gardena/hass-gardena-smart-system"}, {"id": "1027547963", "new": true, "full_name": "Master13011/SNCF-API-HA"}, {"id": "335750566", "full_name": "metbril/home-assistant-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": "342208616", "full_name": "dm82m/hass-Deltasol-KM2"}, {"id": "922645530", "new": true, "full_name": "vgcouso/ha-tracker"}, {"id": "197983504", "full_name": "rdehuyss/homeassistant-custom_components-denkovi"}, {"id": "729549493", "new": true, "full_name": "altfoxie/ha-sberdevices"}, {"id": "261031401", "full_name": "youdroid/home-assistant-couch<PERSON><PERSON>"}, {"id": "894120201", "new": true, "full_name": "einFreak/ha-regensburg-transport"}, {"id": "604796673", "full_name": "myTselection/Carbu_com"}, {"id": "728852983", "full_name": "firstof9/ha-gasbuddy"}, {"id": "373750934", "full_name": "cnstudio/Taipower-Bimonthly-Energy-Cost-homeassistant"}, {"id": "146510412", "full_name": "custom-components/climate.e_thermostaat"}, {"id": "401856574", "full_name": "jobvk/Home-Assistant-Windcentrale"}, {"id": "668937922", "full_name": "dalinicus/homeassistant-acinfinity"}, {"id": "164155243", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>/goodwe-sems-home-assistant"}, {"id": "941470905", "new": true, "full_name": "enoch85/ovms-home-assistant"}, {"id": "188698828", "full_name": "dext0r/yandex_smart_home"}, {"id": "904391556", "new": true, "full_name": "geappliances/geappliances-integration"}, {"id": "779795591", "new": true, "full_name": "littleyoda/ha-pysmaplus"}, {"id": "416883534", "full_name": "vincentwolsink/home_assistant_micronova_agua_iot"}, {"id": "749784673", "new": true, "full_name": "aronkahrs-us/inumet-weather-ha"}, {"id": "430818561", "full_name": "WillCodeForCats/tekmar-482"}, {"id": "976282396", "new": true, "full_name": "jordanhinks/abc_council_bin_collection"}, {"id": "938992686", "new": true, "full_name": "aykutvr/smartcosa-home-assistant-integration"}, {"id": "477001098", "full_name": "kongo09/philips-airpurifier-coap"}, {"id": "259865897", "full_name": "Bouni/drkblut<PERSON>ende"}, {"id": "658074877", "full_name": "Ludy87/ipv64"}, {"id": "1024955154", "new": true, "full_name": "myny-git/smappee_ev"}, {"id": "603596768", "full_name": "Andre0512/hon"}, {"id": "601894276", "full_name": "leonardlcl/general_link"}, {"id": "132661981", "full_name": "legrego/homeassistant-elasticsearch"}, {"id": "251039581", "full_name": "hultenvp/solis-sensor"}, {"id": "403401396", "full_name": "eifinger/hass-here-weather"}, {"id": "1003838951", "new": true, "full_name": "kgstorm/home-assistant-calorie-tracker"}, {"id": "912524945", "new": true, "full_name": "cnecrea/erovinieta"}, {"id": "858059476", "new": true, "full_name": "Ibepower/Ibepower-Homeassistant-Integration"}, {"id": "295523408", "full_name": "epoplavskis/homeassistant_salus"}, {"id": "121934877", "full_name": "syssi/xiaomi_airconditioningcompanion"}, {"id": "584497784", "full_name": "mampfes/ha_freeair_connect"}, {"id": "309178213", "full_name": "gtjadsonsantos/controlid"}, {"id": "625309779", "full_name": "geert<PERSON><PERSON>man/nexxtmove"}, {"id": "689755820", "full_name": "ni<PERSON><PERSON><PERSON><PERSON>/akuvox"}, {"id": "213950645", "full_name": "exKAjFASH/media_player.elkoep_lara"}, {"id": "799096673", "new": true, "full_name": "wuwentao/midea_ac_lan"}, {"id": "311594993", "full_name": "slashback100/presence_simulation"}, {"id": "322881712", "full_name": "juacas/honor_x3"}, {"id": "362145464", "full_name": "Petro31/ha-integration-multizone-controller"}, {"id": "624779425", "full_name": "aturri/ha-zcsazzurro"}, {"id": "788641369", "new": true, "full_name": "avishorp/sync_group"}, {"id": "505066911", "full_name": "mathieu-mp/homeassistant-intex-spa"}, {"id": "879827747", "new": true, "full_name": "jrmattila/ha-elenia"}, {"id": "909577154", "new": true, "full_name": "cnecrea/eonromania"}, {"id": "977126659", "new": true, "full_name": "mguyard/hass-iopool"}, {"id": "970521116", "new": true, "full_name": "KipK/marees_france"}, {"id": "523485043", "full_name": "killer0071234/ha-hiq"}, {"id": "565137157", "full_name": "sftgunner/edinplus-integration"}, {"id": "220313935", "full_name": "turbokongen/hass-AMS"}, {"id": "483269510", "full_name": "chises/ha-oilfox"}, {"id": "*********", "full_name": "astrandb/miele"}, {"id": "*********", "new": true, "full_name": "normcyr/home-assistant-montre<PERSON>-aqi"}, {"id": "*********", "new": true, "full_name": "aropop/home-assistant-cronicle"}, {"id": "*********", "full_name": "macxq/foxess-ha"}, {"id": "*********", "full_name": "<PERSON>ot<PERSON><PERSON><PERSON><PERSON>/Home-Assistant-custom-components-Custom-Templates"}, {"id": "**********", "new": true, "full_name": "remimikalsen/sparebank1_pengerobot"}, {"id": "*********", "full_name": "leonardlcl/mhtzn"}, {"id": "*********", "full_name": "robbrad/UKBinCollectionData"}, {"id": "*********", "full_name": "jscruz/sensor.carbon_intensity_uk"}, {"id": "*********", "full_name": "OpenEPaperLink/Home_Assistant_Integration"}, {"id": "*********", "new": true, "full_name": "IoTFenster/MySmartWindow"}, {"id": "*********", "full_name": "custom-components/weatheralerts"}, {"id": "*********", "full_name": "rsnodgrass/hass-xantech"}, {"id": "*********", "new": true, "full_name": "pveiga90/What-s-up-Docker-Updates-Monitor"}, {"id": "*********", "full_name": "fondberg/spotcast"}, {"id": "*********", "full_name": "mag1024/bosch-alarm-homeassistant"}, {"id": "*********", "full_name": "henricm/ha-ferroamp"}, {"id": "*********", "repository_manifest": {"name": "Bambu Lab", "render_readme": true, "homeassistant": "2025.1.1", "zip_release": true, "filename": "bambu_lab.zip"}, "full_name": "greghesp/ha-bambulab", "authors": ["@greghesp", "@AdrianGarside"], "category": "integration", "description": "A Home Assistant Integration for Bambu Lab Printers", "domain": "bambu_lab", "downloads": 36004, "etag_repository": "W/\"150ca3ef63cc148b72a73798add683b5e823825cad96f0460a235b0e485d6815\"", "last_updated": "2025-08-25T06:04:44Z", "stargazers_count": 1664, "topics": ["3d-printing", "bambulab", "homeassistant-custom-component", "iot"], "installed_commit": "45d73ea", "installed": true, "last_commit": "9996ac0", "last_version": "v2.1.27", "manifest_name": "Bambu Lab", "prerelease": "v2.1.28-beta8", "releases": true, "version_installed": "v2.1.27", "last_fetched": 1756419863.791054}, {"id": "773976846", "new": true, "full_name": "rine77/homeassistantedupage"}, {"id": "985400384", "new": true, "full_name": "db1996/homeassistant_runelite"}, {"id": "938251584", "new": true, "full_name": "ALERTua/hass-gaggiuino"}, {"id": "1026027494", "new": true, "full_name": "klaptafel/ha-previous-state-tracker"}, {"id": "523718938", "new": true, "full_name": "eriknn/ha-pax_ble"}, {"id": "501618674", "full_name": "andrzejchm/blebox_shutterbox_tilt"}, {"id": "643517852", "new": true, "full_name": "ThaStealth/alfen_modbus"}, {"id": "356778495", "repository_manifest": {"name": "WebRTC Camera", "homeassistant": "2023.2.0", "render_readme": true}, "full_name": "AlexxIT/WebRTC", "authors": ["@AlexxIT"], "category": "integration", "description": "Home Assistant custom component for real-time viewing of almost any camera stream using WebRTC and other technologies.", "domain": "webrtc", "etag_repository": "W/\"bdbbcc4c549aed79b99aa4f30fd749952b729e294f52afda2c68f6934b9da5ea\"", "last_updated": "2025-08-15T09:26:00Z", "stargazers_count": 1835, "topics": ["ip-camera", "mediasource-extensions", "rtsp", "webrtc"], "installed_commit": "ec268cd", "installed": true, "last_commit": "cd84c4b", "last_version": "v3.6.1", "manifest_name": "WebRTC Camera", "releases": true, "version_installed": "v3.6.1", "last_fetched": 1756449006.080416}, {"id": "254203764", "full_name": "ben8p/home-assistant-bunq-balance-sensors"}, {"id": "845456548", "new": true, "full_name": "jampez77/RoyalMail"}, {"id": "334364176", "full_name": "tom-winkler/ha-webfleet-integration"}, {"id": "925228685", "new": true, "full_name": "matfroh/abl_emh1_modbus"}, {"id": "260169906", "full_name": "<PERSON><PERSON>/luxtronik"}, {"id": "463652459", "full_name": "Nazze/ha_best_bottrop_garbage_collection"}, {"id": "402612874", "full_name": "iprak/weatherapi"}, {"id": "200073618", "full_name": "dlashua/template<PERSON>rysensor"}, {"id": "942670390", "new": true, "full_name": "lnagel/hass-komfovent"}, {"id": "269205129", "full_name": "h4de5/home-assistant-vimar"}, {"id": "261873234", "full_name": "<PERSON><PERSON><PERSON><PERSON>-ST/sector"}, {"id": "930549083", "new": true, "full_name": "airalab/altruist-homeassistant-integration"}, {"id": "140618233", "full_name": "RobHofmann/HomeAssistant-GreeClimateComponent"}, {"id": "330645002", "full_name": "MTrab/danfoss_ally"}, {"id": "502322534", "new": true, "full_name": "dala318/python_poollab"}, {"id": "245694520", "full_name": "asev/homeassistant-helios"}, {"id": "245267534", "full_name": "twrecked/hass-virtual"}, {"id": "178101579", "full_name": "custom-components/sensor.airthings_wave"}, {"id": "346536654", "full_name": "sopelj/hass-ember-mug-component"}, {"id": "456340193", "full_name": "daniel<PERSON>vard/homeassistant-innova"}, {"id": "506356147", "full_name": "gvigroux/hon"}, {"id": "555675718", "full_name": "sHedC/homeassistant-mastertherm"}, {"id": "606225263", "full_name": "tronikos/google_assistant_sdk_custom"}, {"id": "162808336", "full_name": "bigbadblunt/homeassistant-lightwave2"}, {"id": "606857916", "full_name": "stackia/ha-deye-dehumidifier"}, {"id": "375838748", "full_name": "Kannix2005/homeassistant-selve"}, {"id": "904832904", "new": true, "full_name": "rinyakok/homeassistant_idokep"}, {"id": "612717689", "full_name": "amitfin/retry"}, {"id": "943528465", "new": true, "full_name": "DigitallyRefined/ha-cloudflare-speed-test"}, {"id": "854628131", "new": true, "full_name": "jampez77/PremierInn"}, {"id": "460108030", "full_name": "syssi/homeassistant-goecharger-mqtt"}, {"id": "441028036", "full_name": "dvd-dev/hilo"}, {"id": "720761730", "full_name": "<PERSON><PERSON><PERSON><PERSON>-ST/attribute_as_sensor"}, {"id": "223993584", "full_name": "custom-components/ble_monitor"}, {"id": "637354954", "full_name": "geer<PERSON><PERSON><PERSON><PERSON>/robonect"}, {"id": "254253124", "full_name": "konnected-io/noonlight-hass"}, {"id": "369774988", "full_name": "petergridge/openweathermaphistory"}, {"id": "935619783", "new": true, "full_name": "etiennec78/ha-celcat"}, {"id": "674291780", "full_name": "CodeFoodPixels/robovac"}, {"id": "129049262", "full_name": "syssi/xiaomi_fan"}, {"id": "945483634", "new": true, "full_name": "zubir2k/homeassistant-dailyhadith"}, {"id": "808023453", "full_name": "cabberley/HA_RedbackTech"}, {"id": "232077394", "full_name": "kirei/hass-chargeamps"}, {"id": "209996125", "full_name": "Poeschl/Remote-PicoTTS"}, {"id": "910266956", "new": true, "full_name": "cnecrea/myelectrica"}, {"id": "264415552", "full_name": "Pyhass/Hive-Custom-Component"}, {"id": "299556199", "full_name": "xraver/mercedes_me_api"}, {"id": "243122556", "full_name": "Hellowlol/ha-tide"}, {"id": "342427139", "full_name": "HASwitchPlate/openHASP-custom-component"}, {"id": "860703144", "new": true, "full_name": "martinstafford/daikin_d3net"}, {"id": "948427490", "new": true, "full_name": "dave-code-ruiz/uponorX265"}, {"id": "410667735", "full_name": "kubawolanin/ha-reaper"}, {"id": "620855349", "full_name": "fredck/lightener"}, {"id": "461906076", "full_name": "markvader/ha-rpi_rf"}, {"id": "183064800", "full_name": "ljmerza/ha-email-sensor"}, {"id": "724237710", "new": true, "full_name": "chriscamicas/gazdebordeaux-ha"}, {"id": "264490983", "full_name": "GeorgeSG/ha-slack-user"}, {"id": "831558453", "new": true, "full_name": "EnzoD86/tuya-smart-ir-ac"}, {"id": "942085979", "new": true, "full_name": "domness/ha_glowdreaming"}, {"id": "688502429", "full_name": "AlexxIT/Jura"}, {"id": "532472578", "full_name": "greghesp/ha-evonic"}, {"id": "750336940", "new": true, "full_name": "thomluther/ha-anker-solix"}, {"id": "946208751", "new": true, "full_name": "chris-mc1/unraid_api"}, {"id": "526379993", "full_name": "0xAlon/tami4edge"}, {"id": "291484700", "repository_manifest": {"name": "Xiaomi Gateway 3", "homeassistant": "2023.2.0", "render_readme": true}, "full_name": "AlexxIT/XiaomiGateway3", "authors": ["@AlexxIT"], "category": "integration", "description": "Home Assistant custom component for control Xiaomi Multimode Gateway (aka Gateway 3), Xiaomi Multimode Gateway 2, Aqara Hub E1 on default firmwares over LAN", "domain": "xiaomi_gateway3", "etag_repository": "W/\"23db75f372e119f70d437f07063a175ba9ded0f458a54beb39200c71e8159ea1\"", "last_updated": "2025-08-27T12:51:22Z", "stargazers_count": 2653, "topics": ["aqara", "ble", "matter", "mesh", "mihome", "xia<PERSON>", "zha", "zigbee", "zigbee2mqtt"], "installed_commit": "2ff4d76", "installed": true, "last_commit": "fc3ce25", "last_version": "v4.1.2", "manifest_name": "Xiaomi Gateway 3", "releases": true, "version_installed": "v4.1.2", "last_fetched": 1756405758.748711}, {"id": "319608056", "full_name": "dckiller51/bodymiscale"}, {"id": "392188182", "full_name": "alryaz/hass-energosbyt-plus"}, {"id": "243841075", "full_name": "lociii/homeassistant-csgo"}, {"id": "127689312", "full_name": "claudegel/sinope-1"}, {"id": "983414241", "new": true, "full_name": "zollak/homeassistant-syslog-receiver"}, {"id": "366332990", "repository_manifest": {"name": "Electrolux Wellbeing", "hacs": "1.6.0", "homeassistant": "2025.1.0"}, "full_name": "JohNan/homeassistant-wellbeing", "authors": ["@JohNan"], "category": "integration", "description": "Get the status from your Electrolux devices connected to Wellbeing", "domain": "wellbeing", "etag_repository": "W/\"cef575d8e572d976b2bf4df2bc528e2bb68bda2a4a4b014b20eb52cdaf66d0bb\"", "last_updated": "2025-07-11T10:04:01Z", "stargazers_count": 101, "topics": ["electrolux", "electrolux-wellbeing", "wellbeing"], "installed_commit": "bc8b039", "installed": true, "last_commit": "bc8b039", "last_version": "v1.3.9", "manifest_name": "Electrolux Wellbeing", "releases": true, "version_installed": "v1.3.9", "last_fetched": 1756298916.211598}, {"id": "296320952", "full_name": "fineemb/xiaomi-cloud"}, {"id": "299875200", "full_name": "toreamun/victorsmartkill-homeassistant"}, {"id": "759368542", "new": true, "full_name": "GuyKh/iec-custom-component"}, {"id": "562404203", "full_name": "olibos/HomeAssistant-RecycleApp"}, {"id": "325329098", "full_name": "IATkachenko/HA-SleepAsAndroid"}, {"id": "157618389", "full_name": "thomasloven/hass-fontawesome"}, {"id": "908968737", "new": true, "full_name": "krozgrov/ha-omlet-integration"}, {"id": "648761057", "full_name": "pkarimov/jukeaudio_ha"}, {"id": "478745957", "full_name": "droso-hass/idfm"}, {"id": "432516566", "full_name": "Breina/idrac_power_monitor"}, {"id": "340596609", "repository_manifest": {"name": "Panasonic Smart App", "homeassistant": "2024.1.2"}, "full_name": "osk2/panasonic_smart_app", "authors": ["@osk2"], "category": "integration", "description": "🔛 Panasonic Smart App integration for Home Assistant.", "domain": "panasonic_smart_app", "etag_repository": "W/\"b9089d1f86512794439ed056626da43e8a127ad76ecc5e76bb61938649dd405e\"", "last_updated": "2025-08-13T14:52:34Z", "stargazers_count": 117, "topics": ["panasonic"], "installed_commit": "1ad2e97", "installed": true, "last_commit": "718dc0b", "last_version": "v2.12.0", "manifest_name": "Panasonic Smart App", "releases": true, "version_installed": "v2.12.0", "last_fetched": 1756226158.957172}, {"id": "997906487", "new": true, "full_name": "zhe<PERSON><PERSON>/mijn_waterlink"}, {"id": "591077142", "full_name": "myTselection/bibliotheek_be"}, {"id": "994455598", "new": true, "full_name": "snell-evan-itt/EG4-Inverter"}, {"id": "899895593", "new": true, "full_name": "DominikStarke/becker_centralcontrol_has"}, {"id": "234875951", "full_name": "vlum<PERSON>ro/home-assistant-securitas"}, {"id": "242335771", "full_name": "lindell/home-assistant-svt-play"}, {"id": "995472729", "new": true, "full_name": "Korkuttum/tuya_body_fat_scale"}, {"id": "231989179", "full_name": "JAAlperin/hass-bardolph"}, {"id": "897180376", "new": true, "full_name": "dala318/ev_load_balancing"}, {"id": "929513609", "new": true, "full_name": "se<PERSON><PERSON><PERSON><PERSON>/neptun_smart_local"}, {"id": "975378118", "new": true, "full_name": "JortvanSchijndel/FusionSolarPlus"}, {"id": "378010382", "full_name": "alryaz/hass-tns-energo"}, {"id": "520791578", "full_name": "ProudElm/solaredgeoptimizers"}, {"id": "936633599", "new": true, "full_name": "jrgim/Zaragoza_tram"}, {"id": "787732252", "full_name": "geer<PERSON><PERSON><PERSON><PERSON>/yoin"}, {"id": "229140999", "full_name": "cathiele/homeassistant-goecharger"}, {"id": "224374747", "full_name": "5high/phicomm-dc1-homeassistant"}, {"id": "580152298", "full_name": "Aohzan/hass-polar"}, {"id": "241427839", "full_name": "robbinjanssen/home-assistant-omnik-inverter"}, {"id": "311913208", "full_name": "gazoodle/gecko-home-assistant"}, {"id": "695365328", "full_name": "popeen/Home-Assistant-Custom-Component-<PERSON><PERSON><PERSON>-<PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"id": "363468409", "full_name": "mbillow/ha-redpocket"}, {"id": "965510183", "new": true, "full_name": "herruzo99/ista-calista"}, {"id": "175020245", "full_name": "JurajNyiri/HomeAssistant-Tavos"}, {"id": "232813686", "full_name": "<PERSON><PERSON><PERSON><PERSON>/Home_Assistant_SkyQ_MediaPlayer"}, {"id": "932653667", "new": true, "full_name": "ilpianista/meteoeuregio"}, {"id": "606563418", "full_name": "HrGaertner/HA-vent-optimization"}, {"id": "935991678", "new": true, "full_name": "Master13011/Mailcow-HA"}, {"id": "202220932", "full_name": "thomasloven/hass-favicon"}, {"id": "582798865", "full_name": "chkuendig/hass-amphiro-ble"}, {"id": "1018578416", "new": true, "full_name": "wbyoung/lampie"}, {"id": "943936900", "new": true, "full_name": "sathia-musso/enelgrid"}, {"id": "747418323", "full_name": "MineTech13/homeassistant-basestation"}, {"id": "201599575", "full_name": "gcorgnet/sensor.emby_upcoming_media"}, {"id": "413680511", "full_name": "widewing/ha-toyota-na"}, {"id": "905825860", "new": true, "full_name": "trevorwarwick/minidspshd"}, {"id": "419381725", "full_name": "wlcrs/huawei_solar"}, {"id": "454951296", "full_name": "thecode/ha-rpi_gpio"}, {"id": "451666485", "new": true, "full_name": "c0un7-z3r0/hass-phoniebox"}, {"id": "560073671", "new": true, "full_name": "kcsoft/virtual-keys"}, {"id": "256928191", "full_name": "Aohzan/ecodevices"}, {"id": "607493281", "full_name": "austinmroczek/neovolta"}, {"id": "674163098", "full_name": "danieldotnl/ha-measureit"}, {"id": "786470226", "full_name": "cdnninja/yoto_ha"}, {"id": "886026202", "new": true, "full_name": "sedward5/nws_spc_outlook"}, {"id": "215825339", "full_name": "alryaz/hass-hekr-component"}, {"id": "577080457", "full_name": "iprak/sensi"}, {"id": "993614841", "new": true, "full_name": "smarthomeblack/npc"}, {"id": "954893927", "new": true, "full_name": "MiguelAngelLV/ha-awtrix"}, {"id": "375355221", "new": true, "full_name": "CharlesGillanders/homeassistant-alphaESS"}, {"id": "221855213", "full_name": "jcwillox/hass-auto-backup"}, {"id": "872941557", "new": true, "full_name": "timniklas/hass-smartme"}, {"id": "257275420", "full_name": "tomaae/homeassistant-openmediavault"}, {"id": "651666000", "full_name": "BottlecapDave/HomeAssistant-HarvestTimeTracker"}, {"id": "272432260", "full_name": "algirdasc/hass-floureon"}, {"id": "443297453", "full_name": "Blear/HassLife"}, {"id": "290193894", "full_name": "sillyfrog/Automate-Pulse-v2"}, {"id": "863016768", "new": true, "full_name": "Thank-you-<PERSON><PERSON>/<PERSON><PERSON>-Dashboard"}, {"id": "290261325", "repository_manifest": {"name": "Adaptive Lighting", "render_readme": true, "homeassistant": "2024.12.0"}, "full_name": "basnijholt/adaptive-lighting", "authors": ["@basnijholt", "@RubenKelevra", "@th3w1zard1", "@protyposis"], "category": "integration", "description": "Adaptive Lighting custom component for Home Assistant", "domain": "adaptive_lighting", "etag_repository": "W/\"58e246830c539acd99279b35093c285152355d92db24e661fc3ef18818f78388\"", "last_updated": "2025-08-25T18:39:22Z", "stargazers_count": 2488, "topics": ["adaptive-lighting", "automation", "hue", "iot", "lights", "sunrise", "sunset", "zigbee"], "installed_commit": "7629002", "installed": true, "last_commit": "0a2d2e9", "last_version": "v1.26.0", "manifest_name": "Adaptive Lighting", "releases": true, "version_installed": "v1.26.0", "last_fetched": 1756441608.051886}, {"id": "811526724", "full_name": "connor<PERSON>opo/leslies-pool"}, {"id": "965657216", "new": true, "full_name": "0jety0/emaux_spv150"}, {"id": "448604854", "full_name": "TomBrien/cardiffwaste-ha"}, {"id": "480112024", "full_name": "patrickribbing/sjofartsverket_viva-component"}, {"id": "1016625747", "new": true, "full_name": "nitaybz/optimistic_feedback"}, {"id": "1008567206", "new": true, "full_name": "Raznor09/moving_intelligence"}, {"id": "358505160", "full_name": "eif<PERSON>/hass-weenect"}, {"id": "812675574", "new": true, "full_name": "denysdovhan/ha-check-weather"}, {"id": "1037892663", "new": true, "full_name": "Hyrla/integration_bwt_cosmy_ha"}, {"id": "877068794", "new": true, "full_name": "jaidenlabelle/tuya-vacuum-maps"}, {"id": "992839141", "new": true, "full_name": "krissen/polleninformation"}, {"id": "363612099", "new": true, "full_name": "leorbs/airco2ntrol"}, {"id": "903888455", "new": true, "full_name": "OpenGrow-Box/OpenGrowBox-HA"}, {"id": "525588589", "full_name": "cjaliaga/home-assistant-a<PERSON><PERSON>"}, {"id": "566821503", "new": true, "full_name": "timniklas/hass-govee_light_ble"}, {"id": "553010184", "full_name": "SpanPanel/span"}, {"id": "615865173", "new": true, "full_name": "kgn3400/pypi_updates"}, {"id": "539629703", "full_name": "Danieldiazi/homeassistant-meteogalicia"}, {"id": "136170574", "full_name": "c1pher-cn/homeassistan-ezviz"}, {"id": "362513331", "full_name": "shaiu/technicolor"}, {"id": "344636306", "full_name": "wimb0/home-assistant-saj-r5-modbus"}, {"id": "584473299", "full_name": "Pigotka/ha-cc-jablotron-cloud"}, {"id": "1012692788", "new": true, "full_name": "msinhore/adaptive_climate"}, {"id": "620107388", "full_name": "codyc1515/ha-em6"}, {"id": "289251122", "full_name": "mletenay/home-assistant-goodwe-inverter"}, {"id": "299753146", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/Home-Assistant-custom-components-<PERSON><PERSON>-Cloud-Map-Extractor"}, {"id": "936215435", "new": true, "full_name": "brewmarsh/meraki-homeassistant"}, {"id": "468093553", "full_name": "amaximus/radioactivity_hu"}, {"id": "539665010", "new": true, "full_name": "dala318/nordpool_planner"}, {"id": "272094506", "full_name": "mrk-its/homeassistant-blitzortung"}, {"id": "537001731", "full_name": "emes30/facebook_messenger"}, {"id": "598381225", "full_name": "drc38/<PERSON><PERSON><PERSON>_solarweb"}, {"id": "706873015", "full_name": "tonyroberts/hawundasmart"}, {"id": "786195990", "new": true, "full_name": "jirutka/hass-smarwi"}, {"id": "517783998", "full_name": "Vova-SH/termux-api"}, {"id": "579590312", "new": true, "full_name": "Aleks130699/ha-fpp"}, {"id": "378213601", "full_name": "nyffchanium/argoclima-integration"}, {"id": "481715988", "full_name": "PimDoos/kia_connect"}, {"id": "145777833", "full_name": "custom-components/sensor.personalcapital"}, {"id": "127251446", "full_name": "xirixiz/homeassistant-afval<PERSON>jzer"}, {"id": "766542184", "new": true, "full_name": "hsk-dk/home-assistant-thermex"}, {"id": "766648885", "full_name": "patman15/BMS_BLE-HA"}, {"id": "792639181", "new": true, "full_name": "marq24/ha-goecharger-api2"}, {"id": "892055242", "new": true, "full_name": "cataseven/Binance-Wallet-Integration-Home-Assistant"}, {"id": "518658701", "new": true, "full_name": "markfrancisonly/ha-cameralux"}, {"id": "754213636", "full_name": "alexdel<PERSON><PERSON>e/ha-sinapsi-alfa"}, {"id": "946280593", "new": true, "full_name": "remimikalsen/ollama_vision"}, {"id": "235659413", "full_name": "JonasPed/homeassistant-eloverblik"}, {"id": "735547796", "full_name": "TheGui01/Frisquet-connect-for-home-assistant"}, {"id": "570874359", "full_name": "masaccio/ha-kingspan-watchman-sensit"}, {"id": "529083424", "full_name": "jaroschek/home-assistant-myup<PERSON>"}, {"id": "487536666", "full_name": "veista/nilan"}, {"id": "711632405", "full_name": "jmc<PERSON>vel<PERSON>/little_monkey"}, {"id": "204192861", "full_name": "Limych/ha-average"}, {"id": "683619534", "full_name": "BHSPitMonkey/homeassistant-garmin-mapshare"}, {"id": "139664351", "full_name": "alandtse/alexa_media_player"}, {"id": "325097827", "full_name": "andvikt/mega_hacs"}, {"id": "283847957", "full_name": "custom-components/pyscript"}, {"id": "944688985", "new": true, "full_name": "ronald-willems/dewarmte-homeassistant"}, {"id": "361961255", "full_name": "make-all/metlink-nz"}, {"id": "992952206", "new": true, "full_name": "duwi2024/ha_duwi_home"}, {"id": "100234318", "full_name": "syssi/philipslight"}, {"id": "679713495", "full_name": "andrew-codechimp/HA-Battery-Notes"}, {"id": "264499592", "full_name": "heyajohnny/cryptoinfo"}, {"id": "641757757", "full_name": "zeronounours/HA-custom-component-energy-meter"}, {"id": "931244909", "new": true, "full_name": "j9brown/scenery"}, {"id": "959982550", "new": true, "full_name": "thecem/octopus_germany"}, {"id": "448323715", "full_name": "iMicknl/ha-nest-protect"}, {"id": "265059207", "full_name": "ThermIQ/thermiq_mqtt-ha"}, {"id": "805658427", "full_name": "MiguelAngelLV/ha-input-stats"}, {"id": "453890532", "full_name": "jrfernandes/ontario_energy_board"}, {"id": "642225418", "full_name": "alryaz/hass-turkov"}, {"id": "362058414", "full_name": "Chouffy/home_assistant_libratone_zipp"}, {"id": "380330823", "full_name": "augustas2/eldes"}, {"id": "223541049", "full_name": "jaruba/ha-samsungtv-tizen"}, {"id": "460167330", "full_name": "mattrayner/pod-point-home-assistant-component"}, {"id": "497322735", "full_name": "NemesisRE/sensor.plex_recently_added"}, {"id": "1000479285", "new": true, "full_name": "starwarsfan/shadow-control"}, {"id": "265716369", "full_name": "gilsonmandalogo/hacs-minerstat"}, {"id": "1034840428", "new": true, "full_name": "arevindh/ha-tinxy-cloud"}, {"id": "711170365", "full_name": "geert<PERSON><PERSON>man/eeveemobility"}, {"id": "316527506", "full_name": "iprak/winix"}, {"id": "716704833", "full_name": "Alexandr<PERSON><PERSON><PERSON>/home-assistant-tplink-router"}, {"id": "831322262", "new": true, "full_name": "remialban/ipx800v3"}, {"id": "634325657", "full_name": "timmo001/homeassistant-integration-goxlr-utility"}, {"id": "915423264", "new": true, "full_name": "Darkdragon14/ha-access-control-manager"}, {"id": "267433712", "full_name": "slesinger/HomeAssistant-PREdistribuce"}, {"id": "178838527", "full_name": "filipvh/hass-nhc2"}, {"id": "282688934", "full_name": "barban-dev/homeassistant-midea-dehumidifier"}, {"id": "199332790", "full_name": "snarky-snark/home-assistant-variables"}, {"id": "666816132", "full_name": "plmilord/Hass.io-custom-component-ikamand"}, {"id": "984101461", "new": true, "full_name": "ndesgranges/bing-wallpaper"}, {"id": "787253413", "new": true, "full_name": "martinarva/dynamic_energy_cost"}, {"id": "889456164", "new": true, "full_name": "figorr/meteocat"}, {"id": "597517839", "full_name": "rexave/hass-orange-internet-on-the-move"}, {"id": "962887269", "new": true, "full_name": "jsheputis/home-assistant-lifetime-fitness"}, {"id": "319744131", "full_name": "martindybal/taphome-homeassistant"}, {"id": "998347419", "new": true, "full_name": "frimtec/hass-swiss-pollen"}, {"id": "1030191174", "new": true, "full_name": "JeppeLeth/hass-ista-online"}, {"id": "966840808", "new": true, "full_name": "houthacker/remeha-modbus"}, {"id": "454859084", "full_name": "ekutner/home-connect-hass"}, {"id": "745898922", "full_name": "slydi<PERSON>/sscpoe"}, {"id": "377012187", "full_name": "jcgoette/weight_gurus_homeassistant"}, {"id": "657192016", "full_name": "<PERSON><PERSON><PERSON>/hass_kat_bulgaria"}, {"id": "846192013", "new": true, "full_name": "jlvcm/ha-actualbudget"}, {"id": "226707533", "full_name": "freol35241/ltss"}, {"id": "160022220", "full_name": "robmarkcole/HASS-amazon-rekognition"}, {"id": "369883961", "full_name": "lorenzo-deluca/homeassistant-silence"}, {"id": "900089635", "new": true, "full_name": "<PERSON><PERSON><PERSON>/honda_recall_check"}, {"id": "679361462", "full_name": "kverqus/hassam"}, {"id": "856128489", "new": true, "full_name": "jheizer/up_4014_tracker"}, {"id": "986911686", "new": true, "full_name": "hestiia-engineering/hass_sql_request"}, {"id": "485895021", "full_name": "dahlb/ha_blueair"}, {"id": "923981105", "new": true, "full_name": "cavefire/Bose-Homeassistant"}, {"id": "603834302", "full_name": "rwoldberg/ldata-ha"}, {"id": "229755760", "full_name": "garbled1/balboa_homeassistan"}, {"id": "453143227", "full_name": "Mr<PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>-Home-Assistant-Custom-Component"}, {"id": "783592918", "full_name": "peteS-UK/emotiva"}, {"id": "1015547831", "new": true, "full_name": "alves-dev/stackspot-homeassistant"}, {"id": "572278409", "full_name": "petergridge/Irrigation-V5"}, {"id": "899358813", "new": true, "full_name": "will<PERSON><PERSON>y/hassio-omlet-smartcoop-door"}, {"id": "866164198", "new": true, "full_name": "benjamin-dcs/file-plusplus"}, {"id": "356385629", "full_name": "Hyundai-Kia-Connect/kia_uvo"}, {"id": "578872078", "full_name": "mandarons/ha-bouncie"}, {"id": "706651591", "full_name": "amitfin/oref_alert"}, {"id": "605635573", "full_name": "frenck/spook"}, {"id": "750338404", "full_name": "aunefyren/bluesound_alt"}, {"id": "533014913", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/ev_smart_charging"}, {"id": "956609051", "new": true, "full_name": "Domintell/ha_domintell"}, {"id": "236572107", "full_name": "AlexxIT/YandexStation"}, {"id": "332651510", "full_name": "jcgoette/baby_buddy_homeassistant"}, {"id": "701827966", "full_name": "deadbeef3137/ha-cloudflare-tunnel-monitor"}, {"id": "884994036", "new": true, "full_name": "Hermesiss/itchio_homeassistant"}, {"id": "771239559", "full_name": "bkbilly/medisanabp_ble"}, {"id": "772764009", "new": true, "full_name": "maeek/ha-aux-cloud"}, {"id": "761624280", "new": true, "full_name": "Altrec/remko_mqtt-ha"}, {"id": "967499827", "new": true, "full_name": "svante-j<PERSON><PERSON><PERSON>/loggamera-home-assistant"}, {"id": "714066776", "full_name": "cazeaux/ha-iracing"}, {"id": "334925385", "full_name": "weltenwort/home-assistant-rct-power-integration"}, {"id": "625647772", "full_name": "geertmeersman/mobile_vikings"}, {"id": "852348573", "new": true, "full_name": "yandex/pogoda-home-assistant"}, {"id": "919237520", "new": true, "full_name": "weiangongsi/ddns"}, {"id": "608596416", "full_name": "lizardsystems/hass-taipit"}, {"id": "916247421", "new": true, "full_name": "Favio25/Kronoterm-homeassistant"}, {"id": "504337320", "full_name": "godely/ha-dremel-3d-printer"}, {"id": "560307075", "full_name": "scaarup/aula"}, {"id": "690180198", "new": true, "full_name": "SanderBlom/BIR_Waste_Watch"}, {"id": "661342098", "full_name": "KiraPC/ha-switchbot-remote"}, {"id": "419449609", "full_name": "BottlecapDave/HomeAssistant-FirstBus"}, {"id": "699440099", "new": true, "full_name": "shadow578/homeassistant_sma-ennexos"}, {"id": "504880554", "full_name": "elahd/ha-cyclepay"}, {"id": "169467285", "full_name": "elad-bar/ha-edgeos"}, {"id": "1014871677", "new": true, "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/kee<PERSON>-ha"}, {"id": "172733314", "repository_manifest": {"name": "HACS", "zip_release": true, "hide_default_branch": true, "homeassistant": "2024.4.1", "hacs": "0.19.0", "filename": "hacs.zip"}, "full_name": "hacs/integration", "authors": ["@ludeeus"], "category": "integration", "description": "HACS gives you a powerful UI to handle downloads of all your custom needs.", "domain": "hacs", "downloads": 1595963, "etag_repository": "W/\"d9b40a3dc1f20739b49210f79ebd7833139da8baca448ecf255643b1277a2f05\"", "last_updated": "2025-08-26T10:26:13Z", "stargazers_count": 6546, "topics": ["community", "package-manager"], "installed": true, "last_commit": "8157ccf", "last_version": "2.0.5", "manifest_name": "HACS", "open_issues": 50, "releases": true, "version_installed": "2.0.5", "last_fetched": 1756463763.565937}, {"id": "977820345", "new": true, "full_name": "benwittbrodt/Metra-Tracker"}, {"id": "598289640", "full_name": "RobertD502/home-assistant-petkit"}, {"id": "972136588", "new": true, "full_name": "julian<PERSON>/TempestHomeAssistant"}, {"id": "1018480711", "new": true, "full_name": "urbanframe/sunlight-intensity"}, {"id": "975958529", "new": true, "full_name": "hanwg/sg-bus-arrivals"}, {"id": "968243597", "new": true, "full_name": "sockless-coding/ha-eaton-ups-companion"}, {"id": "316597224", "full_name": "dphae/bsh"}, {"id": "696399706", "new": true, "full_name": "andreaprosseda/vimar-byme-plus-homeassistant"}, {"id": "313759590", "full_name": "ryanmac8/HA-Mint-Mobile"}, {"id": "197058358", "full_name": "jxlarrea/ha-emfitqs"}, {"id": "351828005", "full_name": "myhomeiot/DahuaVTO"}, {"id": "255139072", "full_name": "dgomes/ha_erse"}, {"id": "1002815970", "new": true, "full_name": "cvl01/ha-ovo-charge"}, {"id": "584257648", "full_name": "popeen/Home-Assistant-Custom-Component-TCL-Remote"}, {"id": "980812187", "new": true, "full_name": "DeclanSC/hass-esi-thermostat"}, {"id": "262645913", "full_name": "youdroid/home-assistant-g<PERSON>a"}, {"id": "554898014", "full_name": "tijsverkoyen/HomeAssistant-FusionSolar"}, {"id": "593275617", "full_name": "NinDTendo/homeassistant_gradual_volume_control"}, {"id": "610701268", "full_name": "nathan<PERSON><PERSON>/foxess_modbus"}, {"id": "953200276", "new": true, "full_name": "Minitour/ha-higoal"}, {"id": "823407655", "new": true, "full_name": "cabberley/HA_AemoNemData"}, {"id": "632007442", "full_name": "S<PERSON><PERSON>der/Home-Assistant-<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": "809038784", "full_name": "RogerSelwyn/MS365-ToDo"}, {"id": "934466189", "new": true, "full_name": "GyroGearl00se/ha_froeling_lambdatronic_modbus"}, {"id": "589071917", "full_name": "rrooggiieerr/homeassistant-homeduino"}, {"id": "911835258", "new": true, "full_name": "bendikrb/ha-devtools"}, {"id": "307499856", "new": true, "full_name": "ckarrie/ha-netgear-plus"}, {"id": "416059983", "full_name": "pail23/stiebel_eltron_isg_component"}, {"id": "232269564", "full_name": "5high/konke"}, {"id": "785936379", "full_name": "jeroenterheerdt/grillbuddy"}, {"id": "228579545", "full_name": "sebr/bhyve-home-assistant"}, {"id": "969976848", "new": true, "full_name": "jz-v/ha-melview"}, {"id": "491066500", "full_name": "airalab/homeassistant-robonomics-integration"}, {"id": "851571917", "new": true, "full_name": "lufton/ha_telegram_client"}, {"id": "992713904", "new": true, "full_name": "Lewa-Reka/ha-rce-pse"}, {"id": "292197182", "full_name": "hcoohb/hass-yeelightbt"}, {"id": "523291160", "full_name": "amitfin/daily_schedule"}, {"id": "240459262", "full_name": "tuxuser/abfallapi_jumomind_ha"}, {"id": "562402396", "full_name": "hmn/siku-integration"}, {"id": "633913242", "new": true, "full_name": "amaximus/ematrica_hu"}, {"id": "277201070", "full_name": "toreamun/amshan-homeassistant"}, {"id": "199718799", "full_name": "snikch/climate.escea"}, {"id": "264655935", "full_name": "gadgetchnnel/entities_calendar"}, {"id": "1013066993", "new": true, "full_name": "ALArvi019/moderntides"}, {"id": "1023094396", "new": true, "full_name": "berra200/home-assistant-rapt-cloud-link"}, {"id": "728839835", "full_name": "JonasJoKuJonas/homeassistant-trias"}, {"id": "311536795", "full_name": "blakeblackshear/frigate-hass-integration"}, {"id": "188106531", "full_name": "moralmunky/Home-Assistant-Mail-And-Packages"}, {"id": "768042531", "new": true, "full_name": "fabio-garavini/ha-openai-whisper-stt-api"}, {"id": "516625225", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>/hass-sutro"}, {"id": "988435981", "new": true, "full_name": "GuidoJeuken-6512/lambda_heat_pumps"}, {"id": "355159299", "full_name": "alryaz/hass-moscow-pgu"}, {"id": "1006534717", "new": true, "full_name": "omgitslurch/hass-unifi-ap-led"}, {"id": "159080189", "full_name": "asantaga/wiserHomeAssistantPlatform"}, {"id": "647057223", "full_name": "delphiki/hass-pronote"}, {"id": "236146080", "full_name": "binsentsu/home-assistant-solaredge-modbus"}, {"id": "893859596", "new": true, "full_name": "redpomodoro/fronius_modbus"}, {"id": "348464316", "full_name": "ec-blaster/magicswitchbot-homeassistant"}, {"id": "819248867", "new": true, "full_name": "oooohhoo/tokit_cooker"}, {"id": "193371566", "full_name": "Piotr<PERSON><PERSON><PERSON>/Home-Assistant-custom-components-Burze.dzis.net"}, {"id": "219035415", "full_name": "ptimatth/GeorideHA"}, {"id": "589430688", "full_name": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>/Home-Assistant-custom-components-GNE-PV-Monitoring"}, {"id": "594842145", "full_name": "InTheDaylight14/nginx-proxy-manager-switches"}, {"id": "**********", "new": true, "full_name": "dmoralesdev/zha_lock_manager"}, {"id": "301509152", "full_name": "BenPru/novus300_Rs485"}, {"id": "262017793", "full_name": "cagnulein/switchbot_press"}, {"id": "603218187", "full_name": "acesyde/hassio_mylight_integration"}, {"id": "997420946", "new": true, "full_name": "dhover/ha-cumulusmx"}, {"id": "698002523", "full_name": "wrodie/ha_behringer_mixer"}, {"id": "519811207", "full_name": "trvqhuy/nestup_evn"}, {"id": "560311562", "full_name": "codyc1515/ha-managemyhealth"}, {"id": "494545750", "full_name": "elden1337/hass-peaqhvac"}, {"id": "351615180", "new": true, "full_name": "rodpayne/home-assistant_person_location"}, {"id": "453785158", "full_name": "youdroid/home-assistant-gogs"}, {"id": "236611771", "full_name": "prairiesnpr/hass-tdameritrade"}, {"id": "687897593", "full_name": "flexopus/flexopus-hass-sensor"}, {"id": "177169766", "full_name": "eseglem/hass-wattbox"}, {"id": "173563704", "full_name": "custom-components/climate.programmable_thermostat"}, {"id": "121891488", "full_name": "thevoltagesource/LennoxiComfort"}, {"id": "159025199", "full_name": "kalanda/homeassistant-aemet-sensor"}, {"id": "448980525", "full_name": "mdeweerd/zha-toolkit"}, {"id": "702067512", "full_name": "MTrab/webastoconnect"}, {"id": "201805130", "full_name": "custom-components/nordpool"}, {"id": "917275650", "new": true, "full_name": "suppqt/hass_mars_hydro"}, {"id": "992177774", "new": true, "full_name": "wbyoung/movement"}, {"id": "750568094", "new": true, "full_name": "happydev-ca/evduty-home-assistant"}, {"id": "870057182", "new": true, "full_name": "jampez77/TheModernMilkman"}, {"id": "319343045", "full_name": "caronc/ha-ultrasync"}, {"id": "656612812", "full_name": "Patrick762/hassio-streamdeck"}, {"id": "831102533", "new": true, "full_name": "ngocjohn/lunar-phase"}, {"id": "574693804", "full_name": "gndean/home-assistant-hypervolt-charger"}, {"id": "256899380", "full_name": "atymic/project_three_zero_ha"}, {"id": "406939721", "full_name": "tomas<PERSON>rich/home-assistant-hikconnect"}, {"id": "258852884", "full_name": "laszlojakab/homeassistant-easycontrols"}, {"id": "650965476", "full_name": "daernsinstantfortress/cupra_we_connect"}, {"id": "269588712", "full_name": "mvdwetering/huesyncbox"}, {"id": "207881337", "full_name": "pinkywafer/Anniversaries"}, {"id": "669605185", "new": true, "full_name": "beecho01/material-symbols"}, {"id": "585689945", "full_name": "popeen/Home-Assistant-Custom-Component-Temperatur-Nu"}, {"id": "756168276", "full_name": "klausj1/homeassistant-statistics"}, {"id": "193588464", "full_name": "Piotr<PERSON><PERSON><PERSON>/Home-Assistant-custom-components-Rozkladzik"}, {"id": "583379046", "full_name": "Strixx76/mold_risk_index"}, {"id": "632003835", "full_name": "grimmpp/home-assistant-<PERSON><PERSON><PERSON>"}, {"id": "340664955", "full_name": "dimagoltsman/ha-proof-dashcam-integration"}, {"id": "847508587", "new": true, "full_name": "MattDahEpic/ha-midas"}, {"id": "789397431", "full_name": "Tvalley71/dantherm"}, {"id": "467638459", "full_name": "joleys/niko-home-control-II"}, {"id": "717139656", "full_name": "Mat931/digitalstrom-homeassistant"}, {"id": "760282660", "full_name": "sHedC/homeassistant-leakbot"}, {"id": "988647442", "new": true, "full_name": "ClusterM/flipper_rc"}, {"id": "878538117", "new": true, "full_name": "<PERSON><PERSON><PERSON><PERSON>/da<PERSON>ch-home-assistant"}, {"id": "961160162", "new": true, "full_name": "DomoticaFacile/raccolta_rifiuti"}, {"id": "979038334", "new": true, "full_name": "Beat2er/homeassistant-trmnl-battery"}, {"id": "250688607", "full_name": "boralyl/steam-wishlist"}, {"id": "200665691", "full_name": "franc6/ics_calendar"}, {"id": "874336081", "new": true, "full_name": "timniklas/hass-fitnesspark"}, {"id": "783745805", "full_name": "unii-security/homeassistant-unii"}, {"id": "777338510", "full_name": "avlemos/dobiss"}, {"id": "876714558", "new": true, "full_name": "thedeemling/hass-energa-my-meter"}, {"id": "718853287", "new": true, "full_name": "SDR3078/ps3-home-assistant"}, {"id": "505598474", "full_name": "netsoft-ruidias/ha-custom-component-precoscombustiveis"}, {"id": "738003407", "full_name": "TheNoctambulist/hass-airtouch"}, {"id": "831669754", "new": true, "full_name": "Brunas/meteo_lt"}, {"id": "309018094", "full_name": "itchannel/fordpass-ha"}, {"id": "924302905", "new": true, "full_name": "wachino/orkli_wifi_thermostat"}, {"id": "944755135", "new": true, "full_name": "tomer-w/ha-nmea2000"}, {"id": "593695273", "full_name": "jampez77/DVLA-Vehicle-Enquiry-Service"}, {"id": "412552305", "new": true, "full_name": "sillymoi/homeassistant-infometric"}, {"id": "732152906", "full_name": "geert<PERSON>rsman/cloudlibrary"}, {"id": "625029904", "new": true, "full_name": "kgn3400/docker_status"}, {"id": "233092112", "full_name": "kodi1/songpal_m"}, {"id": "101482973", "full_name": "syssi/xiaomi_airpurifier"}, {"id": "809492726", "full_name": "lhw/cloudweatherproxy"}, {"id": "1007812765", "new": true, "full_name": "BigPiloto/ha-drugstore-stock"}, {"id": "360408082", "full_name": "RobertD502/home-assistant-flair"}, {"id": "959115055", "new": true, "full_name": "ebertek/ssm"}, {"id": "171854441", "full_name": "custom-components/youtube"}, {"id": "728966752", "full_name": "parvez/network_scanner"}, {"id": "448126069", "new": true, "full_name": "timstallmann/home-assistant-we<PERSON><PERSON>"}, {"id": "442001863", "full_name": "amosyuen/ha-tplink-deco"}, {"id": "930945407", "new": true, "full_name": "flame4ever/homeassistant-controme-integration"}, {"id": "318182014", "full_name": "al-one/hass-xiaomi-miot"}, {"id": "318359434", "full_name": "FutureTense/keymaster"}, {"id": "433577406", "full_name": "junkfix/config-editor"}, {"id": "512922944", "full_name": "rnovacek/homeassistant_cz_energy_spot_prices"}, {"id": "585050790", "full_name": "itchannel/tdarr_ha"}, {"id": "911358737", "new": true, "full_name": "bairnhard/home-assistant-google-aqi"}, {"id": "939531992", "new": true, "full_name": "TypQxQ/Sigenergy-Local-Modbus"}, {"id": "762896596", "new": true, "full_name": "kennethroe/vehiclevue"}, {"id": "442594482", "full_name": "pawelhulek/kontomierz-sensor"}, {"id": "401454435", "full_name": "hif2k1/battery_sim"}, {"id": "919185430", "new": true, "full_name": "Rafciq/BM6"}, {"id": "756368974", "full_name": "gritaro/gigachain"}, {"id": "729742358", "new": true, "full_name": "gentslava/elektronny-gorod"}, {"id": "971321216", "new": true, "full_name": "Seba101288/home-assistant-ever-ups"}, {"id": "922866826", "new": true, "full_name": "moehrem/DiveraControl"}, {"id": "669776150", "full_name": "djerik/beolink-ha"}, {"id": "284006518", "full_name": "bremor/bonaire_myclimate"}, {"id": "374314958", "full_name": "alexdelprete/ha-abb-powerone-pvi-sunspec"}, {"id": "807797415", "full_name": "dudanov/hassio-ftms"}, {"id": "570006201", "full_name": "sfstar/hass-victron"}, {"id": "362700564", "repository_manifest": {"name": "Tesla", "hacs": "1.6.0", "homeassistant": "2024.11.0", "zip_release": true, "filename": "tesla_custom.zip"}, "full_name": "alandtse/tesla", "authors": ["@alandtse"], "category": "integration", "description": "Tesla custom integration for Home Assistant. This requires a refresh token be generated by third-party apps to login.", "domain": "tesla_custom", "downloads": 13874, "etag_repository": "W/\"811e1f389bfbabfbef7eae20bdb066eddbdbb5827bec60d593d174e78f4f4f81\"", "last_updated": "2025-08-25T18:27:33Z", "stargazers_count": 679, "topics": ["home-assistant-component", "tesla"], "installed_commit": "bf7d587", "installed": true, "last_commit": "bf7d587", "last_version": "v3.25.2", "manifest_name": "Tesla Custom Integration", "releases": true, "version_installed": "v3.25.2", "last_fetched": 1756304402.302099}, {"id": "976702877", "new": true, "full_name": "derolli1976/enpal"}, {"id": "985868486", "new": true, "full_name": "sbenodiz/ai_agent_ha"}, {"id": "661432020", "full_name": "8none1/lednetwf_ble"}, {"id": "993557459", "new": true, "full_name": "alexbk66/hs_command_listener2"}, {"id": "872095848", "new": true, "full_name": "ardevd/ha-dimo"}, {"id": "842991319", "new": true, "full_name": "<PERSON><PERSON><PERSON>/home-assistant-gpio-integration"}, {"id": "388918745", "full_name": "CreasolTech/home-assistant-creasol-dombus"}, {"id": "909497384", "new": true, "full_name": "Elijaht-dev/ovh_ipv6"}, {"id": "193371652", "full_name": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>/Home-Assistant-custom-components-Looko2"}, {"id": "503856080", "full_name": "netsoft-ruidias/ha-custom-component-myedenred"}, {"id": "398781181", "full_name": "ofalvai/home-assistant-candy"}, {"id": "1017697433", "new": true, "full_name": "EVWorth/hass-adtpulse"}, {"id": "395991055", "full_name": "amaximus/anniversary"}, {"id": "1028403460", "new": true, "full_name": "tbclark3/ha-xweatherly"}, {"id": "263757123", "full_name": "finity69x2/nws_alerts"}, {"id": "793136366", "full_name": "kevin-briand/massa_node"}, {"id": "882776709", "new": true, "full_name": "MKsys1337/MiWiFi-CB0401V2"}, {"id": "483187645", "full_name": "geoffreylagaisse/Hass-Microsoft-Graph"}, {"id": "615730721", "full_name": "lizardsystems/hass-tnse"}, {"id": "819585848", "new": true, "full_name": "Makhuta/homeassistant-duolingo"}, {"id": "692375020", "full_name": "mampfes/ha_bayernluefter"}, {"id": "179347477", "full_name": "claytonjn/hass-circadian_lighting"}, {"id": "146792954", "new": true, "full_name": "custom-components/sensor.sonarr_upcoming_media"}, {"id": "152294445", "full_name": "custom-components/remote_homeassistant"}, {"id": "237102126", "full_name": "peetereczek/ztm"}, {"id": "693589195", "full_name": "RogerSelwyn/AICO_HomeLINK"}, {"id": "699910852", "full_name": "marq24/ha-tibber-pulse-local"}, {"id": "625947979", "full_name": "geer<PERSON><PERSON><PERSON><PERSON>/youfone"}, {"id": "163322610", "full_name": "djbulsink/panasonic_ac"}, {"id": "976880592", "new": true, "full_name": "tube0013/Smartcar-HA"}, {"id": "953019875", "new": true, "full_name": "EarthGoodness/egi"}, {"id": "796624167", "new": true, "full_name": "CompitHomeAssistant/HomeAssistant"}, {"id": "544550612", "full_name": "ZsBT/hass-w1000-portal"}, {"id": "411736321", "full_name": "mampfes/hacs_dwd_pollenflug"}, {"id": "961543293", "new": true, "full_name": "andybochmann/ha-virtual-battery"}, {"id": "1020489323", "new": true, "full_name": "tarikbc/ha-intelbras-alarm"}, {"id": "556789449", "full_name": "arifwn/homeassistant-whatspie-integration"}, {"id": "880314404", "new": true, "full_name": "toggm/askoheat"}, {"id": "314593331", "full_name": "dj<PERSON><PERSON><PERSON>/hasatelli<PERSON><PERSON>er"}, {"id": "542621509", "full_name": "user2684/imou_life"}, {"id": "190378093", "full_name": "And3rsL/VisonicAlarm-for-Has<PERSON>"}, {"id": "237628853", "full_name": "tuxuser/abfallapi_regioit_ha"}, {"id": "886921233", "new": true, "full_name": "Dams51/Pleinchamp"}, {"id": "1032393383", "new": true, "full_name": "r<PERSON>lian/lutron_lip"}, {"id": "169460975", "full_name": "akasma74/Hass-Custom-Alarm"}, {"id": "518498752", "full_name": "c1pher-cn/heweather"}, {"id": "439467929", "full_name": "nbogojevic/homeassistant-midea-air-appliances-lan"}, {"id": "261311061", "full_name": "garbled1/homeassistant_ecowitt"}, {"id": "242635439", "full_name": "kuchel77/diskspace"}, {"id": "729975302", "full_name": "ankohanse/hass-dab-pumps"}, {"id": "886554259", "new": true, "full_name": "zubir2k/homeassistant-esolatgps"}, {"id": "496755553", "full_name": "thecode/ha-onewire-sysbus"}, {"id": "434912125", "full_name": "wernerhp/ha.integration.load_shedding"}, {"id": "666758552", "full_name": "kamaradclimber/vigieau"}, {"id": "257104502", "full_name": "amaximus/fkf-garbage-collection"}, {"id": "856542767", "new": true, "full_name": "skodaconnect/homeassistant-myskoda"}, {"id": "365567023", "full_name": "sprocket-9/hacs-nuvo-serial"}, {"id": "965174896", "new": true, "full_name": "Nicxe/f1_sensor"}, {"id": "987284013", "new": true, "full_name": "svasek/homeassistant-vistapool-modbus"}, {"id": "900650545", "new": true, "full_name": "Prosono/SMARTi_BaseComponent"}, {"id": "342026799", "full_name": "amaximus/pollen_hu"}, {"id": "920583935", "new": true, "full_name": "Samywamy10/ha-cubecoders-amp"}, {"id": "914510941", "new": true, "full_name": "hass-uconnect/hass-uconnect"}, {"id": "380881340", "new": true, "full_name": "bm1549/home-assistant-frigidaire"}, {"id": "250022973", "repository_manifest": {"name": "SmartThinQ LGE Sensors", "zip_release": true, "filename": "smartthinq_sensors.zip", "homeassistant": "2025.1.0"}, "full_name": "ollo69/ha-smartthinq-sensors", "authors": ["@ollo69"], "category": "integration", "description": "HomeAssistant custom integration for SmartThinQ LG devices", "domain": "smartthinq_sensors", "downloads": 46280, "etag_repository": "W/\"9b867baefa07b3f902e6fdd0a89cb55e4b12421efc2bd16af61e9b984aadf9dd\"", "last_updated": "2025-01-05T20:22:11Z", "stargazers_count": 1249, "topics": ["ac", "air-purifier", "climate", "dehumidifier", "dishwasher", "dryer", "fan", "lg", "lge", "microwave", "oven", "range", "refrigerator", "sensors", "smartthinq", "thinq", "washer"], "installed_commit": "e8b8345", "installed": true, "last_commit": "e8b8345", "last_version": "v0.41.1", "manifest_name": "SmartThinQ LGE Sensors", "open_issues": 113, "releases": true, "version_installed": "v0.41.1", "last_fetched": 1756463490.33179}, {"id": "484708274", "full_name": "Vaskivskyi/ha-asusrouter"}, {"id": "923217853", "new": true, "full_name": "<PERSON><PERSON><PERSON><PERSON>/Home-Assistant-Electric-Ireland"}, {"id": "684619264", "new": true, "full_name": "FaserF/ha-chefkoch"}, {"id": "527179792", "full_name": "DarwinsBuddy/WienerNetzeSmartmeter"}, {"id": "499901994", "full_name": "LavermanJJ/home-assistant-solarfocus"}, {"id": "928193419", "new": true, "full_name": "taikun114/VOICEVOX-TTS-for-Home-Assistant"}, {"id": "512007926", "full_name": "xannor/ha_reolink_discovery"}, {"id": "961954558", "new": true, "full_name": "ndesgranges/simple-plant"}, {"id": "384910725", "full_name": "hwmland/homeassistant-xmrpool_stat"}, {"id": "995665345", "new": true, "full_name": "myTselection/smartschool_ha"}, {"id": "988991006", "new": true, "full_name": "0xQuantumHome/bayrol-home-hassistant"}, {"id": "304573324", "full_name": "crowbarz/ha-sql_json"}, {"id": "751031038", "new": true, "full_name": "samoswall/polaris-mqtt"}, {"id": "999409885", "new": true, "full_name": "Connectlife-LLC/HomeAssistantPlugin"}, {"id": "573101191", "full_name": "Alexwijn/SAT"}, {"id": "903881997", "new": true, "full_name": "cnecrea/smsto"}, {"id": "941682205", "new": true, "full_name": "jmdevita/parcel-ha"}, {"id": "131915802", "full_name": "custom-components/places"}, {"id": "581307720", "full_name": "CM000n/qss"}, {"id": "253842395", "full_name": "msp1974/homeassistant-jlrincontrol"}, {"id": "529926820", "full_name": "v1ack/lelight"}, {"id": "305147191", "full_name": "zigul/HomeAssistant-CEZdistribuce"}, {"id": "600872470", "full_name": "rrooggiieerr/homeassistant-okokscale"}, {"id": "134057086", "full_name": "syssi/xiaomi_cooker"}, {"id": "140907992", "full_name": "nicole-ashley/homeassistant-goldair-climate"}, {"id": "736875032", "full_name": "jmacri01/homeassistant-custom-components-catholic-calendar"}, {"id": "770263855", "full_name": "stickpin/homeassistant-meinvodafone"}, {"id": "228649088", "full_name": "cyberjunky/home-assistant-p2000"}, {"id": "300358676", "repository_manifest": {"name": "Tapo: Cameras Control", "homeassistant": "2024.4.0"}, "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>/HomeAssistant-Tapo-Control", "authors": ["@JurajNyiri"], "category": "integration", "description": "Control for Tapo cameras as a Home Assistant component", "domain": "tapo_control", "etag_repository": "W/\"389da720f684e86099d88355ba2775e130ee4ea8f3b73c64239b858c9cd22407\"", "last_updated": "2025-08-15T14:09:01Z", "stargazers_count": 1558, "topics": ["camera", "cameras", "homeassistant-custom-component", "ptz", "tapo"], "installed_commit": "e66f4a3", "installed": true, "last_commit": "9ba8476", "last_version": "7.0.0", "manifest_name": "Tapo: Cameras Control", "releases": true, "version_installed": "7.0.0", "last_fetched": 1756449431.159522}, {"id": "410867791", "full_name": "hasscc/hass-edge-tts"}, {"id": "843892078", "new": true, "full_name": "j9brown/victron-mk3-hass"}, {"id": "292390011", "full_name": "edwork/homeassistant-peloton-sensor"}, {"id": "847936670", "new": true, "full_name": "mash2k3/qingping_cgs1"}, {"id": "698694783", "full_name": "MarcoGos/davis_vantage"}, {"id": "339464185", "full_name": "erikkastelec/hass-WEM-Portal"}, {"id": "479221839", "full_name": "NickM-27/swatch-hass-integration"}, {"id": "587122619", "full_name": "dahlb/ha_carrier"}, {"id": "642982371", "full_name": "hudsonbrendon/ha_epic_games"}, {"id": "326288498", "full_name": "jugla/worldtidesinfocustom"}, {"id": "193371469", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/Home-Assistant-custom-components-Antistorm"}, {"id": "203592862", "full_name": "blindlight86/HA_USR-R16"}, {"id": "609302851", "full_name": "bkbilly/tpms_ble"}, {"id": "195883127", "full_name": "Martinvdm/garbage-nissewaard-homeassistant"}, {"id": "620277986", "full_name": "marcoboers/home-assistant-quatt"}, {"id": "932770300", "new": true, "full_name": "Phil7989/advanced_snapshot"}, {"id": "154845921", "full_name": "custom-components/sensor.ssh"}, {"id": "263179176", "full_name": "jeroenterheerdt/HAsmartirrigation"}, {"id": "234514524", "full_name": "AaronDavidSchneider/SonosAlarm"}, {"id": "299123388", "full_name": "jseidl/hass-magic_areas"}, {"id": "173564471", "full_name": "custom-components/sensor.file_restore"}, {"id": "422834940", "full_name": "N0ciple/hass-kef-connector"}, {"id": "302145522", "full_name": "djtimca/harocketlaunchlive"}, {"id": "445977563", "full_name": "denpamusic/homeassistant-plum-ecomax"}, {"id": "810273505", "full_name": "RogerSelwyn/MS365-Mail"}, {"id": "708304485", "full_name": "ciejer/metservice-weather"}, {"id": "319401286", "full_name": "peribeir/homeassistant-rademacher"}, {"id": "537806998", "full_name": "lociii/homeassistant-overwolf-status"}, {"id": "261849832", "full_name": "sockless-coding/garo_wallbox"}, {"id": "356725611", "full_name": "djerik/wavinsentio-ha"}, {"id": "961844215", "new": true, "full_name": "hsakoh/ha-switchbot-kvs-camera"}, {"id": "733708339", "full_name": "binarydev/ha-generac"}, {"id": "628038776", "full_name": "loopj/home-assistant-vantage"}, {"id": "998511539", "new": true, "full_name": "blaineventurine/simple_inventory"}, {"id": "207110572", "full_name": "custom-components/sensor.avfallsor"}, {"id": "766376848", "new": true, "full_name": "<PERSON>lain/hass_ingress"}, {"id": "891214473", "new": true, "full_name": "delize/home-assistant-loggamera-integration"}, {"id": "195438291", "full_name": "custom-components/sensor.nintendo_wishlist"}, {"id": "757106101", "full_name": "rgerbranda/rbfa"}, {"id": "307974458", "full_name": "TekniskSupport/home-assistant-resrobot"}, {"id": "714757792", "full_name": "gickowtf/pixoo-homeassistant"}, {"id": "660940844", "new": true, "full_name": "Superkikim/mh-maxsmart-hass"}, {"id": "524376939", "full_name": "PimDoos/onesmartcontrolha"}, {"id": "1010130459", "new": true, "full_name": "nemesa/ha-tcl-home-unofficial-integration"}, {"id": "672323037", "full_name": "Patrick762/hassio-<PERSON>tti-bt"}, {"id": "998435207", "new": true, "full_name": "jonandel/ha-hildebrand<PERSON>low-dcc"}, {"id": "770055209", "new": true, "full_name": "stanus74/home-assistant-saj-h2-modbus"}, {"id": "960107664", "new": true, "full_name": "dirkgroenen/hass-evse-load-balancer"}, {"id": "347143701", "full_name": "rccoleman/channels_dvr_recently_recorded"}, {"id": "501368149", "full_name": "J-Lindvig/Fuelprices_DK"}, {"id": "717185121", "full_name": "Mallonbacka/custom-component-cloudwatch"}, {"id": "357930725", "full_name": "elad-bar/ha-shinobi"}, {"id": "631670903", "full_name": "ufozone/ha-zcs-mower"}, {"id": "975714202", "new": true, "full_name": "SmartyVan/hass-geolocator"}, {"id": "534750752", "full_name": "rrooggiieerr/homeassistant-xyscreens"}, {"id": "973373722", "new": true, "full_name": "hopkins-tk/home-assistant-aseko-local"}, {"id": "433815675", "new": true, "full_name": "tggm/rointe-radiators"}, {"id": "692576253", "full_name": "klatka/nc-talk-bot-component"}, {"id": "406849413", "full_name": "mweinelt/ha-prometheus-sensor"}, {"id": "386049746", "full_name": "viragelabs/virage_dashboard"}, {"id": "403062943", "full_name": "jugla/battery_consumption"}, {"id": "191255859", "new": true, "full_name": "robert-alfaro/genius-lyrics"}, {"id": "613374790", "new": true, "full_name": "kgn3400/hiper_drift"}, {"id": "969116954", "new": true, "full_name": "PineappleEmperor/ocado-ha"}, {"id": "897032526", "new": true, "full_name": "ScreamingToaster/Bluesky-Integration"}, {"id": "643579135", "full_name": "jmcollin78/solar_optimizer"}, {"id": "260264517", "full_name": "r-renato/ha-climacell-weather"}, {"id": "552426092", "full_name": "kamaradclimber/heishamon-homeassistant"}, {"id": "579801670", "full_name": "bdunn44/hass-jellyfish-lighting"}, {"id": "450192057", "full_name": "ClusterM/skykettle-ha"}, {"id": "482473793", "full_name": "Aohzan/hass-prixcarburant"}, {"id": "751546869", "full_name": "DasBasti/SmartHashtag"}, {"id": "471000066", "full_name": "elden1337/hass-peaq"}, {"id": "340616586", "full_name": "Limych/ha-narodmon"}, {"id": "352169259", "full_name": "aex351/home-assistant-ne<PERSON><PERSON>-app"}, {"id": "272337216", "full_name": "turbulator/pandora-cas"}, {"id": "823668059", "new": true, "full_name": "Foxi352/pollen_lu"}, {"id": "303827752", "full_name": "sbabcock23/hass-tryfi"}, {"id": "676012641", "full_name": "wolffshots/hass-audiobookshelf"}, {"id": "153870340", "full_name": "nickneos/HA_harmony_climate_component"}, {"id": "922537065", "new": true, "full_name": "bieniu/ha-meteo-imgw-pib"}, {"id": "544105569", "full_name": "rrooggiieerr/homeassistant-benqprojector"}, {"id": "1034014801", "new": true, "full_name": "MiguelTVMS/e-redes-smart-metering-plus-hass"}, {"id": "548554162", "full_name": "dcmeglio/homeassistant-waste_management"}, {"id": "1014779052", "new": true, "full_name": "i<PERSON><PERSON><PERSON>/grocy"}, {"id": "223739645", "full_name": "atxbyea/samsungrac"}, {"id": "911976018", "new": true, "full_name": "jonnybergdahl/HomeAssistant_NewsbinPro_Integration"}, {"id": "954067134", "new": true, "full_name": "maybetaken/Solar_Manager"}, {"id": "874199659", "new": true, "full_name": "timniklas/hass-fitx"}, {"id": "379621461", "full_name": "tomasmcguinness/homeassistant-mixergy"}, {"id": "552532860", "full_name": "evantaur/seiverkot-consumption"}, {"id": "612978245", "repository_manifest": {"name": "<PERSON><PERSON><PERSON>", "render_readme": true, "homeassistant": "2024.8.0"}, "full_name": "libdyson-wg/ha-dyson", "authors": ["@libdyson-wg", "@dotvezz", "@cmgrayb"], "category": "integration", "description": "Home Assistant custom integration for Wi-Fi connected Dyson devices", "domain": "dyson_local", "etag_repository": "W/\"75d2af833ec4c6e85676f97e78b6288c477ba780c94d2cb4d450c5d2f9a8bfdb\"", "last_updated": "2025-08-15T20:21:55Z", "stargazers_count": 348, "topics": ["dyson", "dyson-devices", "dyson-fan", "dyson-link", "dysonlink"], "installed_commit": "41c3338", "installed": true, "last_commit": "41c3338", "last_version": "v1.7.0", "manifest_name": "<PERSON><PERSON><PERSON>", "releases": true, "version_installed": "v1.7.0", "last_fetched": 1756259166.838096}, {"id": "400832075", "full_name": "radical-squared/aquatemp"}, {"id": "326220257", "full_name": "marotoweb/home-assistant-vacuum-viomise"}, {"id": "528618549", "full_name": "chaimchaikin/molad-ha"}, {"id": "797655168", "full_name": "pipeless-ai/home-assistant-custom-component"}, {"id": "333849286", "full_name": "kpoppel/homeassistant-eforsyning"}, {"id": "378767428", "full_name": "hudsonbrendon/HA-drivvo"}, {"id": "229014136", "full_name": "doudz/homeassistant-myjdownloader"}, {"id": "552115509", "full_name": "thomasloven/hass-plejd"}, {"id": "346474804", "full_name": "DurgNomis-drol/ha_toyota"}, {"id": "647324399", "full_name": "Athozs/hass-additional-ca"}, {"id": "919907840", "new": true, "full_name": "Hogster/BPS"}, {"id": "953384647", "new": true, "full_name": "rbrunt/paprika-ha"}, {"id": "522245338", "full_name": "ndom91/homeassistant-checkly"}, {"id": "839422993", "new": true, "full_name": "andreadegiovine/homeassistant-stellantis-vehicles"}, {"id": "356053801", "full_name": "pcourbin/imaprotect"}, {"id": "827730904", "new": true, "full_name": "Roger<PERSON><PERSON>wyn/MS365-Teams"}, {"id": "351604227", "full_name": "<PERSON>ha-<PERSON><PERSON>/brand<PERSON>kute"}, {"id": "460392242", "full_name": "<PERSON><PERSON><PERSON>/swemail"}, {"id": "196605143", "full_name": "RobHofmann/HomeAssistant-PhilipsAndroid2014"}, {"id": "297106424", "full_name": "joggs/home_assistant_e<PERSON>co"}, {"id": "504664392", "full_name": "basilfx/homeassistant-biketrax"}, {"id": "233092629", "full_name": "kodi1/tvh_rec"}, {"id": "295627573", "full_name": "micha<PERSON><PERSON><PERSON>/Home-Assistant-Custom-Component-Fortnite"}, {"id": "316396217", "full_name": "evilmarty/mjpeg-timelapse"}, {"id": "236358405", "full_name": "nick2525/broadlink_s1c_s2c"}, {"id": "154417419", "full_name": "nstrelow/ha_philips_android_tv"}, {"id": "318927348", "full_name": "d03n3rfr1tz3/hass-divoom"}, {"id": "639503073", "full_name": "sebcaps/atmofrance"}, {"id": "135166048", "full_name": "robmarkcole/HASS-Machinebox-Classificationbox"}, {"id": "443905243", "full_name": "Ludy87/xplora_watch"}, {"id": "894143767", "new": true, "full_name": "snell-evan-itt/hassio-ecoflow-cloud-US"}, {"id": "556352757", "full_name": "mmillmor/geo_home"}, {"id": "230151505", "full_name": "laszlojakab/homeassistant-dijnet"}, {"id": "328566789", "full_name": "giachello/beoplay"}, {"id": "480127478", "full_name": "Ludy87/ecotrend-ista"}, {"id": "909837612", "new": true, "full_name": "Chuffnugget/volcano_integration"}, {"id": "290436986", "full_name": "fineemb/Colorfulclouds-weather"}, {"id": "530452578", "full_name": "modrzew/hass-flashforge-adventurer-3"}, {"id": "504935480", "full_name": "mill1000/midea-ac-py"}, {"id": "797995385", "full_name": "zerolo/aguasgaiaHA"}, {"id": "228627470", "full_name": "cyberjunky/home-assistant-hvc<PERSON>ep"}, {"id": "767463130", "full_name": "artspb/homeassistant-tk-husteblume"}, {"id": "266557774", "full_name": "deblockt/hass-proscenic-790T-vacuum"}, {"id": "155499113", "new": true, "full_name": "custom-components/sensor.plex_recently_added"}, {"id": "635072820", "full_name": "pdw-mb/tsmart_ha"}, {"id": "379688863", "full_name": "TheRealWaldo/thermal"}, {"id": "302985427", "full_name": "wizmo2/zidoo-player"}, {"id": "572284948", "full_name": "yo-han/Home-Assistant-<PERSON><PERSON>"}, {"id": "909186509", "new": true, "full_name": "jorourke/naim-atom-home-assistant"}, {"id": "391700886", "full_name": "deblockt/hass-aria2"}, {"id": "687441634", "full_name": "robinostlund/homeassistant-svk-mimer"}, {"id": "373845609", "full_name": "bramstroker/homeassistant-powercalc"}, {"id": "822801469", "new": true, "full_name": "Monitor-My-Solar/monitormysolar"}, {"id": "202987887", "full_name": "zachowj/hass-node-red"}, {"id": "190260955", "full_name": "bramkragten/mind"}, {"id": "896108135", "new": true, "full_name": "markuzzi/somfy_cul_integration"}, {"id": "282714722", "full_name": "mchwalisz/home-assistant-senec"}, {"id": "459761427", "full_name": "pawelhulek/pgnig-sensor"}, {"id": "213346369", "full_name": "scottyphillips/echonetlite_homeassistant"}, {"id": "545119372", "full_name": "Fr3d/camect-ha"}, {"id": "366911690", "full_name": "rroller/dahua"}, {"id": "368653916", "full_name": "moox-it/hass-moox-track"}, {"id": "552516426", "new": true, "full_name": "i<PERSON>us/hass-swissweather"}, {"id": "709665442", "full_name": "vingerha/gtfs2"}, {"id": "442225646", "full_name": "morosanmihail/HA-LondonTfL"}, {"id": "557087679", "new": true, "full_name": "lloydw/hass-spanet"}, {"id": "705784959", "full_name": "krasnoukhov/homeassistant-smart-maic"}, {"id": "151550084", "full_name": "custom-components/sensor.owlintuition"}, {"id": "875140397", "new": true, "full_name": "ivancoppa/homeassistant-perry-cdom"}, {"id": "622332920", "full_name": "j<PERSON>ler/hass-ttlock"}, {"id": "844729853", "new": true, "full_name": "Angelius007/myfox-api"}, {"id": "207794683", "full_name": "gregoryduckworth/GoogleGeocode-HASS"}, {"id": "901002806", "new": true, "full_name": "Jezza34000/homeassistant_petkit"}, {"id": "443221313", "new": true, "full_name": "prestomation/resmed_myair_sensors"}, {"id": "900154271", "new": true, "full_name": "Appartme/Appartme-System-HACI"}, {"id": "968638048", "new": true, "full_name": "solarssk/ztm_warsaw"}, {"id": "636324558", "full_name": "ryanbdclark/owlet"}, {"id": "814985321", "new": true, "full_name": "Poshy163/HomeAssistant-Sharesight"}, {"id": "447878635", "full_name": "IATkachenko/HA-YandexWeather"}, {"id": "936596170", "new": true, "full_name": "Master13011/vacances-scolaire-HA"}, {"id": "548811638", "full_name": "amosyuen/ha-epson-projector-link"}, {"id": "937118951", "new": true, "full_name": "lnagel/hass-eaton-ups-mqtt"}, {"id": "177978011", "full_name": "StyraHem/ShellyForHASS"}, {"id": "394704821", "full_name": "wills106/homeassistant-solax-modbus"}, {"id": "294037465", "full_name": "swingerman/ha-dual-smart-thermostat"}, {"id": "234983286", "full_name": "Home-Is-Where-You-Hang-Your-Hack/sensor.goveetemp_bt_hci"}, {"id": "685246640", "full_name": "Danieldiazi/homeassistant-meteogalicia_tides"}, {"id": "472077314", "full_name": "pawkakol1/worlds-air-quality-index"}, {"id": "364208180", "full_name": "koying/jellyfin_ha"}, {"id": "328361159", "full_name": "j<PERSON><PERSON><PERSON>er/home-assistant-ultimaker"}, {"id": "246657442", "full_name": "lolouk44/CurrentCost_HA_CC"}, {"id": "383732864", "full_name": "cyberjunky/home-assistant-garmin_connect"}, {"id": "209955487", "full_name": "slesinger/HomeAssistant-BMR"}, {"id": "442181774", "full_name": "ta<PERSON><PERSON><PERSON><PERSON>/daikin_altherma"}, {"id": "*********", "new": true, "full_name": "NemoN/ha-intex-swg"}, {"id": "*********", "new": true, "full_name": "tehlers/ha-drooff-fireplus"}, {"id": "*********", "new": true, "full_name": "<PERSON>-<PERSON><PERSON>-badil/home-assistant_he<PERSON><PERSON>-v<PERSON><PERSON>"}, {"id": "*********", "new": true, "full_name": "rsampayo/sleepme_thermostat"}, {"id": "*********", "full_name": "evercape/hass-resol-KM2"}, {"id": "*********", "full_name": "Kartax/home-assistant-binance"}, {"id": "85400693", "full_name": "<PERSON><PERSON><PERSON><PERSON>/home-assistant-pad<PERSON>-tracker"}, {"id": "1021297330", "new": true, "full_name": "ant0nkr/luxpower-ha-integration"}, {"id": "*********", "new": true, "full_name": "hudsonbrendon/HA-solar-plus-intelbras"}, {"id": "*********", "full_name": "custom-components/brewdog"}, {"id": "*********", "new": true, "full_name": "bywciu/Home-Assistant-Custom-Components-ReversoTTS"}, {"id": "*********", "full_name": "earendil06/Windy-Webcams"}, {"id": "*********", "full_name": "dcmeglio/homeassistant-petsafe"}, {"id": "*********", "full_name": "msvisser/remeha_home"}, {"id": "*********", "full_name": "itchannel/optus-ha"}, {"id": "*********", "new": true, "full_name": "Haluska77/regulus-ha"}, {"id": "*********", "full_name": "dolezsa/thermal_comfort"}, {"id": "*********", "full_name": "tykeal/homeassistant-rental-control"}, {"id": "*********", "new": true, "full_name": "kgn3400/remote_activity_monitor"}, {"id": "582268944", "full_name": "mampfes/ha_epex_spot"}, {"id": "1001666416", "new": true, "full_name": "droans/mass_queue"}, {"id": "566598076", "full_name": "dscao/ikuai"}, {"id": "710608974", "full_name": "codyc1515/ha-yeelock"}, {"id": "292720530", "full_name": "alryaz/hass-pandora-cas"}, {"id": "775995022", "full_name": "randomletters/motion_dimmer"}, {"id": "789588169", "full_name": "popeen/Home-Assistant-Custom-Component-Brandrisk-Eldningsforbud"}, {"id": "771099628", "full_name": "verdel/hass-petoneer-smartdot"}, {"id": "745825556", "new": true, "full_name": "rihokirss/homeasisstant-rn301"}, {"id": "996854813", "new": true, "full_name": "tomer-w/ha-victron-mqtt"}, {"id": "598336481", "full_name": "GuyKh/ims-custom-component"}, {"id": "819932236", "new": true, "full_name": "<PERSON><PERSON><PERSON><PERSON>-ST/response_as_sensor"}, {"id": "983587131", "new": true, "full_name": "izipuho/OpenWRT_control"}, {"id": "170309600", "full_name": "Juraj<PERSON><PERSON><PERSON>/HomeAssistant-Atrea"}, {"id": "442878365", "full_name": "hostcc/hass-gs-alarm"}, {"id": "547177218", "full_name": "mb-software/homeassistant-powerbrain"}, {"id": "97201395", "full_name": "syssi/xiaomiplug"}, {"id": "934720202", "new": true, "full_name": "Refoss/refoss_rpc"}, {"id": "199306511", "full_name": "gieljnssns/kostalpiko-sensor-homeassistant"}, {"id": "987004256", "new": true, "full_name": "andrew-codechimp/HA-Label-State"}, {"id": "958059839", "new": true, "full_name": "alaltitov/homeassistant-frontend-translations"}, {"id": "607766615", "full_name": "emics/ham_radio_propagation"}, {"id": "279184610", "full_name": "madpilot/hass-amber-electric"}, {"id": "906758470", "new": true, "full_name": "bjorncs/ha-brewcreator"}, {"id": "990956362", "new": true, "full_name": "lsellens/thesimple-thermostat"}, {"id": "845069954", "new": true, "full_name": "raelix/HA-<PERSON><PERSON><PERSON>-integration"}, {"id": "928012218", "new": true, "full_name": "Zendure/Zendure-HA"}, {"id": "164489685", "full_name": "syssi/nextbike"}, {"id": "881873985", "new": true, "full_name": "QNimbus/haefele-connect-mesh"}, {"id": "544426802", "full_name": "Tasshack/dreame-vacuum"}, {"id": "239339530", "full_name": "Johnwulp/rad-afval"}, {"id": "307678069", "full_name": "enkama/hass-variables"}, {"id": "338782385", "full_name": "frimtec/hass-compal-wifi"}, {"id": "910687981", "new": true, "full_name": "cnecrea/cursbnr"}, {"id": "292616002", "full_name": "swartjean/ha-seedboxes-cc"}, {"id": "835428735", "full_name": "ad-ha/mg-saic-ha"}, {"id": "686778752", "full_name": "pantherale0/ha-familysafety"}, {"id": "1012361578", "new": true, "full_name": "tom42530/asys_ble_ha"}, {"id": "346329169", "full_name": "d<PERSON><PERSON><PERSON>/hass-miwifi"}, {"id": "291317330", "full_name": "mletenay/home-assistant-ev-charge-control"}, {"id": "864903077", "new": true, "full_name": "g4bri3lDev/munich_public_transport"}, {"id": "712412324", "full_name": "osohotwateriot/osoenergy_community"}, {"id": "581264508", "full_name": "dominikamann/oekofen-pellematic-compact"}, {"id": "749416586", "new": true, "full_name": "frlequ/homeassistant-mojelektro"}, {"id": "145180996", "full_name": "custom-components/feedparser"}, {"id": "842024649", "new": true, "full_name": "jampez77/Jet2"}, {"id": "719333597", "new": true, "full_name": "borski/ha-lucidmotors"}, {"id": "614438299", "full_name": "Taraman17/hass-homee"}, {"id": "820944003", "new": true, "full_name": "duhow/hass-cover-time-based"}, {"id": "627734223", "full_name": "sanghvi<PERSON><PERSON>t/ha-monarchmoney"}, {"id": "601113362", "full_name": "t0mer/manish-custom-notifier"}, {"id": "506738088", "full_name": "netsoft-ruidias/ha-custom-component-sodexo"}, {"id": "937169502", "new": true, "full_name": "UnoSite/IsItPayday"}, {"id": "735063605", "full_name": "elsbrock/cowboy-ha"}, {"id": "974470958", "new": true, "full_name": "mike81gr/deddie-metering"}, {"id": "707279332", "full_name": "krasnoukhov/homeassistant-nova-poshta"}, {"id": "286186485", "full_name": "nielsfaber/scheduler-component"}, {"id": "925757263", "new": true, "full_name": "dinki/view_assist_integration"}, {"id": "323346718", "full_name": "fsaris/home-assistant-awox"}, {"id": "867807428", "new": true, "full_name": "Nicxe/home-assistant-s<PERSON><PERSON><PERSON><PERSON>"}, {"id": "233089370", "full_name": "kodi1/esp_wd"}, {"id": "968050316", "new": true, "full_name": "phoinixgrr/sigma_connect_ha"}, {"id": "906704104", "new": true, "full_name": "Jarauvi/beny_wifi"}, {"id": "858170506", "new": true, "full_name": "jampez77/Evri"}, {"id": "905648820", "new": true, "full_name": "AlbrechtL/hass-padersprinter"}, {"id": "403123516", "full_name": "barleybobs/homeassistant-ecowater-softener"}, {"id": "835654666", "new": true, "full_name": "Dekadinious/trsdm_custom_device_tracker_for_home_assistant"}, {"id": "989691864", "new": true, "full_name": "bvweerd/dynamic_energy_contract_calculator"}, {"id": "995567426", "new": true, "full_name": "dhover/ha-iungo"}, {"id": "316807165", "full_name": "HomeAssistant-Mods/home-assistant-miele"}, {"id": "941493688", "new": true, "full_name": "DeKaN/ha-boneco"}, {"id": "396695907", "full_name": "mullerdavid/hass_GreeExt"}, {"id": "255258767", "full_name": "Aohzan/ipx800"}, {"id": "979983706", "new": true, "full_name": "bvweerd/simple_pid_controller"}, {"id": "744765851", "full_name": "Weissnix4711/hass-listenbrainz"}, {"id": "383608593", "full_name": "h4de5/home-assistant-to<PERSON><PERSON>_ac"}, {"id": "259739166", "full_name": "markgdev/home-assistant_Octopus<PERSON>gile"}, {"id": "873826922", "new": true, "full_name": "timniklas/hass-blitzerde"}, {"id": "395770920", "full_name": "firstof9/ha-openei"}, {"id": "144888844", "full_name": "Paul-dH/Home-Assisant-Sensor-OvApi"}, {"id": "666305946", "new": true, "full_name": "MarcoGos/kleenex_pollenradar"}, {"id": "117426840", "full_name": "robinostlund/homeassistant-volkswagencarnet"}, {"id": "870208091", "new": true, "full_name": "Darkdragon14/ha-guest-mode"}, {"id": "993966577", "new": true, "full_name": "TJPoorman/home_maintenance"}, {"id": "486729209", "full_name": "p0l0/hapetwalk"}, {"id": "586474647", "full_name": "myTselection/pixometer"}, {"id": "508800396", "full_name": "netsoft-ruidias/ha-custom-component-coverflex"}, {"id": "187201747", "full_name": "jihao/rokid-webhook-hass"}, {"id": "573737078", "full_name": "faanskit/ha-esolar"}, {"id": "382905556", "full_name": "kotborealis/home-assistant-custom-components-cover-time-based-synced"}, {"id": "239366330", "full_name": "mikelawrence/senseme-hacs"}, {"id": "808503273", "full_name": "stoppegp/ha-dwd-precipitation-forecast"}, {"id": "695053516", "new": true, "full_name": "flame4ever/eeve_mower_willow"}, {"id": "540279701", "full_name": "tcarwash/home-assistant_noaa-space-weather"}, {"id": "961973635", "new": true, "full_name": "Looking4Cache/home-assistant-remote-assistant"}, {"id": "328671547", "full_name": "dmoranf/home-assistant-wattio"}, {"id": "991141911", "new": true, "full_name": "heindrichpaul/ennatuurlijk_disruptions"}, {"id": "220661494", "full_name": "cyr-ius/hass-livebox-component"}, {"id": "597539627", "full_name": "bodyscape/cielo_home"}, {"id": "324913968", "full_name": "hg1337/homeassistant-dwd"}, {"id": "662947374", "full_name": "nickknissen/hass-monta"}, {"id": "417400028", "full_name": "ScratMan/HASmartThermostat"}, {"id": "935801155", "new": true, "full_name": "nfeuerhelm/ha-proj-viewsonic"}, {"id": "856448738", "new": true, "full_name": "sir<PERSON>rby/unifi-network-rules"}, {"id": "340419467", "new": true, "full_name": "astrandb/teracom_hass"}, {"id": "468666097", "full_name": "CoMPaTech/stromer"}, {"id": "511504216", "full_name": "Skarbo/hass-scinan-thermostat"}, {"id": "225248441", "full_name": "tomaae/homeassistant-mikrotik_router"}, {"id": "909339223", "new": true, "full_name": "Flo-Schilli/ha-grohe_smarthome"}, {"id": "258012818", "full_name": "vigonotion/hass-simpleicons"}, {"id": "925869089", "new": true, "full_name": "sslivins/hass_omnisense"}, {"id": "960213673", "new": true, "full_name": "kcoffau/AUS_BOM_Space_Weather_Alert_System"}, {"id": "*********", "full_name": "fsaris/home-assistant-zon<PERSON><PERSON><PERSON>-one"}, {"id": "*********", "full_name": "<PERSON><PERSON><PERSON><PERSON>/ha-star<PERSON><PERSON><PERSON><PERSON>"}, {"id": "*********", "full_name": "DeerMaximum/Technische-Alternative-CoE"}, {"id": "*********", "full_name": "t0mer/matterbridge-custom-notifier"}, {"id": "*********", "new": true, "full_name": "FaserF/ha-kadermanager"}, {"id": "*********", "full_name": "edekeijzer/osrm_travel_time"}, {"id": "*********", "new": true, "full_name": "christia<PERSON><PERSON>sens/hass-oidc-auth"}, {"id": "*********", "new": true, "full_name": "tykovec/home-assistant-tritius"}, {"id": "*********", "full_name": "signalkraft/mypyllant-component"}, {"id": "*********", "full_name": "dingo35/ha-SmartEVSEv3"}, {"id": "*********", "full_name": "joemcc-90/leeds-bins-hass"}, {"id": "*********", "full_name": "Limych/ha-tor_check"}, {"id": "*********", "full_name": "BobTheShoplifter/HomeAssistant-Posten"}, {"id": "*********", "full_name": "Vaskivskyi/ha-chroma"}, {"id": "*********", "full_name": "tikismoke/home-assistant-plc<PERSON>"}, {"id": "*********", "full_name": "Anonym-tsk/homeassistant-climate-xiaomi-remote"}, {"id": "*********", "full_name": "dave-code-ruiz/uhomeuponor"}, {"id": "*********", "full_name": "claudegel/sinope-gt125"}, {"id": "*********", "full_name": "heyajohnny/afvalinfo"}, {"id": "*********", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/Home-Assistant-custom-components-Tauron-AMIplus"}, {"id": "731302513", "full_name": "tbouron/ha-agur"}, {"id": "951862232", "new": true, "full_name": "danq8/neo_watcher"}, {"id": "76125161", "full_name": "tybritten/ical-sensor-homeassistant"}, {"id": "745783554", "full_name": "sanjoyg/dirigera_platform"}, {"id": "900746471", "new": true, "full_name": "ClusterM/localtuya_rc"}, {"id": "730400084", "full_name": "SplinterHead/ha-honeygain"}, {"id": "806061552", "full_name": "rany2/ha-open-meteo-solar-forecast"}, {"id": "872828936", "new": true, "full_name": "MTrab/stromligning"}, {"id": "874335020", "new": true, "full_name": "gorfo66/flashbird-homeassistant"}, {"id": "567001290", "full_name": "knudsvik/EnergyScore"}, {"id": "323155307", "full_name": "aceindy/Duepi_EVO"}, {"id": "658597458", "full_name": "vakio-ru/vakio_base_smart"}, {"id": "1028266207", "new": true, "full_name": "FUjr/homeassistant-openwrt-ubus"}, {"id": "828641829", "new": true, "full_name": "kgn3400/calendar_merge"}, {"id": "464416924", "full_name": "corporategoth/ha-powerpetdoor"}, {"id": "276915021", "full_name": "nordicopen/easee_hass"}, {"id": "934761293", "new": true, "full_name": "akinin/ha_keenetic"}, {"id": "967654897", "new": true, "full_name": "zulufoxtrot/ha-zyxel"}, {"id": "246410785", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>r/ics"}, {"id": "504081359", "full_name": "markvader/sonic"}, {"id": "120696364", "full_name": "vinteo/hass-opensprinkler"}, {"id": "258012483", "full_name": "AkA57/liveboxtvuhd"}, {"id": "332911333", "full_name": "rgc99/irrigation_unlimited"}, {"id": "319346850", "full_name": "Limych/ha-snowtire"}, {"id": "337387822", "full_name": "muhlba91/onyx-homeassistant-integration"}, {"id": "220482107", "full_name": "elad-bar/ha-hpprinter"}, {"id": "228678807", "full_name": "cyberjunky/home-assistant-toon_smartmeter"}, {"id": "199291345", "full_name": "JurajNyiri/HomeAssistant-qBitTorrentAlternativeSpeed"}, {"id": "334284822", "full_name": "ohheyrj/home-assistant-aws-codepipeline"}, {"id": "266779715", "full_name": "iprak/yahoofinance"}, {"id": "718762596", "full_name": "krasnoukhov/homeassistant-tesy"}, {"id": "730719420", "new": true, "full_name": "redstone99/hass-alert2"}, {"id": "908859014", "new": true, "full_name": "Beat-YT/hydropeak-ha"}, {"id": "1011438580", "new": true, "full_name": "ddrimus/ha-tper-tracker"}, {"id": "657938356", "new": true, "full_name": "zhbjsh/homeassistant-ssh"}, {"id": "568434741", "full_name": "mbuchber/ha_heliotherm"}, {"id": "795624377", "full_name": "vermut/ha_amc_alarm"}, {"id": "331597657", "repository_manifest": {"name": "Aqara Gateway", "render_readme": true}, "full_name": "niceboygithub/AqaraGateway", "authors": ["@niceboygithub"], "category": "integration", "description": "Aqara Gateway/Hub integration for Home Assistant", "domain": "aqara_gateway", "etag_repository": "W/\"967d24401a715fb73279fbd87fa2512b9d4c91d3d2c856ce81991cf02c881ecf\"", "last_updated": "2025-08-14T01:58:07Z", "stargazers_count": 603, "topics": ["aqara", "xia<PERSON>", "zigbee", "zigbee-gateway"], "installed_commit": "a9867f2", "installed": true, "last_commit": "cc64708", "last_version": "0.3.2", "manifest_name": "Aqara Gateway", "releases": true, "version_installed": "0.3.2", "last_fetched": 1756458522.56395}, {"id": "249381778", "repository_manifest": {"name": "Local Tuya", "homeassistant": "2024.1.0"}, "full_name": "rospogrigio/localtuya", "authors": ["@rospogrigio", "@postlund"], "category": "integration", "description": "local handling for Tuya devices", "domain": "localtuya", "etag_repository": "W/\"704008c660fad0535b6fbe128939d8750f5f7ce8094a10a4b1c6a1a7c9786f93\"", "last_updated": "2025-05-08T07:54:50Z", "stargazers_count": 3432, "topics": ["localtuya", "tuya", "tuya-api"], "installed_commit": "9eabc43", "installed": true, "last_commit": "9eabc43", "last_version": "v5.2.4", "manifest_name": "LocalTuya integration", "releases": true, "version_installed": "v5.2.4", "last_fetched": 1756456743.61313}, {"id": "860336465", "new": true, "full_name": "jampez77/Yodel"}, {"id": "650550675", "full_name": "vakio-ru/vakio_openair"}, {"id": "726217692", "full_name": "legrego/homeassistant-combustion"}, {"id": "228662926", "full_name": "cyberjunky/home-assistant-toon_climate"}, {"id": "645730299", "full_name": "CumpsD/home-assistant-leo-ntp"}, {"id": "945606645", "new": true, "full_name": "luis-garza/movistar_rft8115vw"}, {"id": "973755153", "new": true, "full_name": "TilmanGriesel/ha_trmnl_weather_station"}, {"id": "869078976", "new": true, "full_name": "Bebbssos/ha-defa-power"}, {"id": "293843053", "full_name": "<PERSON><PERSON><PERSON><PERSON>/home_assistant_adax"}, {"id": "432434646", "full_name": "guerrerotook/securitas-direct-new-api"}, {"id": "572282256", "full_name": "wez/govee-lan-hass"}, {"id": "190927503", "full_name": "plmilord/Hass.io-custom-component-spaclient"}, {"id": "706211952", "full_name": "basbruss/adaptive-cover"}, {"id": "588912665", "full_name": "vooon/hass-myheat"}, {"id": "594007512", "full_name": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/moonraker-home-assistant"}, {"id": "999139114", "new": true, "full_name": "CharlesP44/Beem_Energy"}, {"id": "497924778", "full_name": "dave-code-ruiz/elkbledom"}, {"id": "875313731", "new": true, "full_name": "prince<PERSON><PERSON>/home-assistant-rako"}, {"id": "942591577", "new": true, "full_name": "danishru/silam_pollen"}, {"id": "708363868", "full_name": "oven-lab/tuya_cloud_map_extractor"}, {"id": "891711154", "new": true, "full_name": "alex-jung/ha-departures"}, {"id": "1021046778", "new": true, "full_name": "C2gl/tududi_integration"}, {"id": "891979542", "new": true, "full_name": "evilmarty/switch_fan"}, {"id": "1037183903", "new": true, "full_name": "bemfa/behome"}, {"id": "720223133", "new": true, "full_name": "pypolestar/polestar_api"}, {"id": "504902462", "full_name": "ReneNulschDE/ha-mysmartbike"}, {"id": "228690854", "full_name": "cyberjunky/home-assistant-ttn_gateway"}, {"id": "625923963", "full_name": "taarskog/home-assistant-component-somweb"}, {"id": "743708645", "full_name": "mukaschultze/ha-must-inverter"}, {"id": "445609628", "full_name": "soloam/ha-pid-controller"}, {"id": "1002241350", "new": true, "full_name": "cabberley/utility_meter_evolved"}, {"id": "478078274", "full_name": "se<PERSON><PERSON><PERSON><PERSON>/sst_cloud"}, {"id": "688119784", "full_name": "gjocys/ha-recom-modbus"}, {"id": "946251566", "new": true, "full_name": "peteS-UK/lyrion_cli"}, {"id": "238568340", "full_name": "Pouzor/freebox_player"}, {"id": "536765576", "full_name": "kamaradclimber/rte-ecowatt"}, {"id": "768255166", "full_name": "joosthoi1/hockey-team-tracker"}, {"id": "818842740", "new": true, "full_name": "yangqian/hass-hk_air_quality"}, {"id": "904887498", "new": true, "full_name": "srkoster/hass-hw-cleaner"}, {"id": "259867685", "full_name": "hasl-sensor/integration"}, {"id": "1008581795", "new": true, "full_name": "Korkuttum/blynk"}, {"id": "668344544", "full_name": "sh00t2kill/linktap_local_http_component"}, {"id": "761387674", "full_name": "PTST/LibreView-HomeAssistant"}, {"id": "726918434", "new": true, "full_name": "dkarv/ha-bwt-perla"}, {"id": "252926906", "full_name": "asev/homeassistant-uponor"}, {"id": "197920457", "full_name": "andersonshatch/midea-ac-py"}, {"id": "658598049", "full_name": "vakio-ru/vakio_kiv"}, {"id": "495607253", "full_name": "jippi/hass-nordnet"}, {"id": "698674167", "full_name": "Sese-Schneider/ha-cover-time-based"}, {"id": "472497355", "full_name": "MTrab/energidataservice"}, {"id": "524883312", "full_name": "nkvoll/home-assistant-qsys-qrc"}, {"id": "456292486", "full_name": "jnxxx/homeassistant-dabblerdk_powermeterreader"}, {"id": "357338258", "full_name": "Limych/ha-apparent-temperature"}, {"id": "597502676", "full_name": "maciej-or/hikvision_next"}, {"id": "438036391", "full_name": "fustom/ariston-remotethermo-home-assistant-v3"}, {"id": "797431228", "new": true, "full_name": "Hoffmann77/ha-dwd-precipitation"}, {"id": "282427417", "full_name": "bremor/public_transport_victoria"}, {"id": "455174197", "full_name": "dummy<PERSON><PERSON>/thewatchman"}, {"id": "525954717", "full_name": "home-assistant-HomeWhiz/home-assistant-HomeWhiz"}, {"id": "377060365", "full_name": "alryaz/hass-lkcomu-interrao"}, {"id": "224258177", "full_name": "cyr-ius/hass-heatzy"}, {"id": "714191907", "full_name": "alengwenus/ha-sma-ev-charger"}, {"id": "906130485", "new": true, "full_name": "jazzz/ha-evocarshare"}, {"id": "870320575", "new": true, "full_name": "AboveColin/HA-Philips-Pet-Series"}, {"id": "278596510", "full_name": "jesserockz/ha-leafspy"}], "plugin": [{"id": "308752409", "full_name": "tmjo/charger-card"}, {"id": "194824532", "full_name": "Imbuzi/meteo-france-weather-card"}, {"id": "833206795", "full_name": "WJDDesigns/Ultra-Vehicle-Card"}, {"id": "492996183", "full_name": "Gh61/lovelace-hue-like-light-card"}, {"id": "923648410", "new": true, "full_name": "drkpxl/printwatch-card"}, {"id": "761188804", "full_name": "dvb6666/homed-zigbee-networkmap"}, {"id": "618081815", "full_name": "flixlix/power-flow-card-plus"}, {"id": "216008446", "full_name": "royto/logbook-card"}, {"id": "261262884", "full_name": "GeorgeSG/lovelace-time-picker-card"}, {"id": "1032497636", "new": true, "full_name": "jm-cook/lovelace-meteogram-card"}, {"id": "596085141", "full_name": "lozzd/octopus-energy-rates-card"}, {"id": "143850865", "full_name": "custom-cards/beer-card"}, {"id": "583449944", "full_name": "silentbil/silent-remotes-card"}, {"id": "934866047", "new": true, "full_name": "xBourner/header-position-card"}, {"id": "768880572", "full_name": "gaco79/gcclock-words"}, {"id": "343112953", "full_name": "jtbgroup/kodi-search-card"}, {"id": "856270242", "new": true, "full_name": "Haluska77/solar-gauge-card"}, {"id": "497829589", "full_name": "fratsloos/fr24_card"}, {"id": "810563350", "new": true, "full_name": "ngocjohn/vehicle-info-card"}, {"id": "268163975", "full_name": "artem-sedykh/mini-climate-card"}, {"id": "164367214", "full_name": "iantrich/roku-card"}, {"id": "449416816", "full_name": "punxaphil/custom-sonos-card"}, {"id": "594389396", "full_name": "Sese-Schneider/ha-energy-overview-card"}, {"id": "178921037", "full_name": "benct/lovelace-multiple-entity-row"}, {"id": "151280062", "full_name": "kalkih/mini-graph-card"}, {"id": "245159052", "full_name": "jcwill<PERSON>/lovelace-canary"}, {"id": "291480917", "full_name": "fineemb/lovelace-colorfulclouds-weather-card"}, {"id": "260597137", "full_name": "fineemb/lovelace-air-filter-card"}, {"id": "160042309", "full_name": "gurbyz/power-wheel-card"}, {"id": "253019926", "full_name": "artem-sedykh/mini-humidifier"}, {"id": "983081406", "new": true, "full_name": "GyroGearl00se/lovelace-froeling-card"}, {"id": "394082552", "full_name": "dermotduffy/advanced-camera-card"}, {"id": "1021999223", "new": true, "full_name": "cataseven/Summary-Card"}, {"id": "165156754", "full_name": "custom-cards/pc-card"}, {"id": "1022307554", "new": true, "full_name": "Rishi8078/TimeFlow-Card"}, {"id": "218178802", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/Home-Assistant-<PERSON><PERSON>-Local-Conditional-card"}, {"id": "908868522", "new": true, "full_name": "francois-le-ko4la/lovelace-entity-progress-card"}, {"id": "358962656", "full_name": "bern<PERSON>r/lovelace-notify-card"}, {"id": "938395950", "new": true, "full_name": "MagicM<PERSON>y/lovelace-calendar-heatmap-card"}, {"id": "187245461", "full_name": "custom-cards/entity-attributes-card"}, {"id": "345753205", "full_name": "finity69x2/fan-percent-button-row"}, {"id": "194037195", "full_name": "Ceerbeerus/beerbolaget-card"}, {"id": "1013376180", "new": true, "full_name": "cataseven/Switch-and-Timer-Bar-Card"}, {"id": "839495072", "new": true, "full_name": "brunosabot/streamline-card"}, {"id": "156292058", "full_name": "custom-cards/flex-table-card"}, {"id": "257102434", "full_name": "amaximus/fkf-garbage-collection-card"}, {"id": "934697455", "new": true, "full_name": "<PERSON><PERSON><PERSON><PERSON>in/ha-today-card"}, {"id": "373857882", "full_name": "wassy92x/lovelace-entities-btn-group"}, {"id": "491465538", "full_name": "Mariusthvdb/custom-icons"}, {"id": "788499425", "full_name": "itsbrianburton/slide-confirm"}, {"id": "215327195", "full_name": "bokub/rgb-light-card"}, {"id": "912108415", "new": true, "full_name": "harmonie-durrant/hha-cards"}, {"id": "1041500845", "new": true, "full_name": "timmaurice/lovelace-radar-card"}, {"id": "384434522", "full_name": "arallsopp/hass-hue-icons"}, {"id": "271886611", "full_name": "badguy99/PlantPictureCard"}, {"id": "196132939", "full_name": "custom-cards/nintendo-wishlist-card"}, {"id": "184658908", "full_name": "benct/lovelace-github-entity-row"}, {"id": "342944383", "full_name": "aex351/home-assistant-<PERSON><PERSON><PERSON>-card"}, {"id": "968219606", "new": true, "full_name": "ndesgranges/simple-plant-card"}, {"id": "147764937", "full_name": "custom-cards/surveillance-card"}, {"id": "362551242", "full_name": "itobey/update-time-card"}, {"id": "613588535", "full_name": "slipx06/sunsynk-power-flow-card"}, {"id": "259126760", "full_name": "<PERSON><PERSON>-<PERSON>-SA/honeycomb-menu"}, {"id": "907236686", "new": true, "full_name": "silentbil/silent-image-slider"}, {"id": "173955605", "full_name": "custom-cards/spotify-card"}, {"id": "263112567", "full_name": "madmicio/ampli-panel-card"}, {"id": "454670742", "full_name": "mawinkler/astroweather-card"}, {"id": "286270157", "full_name": "nielsfaber/scheduler-card"}, {"id": "158654878", "full_name": "nervetattoo/simple-thermostat"}, {"id": "236664033", "full_name": "dooz127/swipe-glance-card"}, {"id": "633889686", "full_name": "silentbil/silent-bus"}, {"id": "632590573", "full_name": "krissen/pollenprognos-card"}, {"id": "205261230", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/Home-Assistant-<PERSON><PERSON>-HTML-Jinja2-Template-card"}, {"id": "999177298", "new": true, "full_name": "jianyu-li/yet-another-media-player"}, {"id": "329411371", "full_name": "wassy92x/lovelace-ha-dashboard"}, {"id": "168939316", "new": true, "full_name": "malcolmrigg/wizard-clock-card"}, {"id": "614083491", "full_name": "rejuvenate/lovelace-horizon-card"}, {"id": "950808973", "new": true, "full_name": "KipK/openevse-card"}, {"id": "559360809", "full_name": "daredoes/linked-lovelace-ui"}, {"id": "1005504308", "new": true, "full_name": "cataseven/Google-Map-Card"}, {"id": "164022050", "full_name": "custom-cards/check-button-card"}, {"id": "498794033", "full_name": "custom-cards/slider-button-card"}, {"id": "321773656", "full_name": "nervetattoo/themable-grid"}, {"id": "977675079", "new": true, "full_name": "homeassistant-extras/pi-hole-card"}, {"id": "142051833", "full_name": "ofekashery/vertical-stack-in-card"}, {"id": "998172561", "new": true, "full_name": "blaineventurine/simple-inventory-card"}, {"id": "193408399", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/lovelace-html-card"}, {"id": "509260172", "full_name": "georgezhao2010/lovelace-curtain-card"}, {"id": "363428919", "full_name": "JurajNyiri/PlexMeetsHomeAssistant"}, {"id": "501725479", "full_name": "zanna-37/hass-swipe-navigation"}, {"id": "184333163", "full_name": "gadgetchnnel/lovelace-card-templater"}, {"id": "203294272", "full_name": "custom-cards/unused-card"}, {"id": "267558148", "full_name": "Mariusthvdb/custom-ui"}, {"id": "180229356", "full_name": "benct/lovelace-xiaomi-vacuum-card"}, {"id": "587494301", "full_name": "czz/timbox-remote-control-card"}, {"id": "326033921", "full_name": "finity69x2/toggle-control-button-row"}, {"id": "972790285", "new": true, "full_name": "nutteloost/actions-card"}, {"id": "192732636", "full_name": "bramkragten/weather-card"}, {"id": "1035084461", "new": true, "full_name": "goggybox/compact-light-card"}, {"id": "236945951", "full_name": "lukevink/lovelace-buien-rain-card"}, {"id": "289188530", "full_name": "reptilex/tesla-style-solar-power-card"}, {"id": "967737153", "new": true, "full_name": "Nerwyn/material-you-utilities"}, {"id": "142545838", "full_name": "custom-cards/canvas-gauge-card"}, {"id": "332589148", "full_name": "nicufarmache/lovelace-big-slider-card"}, {"id": "639953950", "full_name": "flixlix/energy-period-selector-plus"}, {"id": "929805127", "new": true, "full_name": "ChevronTango/nationalrail-status-card"}, {"id": "1014115522", "new": true, "full_name": "rkotulan/ha-wall-clock-card"}, {"id": "249942054", "full_name": "thomasloven/lovelace-hui-element"}, {"id": "497595623", "new": true, "full_name": "OliverEC04/compact-timetable-card"}, {"id": "1016338200", "new": true, "full_name": "timmaurice/lovelace-blitzortung-lightning-card"}, {"id": "286038496", "full_name": "tomvanswam/compass-card"}, {"id": "631525923", "full_name": "DanteWinters/lux-power-distribution-card"}, {"id": "247134044", "full_name": "faeibson/lovelace-multiline-text-input-card"}, {"id": "878557286", "new": true, "full_name": "xBourner/status-card"}, {"id": "609593305", "full_name": "t1gr0u/rain-gauge-card"}, {"id": "640244449", "full_name": "wilsto/pool-monitor-card"}, {"id": "321140869", "full_name": "ben8p/lovelace-auto-reload-card"}, {"id": "197715418", "full_name": "RodBr/miflora-card"}, {"id": "458491675", "new": true, "full_name": "NeonGrayX/lovelace-nicehash-excavator-monitor-card"}, {"id": "146335411", "full_name": "custom-cards/rmv-card"}, {"id": "185304888", "full_name": "custom-cards/text-action-element"}, {"id": "457767453", "full_name": "Kaptensanders/skolmat-card"}, {"id": "202546107", "full_name": "bbbenji/synthwave-hass-extras"}, {"id": "831739499", "new": true, "full_name": "ngocjohn/lunar-phase-card"}, {"id": "1016326705", "new": true, "full_name": "sierramike/lovelace-advanced-digital-clock"}, {"id": "169783299", "full_name": "ljmerza/github-card"}, {"id": "148520838", "full_name": "kalkih/mini-media-player"}, {"id": "207018200", "full_name": "amaximus/garbage-collection-card"}, {"id": "307058107", "full_name": "fineemb/lovelace-car-card"}, {"id": "545945955", "full_name": "marcokreeft87/formulaone-card"}, {"id": "1005187620", "new": true, "full_name": "FredrikM97/mealplan-card"}, {"id": "180528950", "full_name": "thomasloven/lovelace-more-info-card"}, {"id": "179491130", "full_name": "custom-cards/group-element"}, {"id": "762520584", "full_name": "elchininet/home-assistant-secret-taps"}, {"id": "402799177", "full_name": "tungmeister/hass-blind-card"}, {"id": "317089096", "full_name": "Tjstock/swipe-navigation-card"}, {"id": "973541248", "new": true, "full_name": "nutteloost/todo-swipe-card"}, {"id": "256292682", "full_name": "maxwroc/battery-state-card"}, {"id": "946071972", "new": true, "full_name": "ManfredTremmel/lovelace-heat-pump-card"}, {"id": "167744584", "full_name": "thomasloven/lovelace-auto-entities"}, {"id": "910225975", "new": true, "full_name": "jo-ket/compact-cover-control-card"}, {"id": "257005990", "full_name": "madmicio/LG-WebOS-Remote-Control"}, {"id": "694403206", "full_name": "argaar/comfortable-environment-card"}, {"id": "172998062", "full_name": "kalkih/simple-weather-card"}, {"id": "269474857", "full_name": "ben8p/lovelace-tab-redirect-card"}, {"id": "693656182", "full_name": "luixal/lovelace-media-source-image-card"}, {"id": "283542587", "full_name": "Villhellm/lovelace-clock-card"}, {"id": "320381430", "full_name": "tomasrudh/analogclock"}, {"id": "499270202", "full_name": "decompil3d/lovelace-hourly-weather"}, {"id": "991075417", "new": true, "full_name": "neponn/ring-tile-card"}, {"id": "366862031", "full_name": "elax46/custom-brand-icons"}, {"id": "204049047", "full_name": "Mo<PERSON>ywalker/openmensa-lovelace-card"}, {"id": "989736031", "new": true, "full_name": "prl159/custom-todo"}, {"id": "158756598", "full_name": "thomasloven/lovelace-state-switch"}, {"id": "916898208", "new": true, "full_name": "bendikrb/lovelace-form-card"}, {"id": "226862969", "full_name": "finity69x2/light-brightness-preset-row"}, {"id": "737028188", "full_name": "ibz0q/better-moment-card"}, {"id": "234961647", "full_name": "piitaya/lovelace-climate-mode-entity-row"}, {"id": "143762825", "full_name": "custom-cards/dual-gauge-card"}, {"id": "770072954", "full_name": "punxaphil/maxi-media-player"}, {"id": "667615978", "full_name": "fixtse/o365-card"}, {"id": "1006806848", "new": true, "full_name": "eyalgal/ha-shopping-list-card"}, {"id": "920836872", "new": true, "full_name": "jwillmer/tariff-chart"}, {"id": "812916796", "new": true, "full_name": "dezihh/my-harmony-card"}, {"id": "1031005600", "new": true, "full_name": "micash545/hass-selfhst-icons"}, {"id": "192835334", "full_name": "CyrisXD/love-lock-card"}, {"id": "201292040", "full_name": "azuwis/zigbee2mqtt-networkmap"}, {"id": "957100951", "new": true, "full_name": "0Paul89/vienna-transport-card"}, {"id": "1020442434", "new": true, "full_name": "cataseven/Tradingview-Widget-Card"}, {"id": "287840715", "full_name": "abmantis/ozw-network-visualization-card"}, {"id": "195671060", "full_name": "isabe<PERSON><PERSON><PERSON>/lovelace-grocy-chores-card"}, {"id": "682327546", "new": true, "full_name": "silviokennecke/ha-public-transport-connection-card"}, {"id": "586363416", "full_name": "phischdev/lovelace-mushroom-better-sliders"}, {"id": "237887092", "full_name": "fineemb/lovelace-thermostat-card"}, {"id": "351472550", "full_name": "jampez77/Multiline-Entity-Card"}, {"id": "560614992", "full_name": "flyrmyr/system-flow-card"}, {"id": "708207040", "full_name": "stefmde/HomeAssistant-TwitchFollowedLiveStreamsCard"}, {"id": "826444056", "new": true, "full_name": "christia<PERSON><PERSON><PERSON>/lovelace-brink-renovent-hru-card"}, {"id": "139634406", "full_name": "ciotlosm/lovelace-thermostat-dark-card"}, {"id": "796755589", "full_name": "elyobelyob/octopus-energy-greenness-forecast-card"}, {"id": "248954055", "full_name": "custom-cards/stack-in-card"}, {"id": "168570875", "full_name": "ljmerza/fitbit-card"}, {"id": "720062465", "full_name": "trollix/ha-tsmoon-card"}, {"id": "995434621", "new": true, "full_name": "droans/mass_card"}, {"id": "1009957674", "new": true, "full_name": "BigPiloto/ha-drugstore-stock-card"}, {"id": "976037481", "new": true, "full_name": "TheRealEiskaffee/brightness-overlay"}, {"id": "455846088", "full_name": "MindFreeze/ha-sankey-chart"}, {"id": "265313034", "full_name": "dimagoltsman/refreshable-picture-card"}, {"id": "745000799", "full_name": "Brianfit/xkcd-card-ha"}, {"id": "321071222", "full_name": "DanChaltiel/heatzy-pilote-card"}, {"id": "220679143", "full_name": "hasl-sensor/lovelace-hasl-departure-card"}, {"id": "951762554", "new": true, "full_name": "yohaybn/lovelace-aliexpress-package-card"}, {"id": "168744428", "full_name": "ljmerza/light-entity-card"}, {"id": "946733708", "new": true, "full_name": "homeassistant-extras/device-card"}, {"id": "182113743", "full_name": "gadgetchnnel/lovelace-text-input-row"}, {"id": "782924117", "full_name": "kevin-briand/massa-node-card"}, {"id": "939311749", "new": true, "full_name": "alexpfau/calendar-card-pro"}, {"id": "231015759", "full_name": "ikaruswill/lovelace-fan-xiaomi"}, {"id": "245239101", "full_name": "gadgetchnnel/lovelace-card-preloader"}, {"id": "237532750", "full_name": "sbryfcz/harmony-card"}, {"id": "776014198", "full_name": "MadSnuif/hockeynl-card"}, {"id": "231145540", "full_name": "dcramer/lovelace-nextbus-card"}, {"id": "572297252", "full_name": "petergridge/Irrigation-Card"}, {"id": "250552447", "full_name": "finity69x2/binary-control-button-row"}, {"id": "683553739", "full_name": "elchininet/keep-texts-in-tabs"}, {"id": "172177543", "full_name": "iantrich/config-template-card"}, {"id": "587057164", "full_name": "kizza/magic-home-party-card"}, {"id": "655713953", "new": true, "full_name": "EnkodoNL/tabbed-card-programmable"}, {"id": "361776538", "full_name": "adizanni/floor3d-card"}, {"id": "640556013", "full_name": "zeronounours/lovelace-energy-entity-row"}, {"id": "159711605", "full_name": "custom-cards/secondaryinfo-entity-row"}, {"id": "257123327", "full_name": "madmicio/channel-pad"}, {"id": "391372854", "full_name": "nielsfaber/alarmo-card"}, {"id": "734825457", "full_name": "nathan-gs/ha-map-card"}, {"id": "911369599", "new": true, "full_name": "xBourner/area-card-plus"}, {"id": "864920721", "new": true, "full_name": "samuel<PERSON>ell/clock-weather-card-hui-icons"}, {"id": "834434033", "new": true, "full_name": "hyperb1iss/hyper-light-card"}, {"id": "197245179", "full_name": "twrecked/lovelace-hass-aarlo"}, {"id": "487680971", "full_name": "Makin-Things/weather-radar-card"}, {"id": "197960232", "full_name": "ljmerza/our-groceries-card"}, {"id": "914649610", "new": true, "full_name": "mon3y78/Lovelace-Bubble-room"}, {"id": "654393646", "full_name": "Nerwyn/universal-remote-card"}, {"id": "755763613", "full_name": "mentalilll/ha-vpd-chart"}, {"id": "859371663", "new": true, "full_name": "ngocjohn/vehicle-status-card"}, {"id": "187245495", "full_name": "custom-cards/gauge-card"}, {"id": "174016256", "full_name": "gadgetchnnel/lovelace-home-feed-card"}, {"id": "181124811", "full_name": "iantrich/radial-menu"}, {"id": "186765704", "full_name": "nervetattoo/banner-card"}, {"id": "203036108", "full_name": "<PERSON><PERSON><PERSON><PERSON>/lovelace-valetudo-map-card"}, {"id": "260940136", "full_name": "r-renato/ha-card-weather-conditions"}, {"id": "263901624", "full_name": "dimagoltsman/generic-remote-control-card"}, {"id": "926281076", "new": true, "full_name": "Tomer27cz/energy-line-gauge"}, {"id": "237812136", "full_name": "iswitch/ha-yandex-icons"}, {"id": "283578257", "full_name": "JonahKr/power-distribution-card"}, {"id": "192732887", "full_name": "bramkragten/swipe-card"}, {"id": "771728766", "full_name": "FamousWolf/week-planner-card"}, {"id": "989153227", "new": true, "full_name": "sxdjt/horizontal-waterfall-history-card"}, {"id": "776846977", "full_name": "roman-16/better-miflora-card"}, {"id": "188572845", "full_name": "marrobHD/rotel-card"}, {"id": "156434866", "full_name": "thomasloven/lovelace-layout-card"}, {"id": "236317072", "full_name": "sdelliot/pie-chart-card"}, {"id": "489457357", "full_name": "junalmeida/homeassistant-minimalistic-area-card"}, {"id": "444350375", "repository_manifest": {"name": "Mushroom", "filename": "mushroom.js", "homeassistant": "2024.8", "render_readme": true}, "full_name": "piitaya/lovelace-mushroom", "category": "plugin", "description": "Build a beautiful Home Assistant dashboard easily", "downloads": 165056, "etag_repository": "W/\"8cd7e1531bb385d96c8ef1c10299410c72d0aea1432ab1c759d34fb17e45a352\"", "last_updated": "2025-08-12T13:02:33Z", "stargazers_count": 4427, "topics": ["card", "mushroom"], "installed_commit": "f600fd8", "installed": true, "last_commit": "54b92c5", "last_version": "v4.5.0", "open_issues": 406, "releases": true, "version_installed": "v4.5.0", "last_fetched": 1756463028.385}, {"id": "179808576", "full_name": "hulkhaugen/hass-bha-icons"}, {"id": "207292725", "full_name": "AmoebeLabs/flex-horseshoe-card"}, {"id": "536329656", "full_name": "PRProd/HA-Firemote"}, {"id": "179788256", "full_name": "iantrich/text-divider-row"}, {"id": "241706284", "full_name": "Savjee/button-text-card"}, {"id": "261291295", "full_name": "denysdovhan/vacuum-card"}, {"id": "300754203", "full_name": "sopelj/lovelace-kanji-clock-card"}, {"id": "187339794", "full_name": "marrobHD/tv-card"}, {"id": "157674859", "full_name": "dnguyen800/air-visual-card"}, {"id": "361205663", "full_name": "MrBartusek/MeteoalarmCard"}, {"id": "728990161", "full_name": "Mariusthvdb/custom-attributes"}, {"id": "161403328", "full_name": "thomasloven/lovelace-card-tools"}, {"id": "292008305", "full_name": "Kibibit/kb-steam-card"}, {"id": "622713177", "full_name": "VeniVidiVici/givtcp-power-flow-card"}, {"id": "591270696", "full_name": "aukedejong/lovelace-windrose-card"}, {"id": "737780218", "full_name": "elchininet/custom-sidebar"}, {"id": "423082071", "full_name": "pmongloid/flipdown-timer-card"}, {"id": "273405252", "full_name": "ironsheep/lovelace-lightning-detector-card"}, {"id": "783046111", "new": true, "full_name": "ibz0q/lovelace-bg-animation"}, {"id": "520644302", "full_name": "MathisAlepis/lovelace-tam-card"}, {"id": "352399227", "full_name": "mampfes/ha-knx-uf-iconset"}, {"id": "925327260", "new": true, "full_name": "skydarc/Venus-OS-Dashboard"}, {"id": "871730343", "new": true, "full_name": "selvalt7/modern-circular-gauge"}, {"id": "188686483", "full_name": "custom-cards/decluttering-card"}, {"id": "303857065", "full_name": "benct/lovelace-battery-entity-row"}, {"id": "313269367", "full_name": "Kibibit/kb-better-graph-colors"}, {"id": "370997019", "full_name": "bernikr/lovelace-webos-keyboard-card"}, {"id": "350509867", "full_name": "dylandoamaral/uptime-card"}, {"id": "433577603", "full_name": "junkfix/config-editor-card"}, {"id": "286860710", "full_name": "finity69x2/cover-position-preset-row"}, {"id": "244872232", "full_name": "jcwillox/lovelace-paper-buttons-row"}, {"id": "431901513", "full_name": "swingerman/lovelace-fluid-level-background-card"}, {"id": "199546187", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/lovelace-google-keep-card"}, {"id": "328957716", "full_name": "Kirb<PERSON>/ha-lovelace-elapsed-time-card"}, {"id": "275672933", "full_name": "mattieha/select-list-card"}, {"id": "855034301", "new": true, "full_name": "Bobsilvio/calcio-live-card"}, {"id": "665501829", "full_name": "mlamberts78/weather-chart-card"}, {"id": "235449701", "full_name": "chaptergy/lightalarm-card"}, {"id": "1040230985", "new": true, "full_name": "timmaurice/lovelace-rss-accordion"}, {"id": "677140532", "new": true, "full_name": "MelleD/lovelace-expander-card"}, {"id": "447474061", "full_name": "rgc99/irrigation-unlimited-card"}, {"id": "831352324", "new": true, "full_name": "LesTR/homeassistant-minimalistic-area-card"}, {"id": "867866479", "new": true, "full_name": "Nicxe/home-assistant-sm<PERSON><PERSON>t-card"}, {"id": "543068603", "full_name": "abualy/philips-tv-remote-card"}, {"id": "273007955", "full_name": "DBa2016/power-usage-card-regex"}, {"id": "733922637", "full_name": "Mariusthvdb/custom-more-info"}, {"id": "180000010", "full_name": "custom-cards/cover-element"}, {"id": "897844707", "new": true, "full_name": "lovelylain/ha-addon-iframe-card"}, {"id": "*********", "full_name": "Makin-Things/bom-radar-card"}, {"id": "*********", "full_name": "queimadus/last-changed-element"}, {"id": "*********", "new": true, "full_name": "nathkrill/lovelace-google-fonts-header-card"}, {"id": "*********", "full_name": "nathan<PERSON><PERSON>/foxess_modbus_charge_period_card"}, {"id": "*********", "full_name": "rautesamtr/thermal_comfort_icons"}, {"id": "*********", "full_name": "karlis-vagalis/circular-timer-card"}, {"id": "*********", "full_name": "ljmerza/travel-time-card"}, {"id": "*********", "full_name": "j-a-n/lovelace-wallpanel"}, {"id": "*********", "full_name": "fineemb/lovelace-cn-map-card"}, {"id": "*********", "full_name": "thomasloven/lovelace-badge-card"}, {"id": "*********", "full_name": "dbuezas/lovelace-plotly-graph-card"}, {"id": "*********", "full_name": "custom-cards/group-card"}, {"id": "*********", "full_name": "fineemb/lovelace-fan-xiaomi"}, {"id": "*********", "new": true, "full_name": "snootched/cb-lcars"}, {"id": "*********", "new": true, "full_name": "trollix/ha-tbaro-card"}, {"id": "*********", "full_name": "ljmerza/harmony-remote-card"}, {"id": "*********", "full_name": "tcarlsen/lovelace-light-with-profiles"}, {"id": "*********", "full_name": "vpdchart/vpdchart-card"}, {"id": "*********", "full_name": "amaximus/bkk-stop-card"}, {"id": "*********", "full_name": "NemesisRE/upcoming-media-card"}, {"id": "*********", "full_name": "finity69x2/cover-control-button-row"}, {"id": "*********", "full_name": "drakulis/jb-battery-card"}, {"id": "577071460", "full_name": "madmicio/ph-meter-temperature"}, {"id": "725762333", "full_name": "bolkedebruin/erhv-lovelace"}, {"id": "187247927", "full_name": "custom-cards/plan-coordinates"}, {"id": "163446489", "full_name": "jonk<PERSON>ian/entur-card"}, {"id": "556306418", "full_name": "amitfin/lovelace-daily-schedule-card"}, {"id": "637041617", "full_name": "pgorod/power-todoist-card"}, {"id": "715374965", "full_name": "wrodie/mixer-card"}, {"id": "1028645841", "new": true, "full_name": "redkanoon/embedded-view-card"}, {"id": "446955395", "full_name": "iablon/HomeAssistant-Touchpad-Card"}, {"id": "191663150", "full_name": "finity69x2/fan-control-entity-row"}, {"id": "801915116", "new": true, "full_name": "timmaurice/lovelace-background-graph-entities"}, {"id": "195497310", "full_name": "DavidFW1960/bom-weather-card"}, {"id": "315044466", "full_name": "amaximus/transmission-card"}, {"id": "998247638", "new": true, "full_name": "dropqube/pv-forecast-card"}, {"id": "911265664", "new": true, "full_name": "Brianfit/calvin-card-ha"}, {"id": "452251255", "full_name": "mathoudebine/homeassistant-browser-control-card"}, {"id": "701044448", "full_name": "wiltodelta/homeassistant-sugartv-card"}, {"id": "302895020", "full_name": "maxwroc/github-flexi-card"}, {"id": "373832981", "full_name": "wassy92x/lovelace-digital-clock"}, {"id": "853369948", "new": true, "full_name": "redstone99/hass-alert2-ui"}, {"id": "141952963", "full_name": "custom-cards/circle-sensor-card"}, {"id": "334448958", "full_name": "ownbee/bootstrap-grid-card"}, {"id": "231674882", "full_name": "thomasloven/lovelace-template-entity-row"}, {"id": "522634019", "full_name": "pkissling/clock-weather-card"}, {"id": "376904517", "full_name": "rianadon/timer-bar-card"}, {"id": "335713085", "full_name": "grinstantin/todoist-card"}, {"id": "987191427", "new": true, "full_name": "DFranzen/Simple-Universal-Touchpad-for-Homeassistant"}, {"id": "537793361", "full_name": "Nyar<PERSON>/myjdownloader-card"}, {"id": "330454534", "full_name": "r<PERSON><PERSON>an/lovelace-lamar<PERSON><PERSON>-config-card"}, {"id": "193262086", "full_name": "dimagoltsman/content-card-remote-control"}, {"id": "993095819", "new": true, "full_name": "SirLancillottoDev/smart-irrigation-card"}, {"id": "925834835", "new": true, "full_name": "ChevronTango/tfl-status-card"}, {"id": "197929015", "full_name": "dmulcahey/zha-network-card"}, {"id": "649637656", "full_name": "Codegnosis/givtcp-battery-card"}, {"id": "439367892", "full_name": "KartoffelToby/better-thermostat-ui-card"}, {"id": "680112919", "repository_manifest": {"name": "Bubble Card", "render_readme": true, "filename": "bubble-card.js", "homeassistant": "2023.9.0"}, "full_name": "Clooos/Bubble-Card", "category": "plugin", "description": "Bubble Card is a minimalist card collection for Home Assistant with a nice pop-up touch.", "etag_repository": "W/\"633aeaa36aa60e4fcebf5c919dfacdb48c9d28243a066ae2b0ab16ffca169888\"", "last_updated": "2025-08-28T14:20:17Z", "stargazers_count": 3138, "topics": ["button", "card", "cards", "dashboard", "frontend", "lovelace-custom-card", "minimalist", "mobile-first", "pop-up", "popup", "slider"], "installed_commit": "a1bb17f", "installed": true, "last_commit": "68e07cd", "last_version": "v3.0.3", "open_issues": 100, "releases": true, "version_installed": "v3.0.3", "last_fetched": 1756462873.696177}, {"id": "214365813", "full_name": "Anonym-tsk/lovelace-starline-card"}, {"id": "304967918", "full_name": "junkfix/numberbox-card"}, {"id": "698501153", "new": true, "full_name": "Nerwyn/custom-card-features"}, {"id": "237620254", "full_name": "tholgir/TodoIst-Task-List"}, {"id": "274738925", "full_name": "denysdovhan/purifier-card"}, {"id": "505459170", "full_name": "usernein/tv-card"}, {"id": "812083995", "full_name": "itsteddyyo/strategy-pack"}, {"id": "971870393", "new": true, "full_name": "microteq/extended-gauge"}, {"id": "276636213", "full_name": "konnectedvn/lovelace-vertical-slider-cover-card"}, {"id": "197759180", "full_name": "postlund/search-card"}, {"id": "933034125", "new": true, "full_name": "homeassistant-extras/zwave-card-set"}, {"id": "607398282", "full_name": "t1gr0u/uv-index-card"}, {"id": "144899700", "full_name": "thomasloven/lovelace-slider-entity-row"}, {"id": "452866308", "full_name": "kinghat/tabbed-card"}, {"id": "437989480", "full_name": "9a4gl/lovelace-centrometal-boiler-card"}, {"id": "187245418", "full_name": "custom-cards/bignumber-card"}, {"id": "281214271", "full_name": "ironsheep/lovelace-rpi-monitor-card"}, {"id": "1021112812", "new": true, "full_name": "sierramike/lovelace-simple-navbar"}, {"id": "190927524", "full_name": "thomasloven/lovelace-card-mod"}, {"id": "871106499", "new": true, "full_name": "jose<PERSON>is9595/lovelace-navbar-card"}, {"id": "646272989", "full_name": "usernein/tailwindcss-template-card"}, {"id": "763203701", "full_name": "RJArmitage/rfxtrx-stateful-blinds-icons"}, {"id": "907336487", "new": true, "full_name": "dmatik/switcher-boiler-card"}, {"id": "290281267", "full_name": "KTibow/fullscreen-card"}, {"id": "193372044", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/lovelace-xiaomi-vacuum-map-card"}, {"id": "183499944", "full_name": "ljmerza/tracking-number-card"}, {"id": "526408682", "full_name": "vasqued2/ha-teamtracker-card"}, {"id": "1020949375", "new": true, "full_name": "unbekannt3/room-card-minimalist"}, {"id": "246549747", "full_name": "totaldebug/atomic-calendar-revive"}, {"id": "240906060", "full_name": "fineemb/lovelace-dc1-card"}, {"id": "466196192", "full_name": "gadgetchnnel/lovelace-header-cards"}, {"id": "567030726", "full_name": "daredoes/default-dashboard"}, {"id": "484538222", "full_name": "ulic75/power-flow-card"}, {"id": "264796130", "full_name": "turbulator/pandora-cas-card"}, {"id": "919209567", "new": true, "full_name": "homeassistant-extras/room-summary-card"}, {"id": "254206234", "full_name": "danimart1991/pvpc-hourly-pricing-card"}, {"id": "497319128", "repository_manifest": {"name": "Kiosk Mode", "filename": "kiosk-mode.js", "render_readme": true}, "full_name": "NemesisRE/kiosk-mode", "category": "plugin", "description": "🙈 Hides the Home Assistant header and/or sidebar", "downloads": 37197, "etag_repository": "W/\"7288c6e602492adf0f006bad4be11dfe3b5a81148be5fe95a4cc99b0a54c67be\"", "last_updated": "2025-08-27T18:53:03Z", "stargazers_count": 563, "topics": ["customization"], "installed_commit": "17eb51f", "installed": true, "last_commit": "4efe902", "last_version": "v7.2.0", "releases": true, "version_installed": "v7.2.0", "last_fetched": 1756326296.459725}, {"id": "331701152", "full_name": "RomRider/apexcharts-card"}, {"id": "215633404", "full_name": "iantrich/restriction-card"}, {"id": "913623169", "new": true, "full_name": "homeassistant-extras/toolbar-status-chips"}, {"id": "997942648", "new": true, "full_name": "entekadesign/kobold-alarm-clock-card"}, {"id": "1020654222", "new": true, "full_name": "cataseven/Strip-Card"}, {"id": "220679530", "full_name": "hasl-sensor/lovelace-hasl-traffic-status-card"}, {"id": "894626535", "new": true, "full_name": "ytilis/hass-progress-bar-feature"}, {"id": "150781994", "full_name": "thomasloven/lovelace-fold-entity-row"}, {"id": "634013716", "full_name": "krissen/sixdegrees-card"}, {"id": "214786112", "full_name": "AmoebeLabs/swiss-army-knife-card"}, {"id": "599334003", "full_name": "pkscout/simple-weather-clock"}, {"id": "302122266", "full_name": "queimadus/cover-icon-element"}, {"id": "450898706", "full_name": "frozenwizard/onlylocklock"}, {"id": "725015794", "full_name": "Mariusthvdb/custom-icon-color"}, {"id": "974898979", "new": true, "full_name": "Pho3niX90/jk-bms-card"}, {"id": "313270182", "full_name": "Kibibit/kb-frosted-cards"}, {"id": "238414582", "full_name": "peeter<PERSON><PERSON>k/ztm-stop-card"}, {"id": "943585734", "new": true, "full_name": "homeassistant-extras/petkit-device-cards"}, {"id": "350886220", "full_name": "finity69x2/fan-mode-button-row"}, {"id": "1037202197", "new": true, "full_name": "nitaybz/apple-home-dashboard"}, {"id": "286408741", "full_name": "ezand/lovelace-posten-card"}, {"id": "678764124", "full_name": "idaho/hassio-trash-card"}, {"id": "760703637", "full_name": "Anrolosia/Shopping-List-with-Grocy-Card"}, {"id": "985404932", "new": true, "full_name": "JMatuszczakk/Locked-<PERSON>ton"}, {"id": "146783593", "full_name": "custom-cards/upcoming-media-card"}, {"id": "392931946", "full_name": "deblockt/aria2-card"}, {"id": "*********", "new": true, "full_name": "silvanocerza/light-card-hue-feature"}, {"id": "*********", "full_name": "jere<PERSON><PERSON><PERSON>/lovelace-roomba-vacuum-card"}, {"id": "*********", "full_name": "Makin-Things/platinum-weather-card"}, {"id": "*********", "full_name": "custom-cards/button-card"}, {"id": "*********", "new": true, "full_name": "davet2001/energy-sankey"}, {"id": "*********", "full_name": "jtbgroup/kodi-playlist-card"}, {"id": "*********", "full_name": "r-renato/ha-card-waze-travel-time"}, {"id": "*********", "full_name": "rianadon/opensprinkler-card"}, {"id": "*********", "full_name": "flixlix/energy-gauge-bundle-card"}, {"id": "*********", "full_name": "Voxxie/lovelace-jumbo-card"}, {"id": "*********", "new": true, "full_name": "leshniak/hass-auth-cookie"}, {"id": "*********", "new": true, "full_name": "marcelhoogantink/enhanced-shutter-card"}, {"id": "*********", "full_name": "ExperienceLovelace/ha-floorplan"}, {"id": "*********", "full_name": "javawizard/ha-navbar-position"}, {"id": "*********", "full_name": "tdvtdv/ha-tdv-bar"}, {"id": "*********", "full_name": "amaximus/pollen-hu-card"}, {"id": "*********", "full_name": "a-p-z/datetime-card"}, {"id": "*********", "full_name": "DigiLive/mushroom-strategy"}, {"id": "*********", "full_name": "kverqus/lovelace-hassam-card"}, {"id": "*********", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>/linak-desk-card"}, {"id": "*********", "new": true, "full_name": "nutteloost/simple-swipe-card"}, {"id": "638230244", "full_name": "flixlix/energy-flow-card-plus"}, {"id": "963750120", "new": true, "full_name": "benjamin-dcs/gauge-card-pro"}, {"id": "904283635", "new": true, "full_name": "madmicio/screensaver-card"}, {"id": "1023363899", "new": true, "full_name": "eyalgal/hatch-card"}, {"id": "689793414", "full_name": "ShadowAya/anchor-card"}, {"id": "666807633", "new": true, "full_name": "zanac/temperature-heatmap-card"}, {"id": "341931266", "full_name": "fufar/simple-clock-card"}], "theme": [{"id": "464998514", "full_name": "flejz/hass-cyberpunk-2077-theme"}, {"id": "284294048", "full_name": "JuanMTech/ios_light_mode_theme"}, {"id": "779268034", "full_name": "chaptergy/homeassistant-theme-dark-pastel"}, {"id": "235057110", "full_name": "home-assistant-community-themes/material-dark-red"}, {"id": "162468030", "full_name": "awolkers/home-assistant-themes"}, {"id": "534353896", "full_name": "th3jesta/ha-lcars"}, {"id": "230672465", "full_name": "houtknots/UglyChristmas-Theme"}, {"id": "1002719232", "new": true, "full_name": "mjs271/arcticForest-dark_HA"}, {"id": "320117484", "full_name": "myleskeeffe/clear-theme-dark-vibrant"}, {"id": "458636658", "full_name": "JuanMTech/ios-theme"}, {"id": "236277163", "full_name": "hekm77/reeder_dark_theme"}, {"id": "743026166", "full_name": "brezlord/BrezNET-iOS"}, {"id": "703332819", "full_name": "malcolm<PERSON>bull/draculaish-ha-theme"}, {"id": "253311340", "full_name": "3ative/3ative-blue-theme"}, {"id": "897275941", "new": true, "full_name": "taikun114/Blue-Theme-by-taikun114"}, {"id": "456201687", "full_name": "piitaya/lovelace-mushroom-themes"}, {"id": "215075899", "full_name": "home-assistant-community-themes/grey-night"}, {"id": "977691784", "new": true, "full_name": "knightburton/minimal-ninja-theme"}, {"id": "729280147", "full_name": "Djelle/milcomarmy"}, {"id": "230974064", "full_name": "arsaboo/oxford_blue_theme"}, {"id": "216181396", "full_name": "home-assistant-community-themes/teal"}, {"id": "202203063", "full_name": "bbbenji/synthwave-hass"}, {"id": "216173358", "full_name": "home-assistant-community-themes/christmas"}, {"id": "214979604", "full_name": "home-assistant-community-themes/dark-mint"}, {"id": "403381222", "full_name": "will<PERSON><PERSON><PERSON><PERSON>/noctis-solarized"}, {"id": "234375294", "full_name": "home-assistant-community-themes/vaporwave-pink"}, {"id": "306914292", "full_name": "JOHLC/transparentblue"}, {"id": "233445397", "full_name": "am80l/sundown"}, {"id": "221287384", "full_name": "naofireblade/clear-theme"}, {"id": "231829137", "full_name": "aFFekopp/noctis"}, {"id": "234032927", "full_name": "JuanMTech/google_light_theme"}, {"id": "216178553", "full_name": "home-assistant-community-themes/material-dark-green"}, {"id": "197006509", "full_name": "seangreen2/slate_theme"}, {"id": "574163721", "full_name": "TilmanGriesel/graphite"}, {"id": "236318024", "full_name": "basnijholt/lovelace-ios-themes"}, {"id": "407627914", "full_name": "pacjo/google_dark_animated"}, {"id": "215075805", "full_name": "home-assistant-community-themes/blue-night"}, {"id": "216183299", "full_name": "home-assistant-community-themes/material-dark-pink"}, {"id": "470262899", "full_name": "coltondick/nordic-theme-main"}, {"id": "871683033", "new": true, "full_name": "PixNyb/hass-theme-blocky"}, {"id": "217374413", "full_name": "home-assistant-community-themes/halloween"}, {"id": "1010117109", "new": true, "full_name": "loryanstrant/blackout"}, {"id": "448355900", "full_name": "SnakeFist007/ha_vastayan_bond"}, {"id": "209891408", "full_name": "home-assistant-community-themes/amoled"}, {"id": "234750356", "full_name": "basnij<PERSON>t/lovelace-ios-light-mode-theme"}, {"id": "1005176721", "new": true, "full_name": "Xitee1/ha-amoled-theme"}, {"id": "1012545675", "new": true, "full_name": "wessamlauf/homeassistant-frosted-glass-themes"}, {"id": "457458731", "full_name": "AmoebeLabs/HA-Theme_M3-07-DarkOliveGreen"}, {"id": "479056577", "full_name": "Matt-PMCT/Green-and-Dark-HA-Theme"}, {"id": "223938651", "full_name": "home-assistant-community-themes/midnight-blue"}, {"id": "235984421", "full_name": "home-assistant-community-themes/blackened"}, {"id": "270638476", "full_name": "home-assistant-community-themes/nord"}, {"id": "249722008", "full_name": "<PERSON><PERSON><PERSON>/your_name"}, {"id": "220641275", "full_name": "home-assistant-community-themes/dark-orange"}, {"id": "255366214", "full_name": "Banditen01/vintage_theme"}, {"id": "480992848", "full_name": "JuanMTech/macOS-Theme"}, {"id": "225969186", "full_name": "home-assistant-community-themes/aqua-fiesta"}, {"id": "309056232", "full_name": "einschmidt/github_dark_theme"}, {"id": "458817847", "full_name": "AmoebeLabs/HA-Theme_M3-04-<PERSON><PERSON><PERSON>"}, {"id": "261924981", "full_name": "<PERSON>S<PERSON>/swart_ninja_dark_theme"}, {"id": "234581410", "full_name": "fi-sch/ux_goodie_theme"}, {"id": "767743196", "full_name": "<PERSON><PERSON><PERSON><PERSON>/home-assistant-bootstrap-5-theme"}, {"id": "226567922", "full_name": "Poeschl/slate_red"}, {"id": "292621909", "full_name": "mikosoft83/hass-windows10-themes"}, {"id": "486045869", "full_name": "<PERSON><PERSON>/Metrology-for-Hass"}, {"id": "274111031", "full_name": "wowgamr/animated-weather-card"}, {"id": "701591334", "new": true, "full_name": "Nerwyn/material-you-theme"}, {"id": "458664750", "full_name": "JuanMTech/google-theme"}, {"id": "461936688", "full_name": "robinwittebol/whatsapp-theme"}, {"id": "984753881", "new": true, "full_name": "bessertristan09/graphite-nightshade-theme"}, {"id": "284293899", "full_name": "JuanMTech/ios_dark_mode_theme"}, {"id": "227988032", "full_name": "estiens/sweet_pink_hass_theme"}, {"id": "216165131", "full_name": "home-assistant-community-themes/solarized-light"}, {"id": "234022648", "full_name": "JuanMTech/google_dark_theme"}, {"id": "231083679", "full_name": "aFFekopp/dark_teal"}, {"id": "443651710", "full_name": "Neekster/MidnightTeal"}, {"id": "277068969", "full_name": "ricardoquecria/caule-themes-pack-1"}, {"id": "255270395", "full_name": "home-assistant-community-themes/stell-blue-with-colors"}, {"id": "233715171", "full_name": "78<PERSON>ley/Home-Assistant-Dark<PERSON>-Theme"}, {"id": "441738040", "full_name": "KTibow/lovelace-soft-theme"}, {"id": "309053262", "full_name": "einschmidt/github_light_theme"}, {"id": "498774862", "full_name": "AmoebeLabs/HA-Theme_M3-C11-Purple"}, {"id": "793248285", "full_name": "Nezz/homeassistant-visionos-theme"}, {"id": "387055527", "full_name": "tgcowell/waves"}, {"id": "214664317", "full_name": "home-assistant-community-themes/midnight"}, {"id": "221288367", "full_name": "naofireblade/clear-theme-dark"}, {"id": "223028160", "full_name": "pbeckcom/green_slate_theme"}, {"id": "825856446", "new": true, "full_name": "veniplex/hass-idx-theme"}, {"id": "222422187", "full_name": "basnij<PERSON>t/lovelace-ios-dark-mode-theme"}, {"id": "570909059", "full_name": "catppuccin/home-assistant"}], "template": [{"id": "656746023", "full_name": "langestefan/auto-sun-blind"}, {"id": "629510143", "full_name": "TheFes/cheapest-energy-hours"}, {"id": "930250618", "full_name": "Nuh<PERSON>/jinja-speech-helpers-german"}, {"id": "694582318", "full_name": "Sir<PERSON><PERSON>eno<PERSON>/Logic-Chekr"}, {"id": "680978332", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>/Availability-Template"}, {"id": "724754572", "full_name": "SirGoodenough/Color-Multi-Tool"}, {"id": "912235383", "full_name": "jazzyisj/speech-helpers-jinja"}, {"id": "624337782", "full_name": "TheFes/relative-time-plus"}, {"id": "624674515", "full_name": "Petro31/easy-time-jinja"}]}}}