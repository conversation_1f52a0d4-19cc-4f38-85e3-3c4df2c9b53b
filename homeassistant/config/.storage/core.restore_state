{"version": 1, "minor_version": 1, "key": "core.restore_state", "data": [{"state": {"entity_id": "conversation.home_assistant", "state": "unknown", "attributes": {"friendly_name": "Home Assistant", "supported_features": 1}, "last_changed": "2025-08-29T15:04:24.351819+00:00", "last_reported": "2025-08-29T15:04:24.351819+00:00", "last_updated": "2025-08-29T15:04:24.351819+00:00", "context": {"id": "01K3V5QFGZV9SXPQC2M8QD24P7", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "event.backup_automatic_backup", "state": "2025-08-28T21:15:41.784+00:00", "attributes": {"event_types": ["completed", "failed", "in_progress"], "event_type": "completed", "backup_stage": null, "failed_reason": null, "friendly_name": "Backup Automatic backup"}, "last_changed": "2025-08-29T15:04:24.362364+00:00", "last_reported": "2025-08-29T15:04:24.362364+00:00", "last_updated": "2025-08-29T15:04:24.362364+00:00", "context": {"id": "01K3V5QFHAZ6ACYDT92DASE9DF", "parent_id": null, "user_id": null}}, "extra_data": {"last_event_type": "completed", "last_event_attributes": {"backup_stage": null, "failed_reason": null}}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "input_boolean.mailbox", "state": "off", "attributes": {"editable": true, "icon": "mdi:mailbox", "friendly_name": "Mailbox"}, "last_changed": "2025-08-29T15:04:24.399498+00:00", "last_reported": "2025-08-29T15:04:24.399498+00:00", "last_updated": "2025-08-29T15:04:24.399498+00:00", "context": {"id": "01K3V5QFJFQ1QFT4WSX1SQ7D6X", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "person.karl_li", "state": "home", "attributes": {"editable": true, "id": "karl_li", "device_trackers": ["device_tracker.karls_iphone_16_pro", "device_tracker.ipad_mini"], "latitude": 25.12800568419237, "longitude": 121.79796554808982, "gps_accuracy": 3, "source": "device_tracker.karls_iphone_16_pro", "user_id": "9b1cbb01e5804f8d8d27eeb071638aab", "friendly_name": "<PERSON>"}, "last_changed": "2025-08-29T15:04:24.399591+00:00", "last_reported": "2025-08-29T15:10:29.214594+00:00", "last_updated": "2025-08-29T15:10:29.214594+00:00", "context": {"id": "01K3V62KTYY7A0TYKZ55NJE8YG", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "person.phibie", "state": "home", "attributes": {"editable": true, "id": "phi<PERSON>", "device_trackers": ["device_tracker.phibz_iphone15"], "latitude": 25.128284539486707, "longitude": 121.7974940422, "gps_accuracy": 19, "source": "device_tracker.phibz_iphone15", "user_id": "c752771c5ce34b52b02cb076cd9db031", "friendly_name": "<PERSON><PERSON>"}, "last_changed": "2025-08-29T15:04:24.399668+00:00", "last_reported": "2025-08-29T15:10:29.214793+00:00", "last_updated": "2025-08-29T15:10:29.214793+00:00", "context": {"id": "01K3V62KTYPWW90G5RFH6JW2X0", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "timer.garage_light_off_delay", "state": "idle", "attributes": {"duration": "0:02:00", "editable": false, "icon": "mdi:timer-outline", "friendly_name": "車庫燈倒數"}, "last_changed": "2025-08-29T15:57:53.003881+00:00", "last_reported": "2025-08-29T15:57:53.003881+00:00", "last_updated": "2025-08-29T15:57:53.003881+00:00", "context": {"id": "01K3V8SCZBJ4KN0FKCS41F962H", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.adaptive_lighting_sleep_mode_qk_cblc5", "state": "off", "attributes": {"icon": "mdi:sleep", "friendly_name": "Adaptive Lighting Sleep Mode: QK-CBLC5"}, "last_changed": "2025-08-29T15:04:26.696791+00:00", "last_reported": "2025-08-29T15:10:20.298195+00:00", "last_updated": "2025-08-29T15:04:26.696791+00:00", "context": {"id": "01K3V5QHT8K05A7C5C82ERG8Z7", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.adaptive_lighting_adapt_color_qk_cblc5", "state": "on", "attributes": {"icon": "mdi:sun-thermometer", "friendly_name": "Adaptive Lighting Adapt Color: QK-CBLC5"}, "last_changed": "2025-08-29T15:04:26.696861+00:00", "last_reported": "2025-08-29T15:10:20.298363+00:00", "last_updated": "2025-08-29T15:04:26.696861+00:00", "context": {"id": "01K3V5QHT8XX9PSRNGMHW9S3JN", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.adaptive_lighting_adapt_brightness_qk_cblc5", "state": "on", "attributes": {"icon": "mdi:brightness-4", "friendly_name": "Adaptive Lighting Adapt Brightness: QK-CBLC5"}, "last_changed": "2025-08-29T15:04:26.696920+00:00", "last_reported": "2025-08-29T15:10:20.298430+00:00", "last_updated": "2025-08-29T15:04:26.696920+00:00", "context": {"id": "01K3V5QHT8EJMYVAR3PXJZB3M7", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.adaptive_lighting_qk_cblc5", "state": "on", "attributes": {"configuration": {}, "manual_control": [], "brightness_pct": 15.206611544671839, "color_temp_kelvin": 2000, "color_temp_mired": 500, "rgb_color": [255, 136.86832541739756, 13.904070298965905], "xy_color": [0.598, 0.383], "hs_color": [30.504, 93.333], "sun_position": -0.9975692759450372, "force_rgb_color": false, "autoreset_time_remaining": {}, "icon": "mdi:theme-light-dark", "friendly_name": "Adaptive Lighting: QK-CBLC5"}, "last_changed": "2025-08-29T15:04:26.697333+00:00", "last_reported": "2025-08-29T16:10:21.390056+00:00", "last_updated": "2025-08-29T16:09:28.179639+00:00", "context": {"id": "01K3V9EKVKVW5P15MQH2X2AF50", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "binary_sensor.karls_iphone_16_pro_focus", "state": "off", "attributes": {"icon": "mdi:moon-waning-crescent", "friendly_name": "<PERSON>’s iPhone 16 Pro Focus"}, "last_changed": "2025-08-29T15:04:28.011716+00:00", "last_reported": "2025-08-29T15:09:40.689101+00:00", "last_updated": "2025-08-29T15:04:28.011716+00:00", "context": {"id": "01K3V5QK3B6B06MKZ441B5Y4HA", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "device_tracker.karls_iphone_16_pro", "state": "home", "attributes": {"source_type": "gps", "battery_level": 90, "latitude": 25.12800568419237, "longitude": 121.79796554808982, "gps_accuracy": 3, "altitude": 56.03304748339892, "vertical_accuracy": 30, "friendly_name": "<PERSON><PERSON>s iPhone 16 Pro"}, "last_changed": "2025-08-29T15:09:40.395034+00:00", "last_reported": "2025-08-29T15:09:40.395034+00:00", "last_updated": "2025-08-29T15:09:40.395034+00:00", "context": {"id": "01K3V6145B74VBXTMQ05SM3BM1", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_activity", "state": "Stationary", "attributes": {"Confidence": "High", "Types": ["Stationary"], "icon": "mdi:human-male", "friendly_name": "Karl’s iPhone 16 Pro Activity"}, "last_changed": "2025-08-29T15:04:28.012383+00:00", "last_reported": "2025-08-29T15:09:40.687124+00:00", "last_updated": "2025-08-29T15:04:28.012383+00:00", "context": {"id": "01K3V5QK3C6M0ZHC8G1WDPK5XR", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "Stationary", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_floors_ascended", "state": "15", "attributes": {"unit_of_measurement": "floors", "icon": "mdi:stairs-up", "friendly_name": "<PERSON><PERSON>s iPhone 16 Pro Floors Ascended"}, "last_changed": "2025-08-29T15:04:28.012456+00:00", "last_reported": "2025-08-29T15:09:40.687449+00:00", "last_updated": "2025-08-29T15:04:28.012456+00:00", "context": {"id": "01K3V5QK3CTCVN1789J0ARMCC8", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": 15, "native_unit_of_measurement": "floors"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_distance", "state": "2831", "attributes": {"unit_of_measurement": "m", "icon": "mdi:hiking", "friendly_name": "<PERSON><PERSON>s iPhone 16 Pro Distance"}, "last_changed": "2025-08-29T15:04:28.012508+00:00", "last_reported": "2025-08-29T15:09:40.687308+00:00", "last_updated": "2025-08-29T15:04:28.012508+00:00", "context": {"id": "01K3V5QK3CHR3QZDGZYE5BBKK4", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": 2831, "native_unit_of_measurement": "m"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_floors_descended", "state": "18", "attributes": {"unit_of_measurement": "floors", "icon": "mdi:stairs-down", "friendly_name": "<PERSON><PERSON>s iPhone 16 Pro Floors Descended"}, "last_changed": "2025-08-29T15:04:28.012557+00:00", "last_reported": "2025-08-29T15:09:40.687615+00:00", "last_updated": "2025-08-29T15:04:28.012557+00:00", "context": {"id": "01K3V5QK3C7QD34WCCC25VCTPX", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": 18, "native_unit_of_measurement": "floors"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_battery_level", "state": "90", "attributes": {"unit_of_measurement": "%", "device_class": "battery", "icon": "mdi:battery-charging-80", "friendly_name": "<PERSON><PERSON>s iPhone 16 Pro Battery Level"}, "last_changed": "2025-08-29T15:04:28.012609+00:00", "last_reported": "2025-08-29T15:09:40.688022+00:00", "last_updated": "2025-08-29T15:04:28.012609+00:00", "context": {"id": "01K3V5QK3C602KMRMJZ6B4FGR9", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": 90, "native_unit_of_measurement": "%"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_steps", "state": "4629", "attributes": {"unit_of_measurement": "steps", "icon": "mdi:walk", "friendly_name": "<PERSON>’s iPhone 16 Pro Steps"}, "last_changed": "2025-08-29T15:04:28.012654+00:00", "last_reported": "2025-08-29T15:09:40.687790+00:00", "last_updated": "2025-08-29T15:04:28.012654+00:00", "context": {"id": "01K3V5QK3CEXNHF6EHEJDGZNN5", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": 4629, "native_unit_of_measurement": "steps"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_average_active_pace", "state": "1", "attributes": {"unit_of_measurement": "m/s", "icon": "mdi:speedometer", "friendly_name": "<PERSON>’s iPhone 16 Pro Average Active Pace"}, "last_changed": "2025-08-29T15:04:28.012694+00:00", "last_reported": "2025-08-29T15:09:40.687891+00:00", "last_updated": "2025-08-29T15:04:28.012694+00:00", "context": {"id": "01K3V5QK3CW96Y8H67F7N0ASYW", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": 1, "native_unit_of_measurement": "m/s"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_battery_state", "state": "Charging", "attributes": {"Low Power Mode": false, "icon": "mdi:battery-charging-80", "friendly_name": "<PERSON>’s iPhone 16 Pro Battery State"}, "last_changed": "2025-08-29T15:04:28.012745+00:00", "last_reported": "2025-08-29T15:09:40.688155+00:00", "last_updated": "2025-08-29T15:04:28.012745+00:00", "context": {"id": "01K3V5QK3CPNFH2TGFWJHG32DT", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "Charging", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_storage", "state": "6.60", "attributes": {"Available": "9.69 GB", "Available (Important)": "49.52 GB", "Available (Opportunistic)": "8.41 GB", "Total": "127.42 GB", "unit_of_measurement": "% available", "icon": "mdi:database", "friendly_name": "<PERSON><PERSON>s iPhone 16 Pro Storage"}, "last_changed": "2025-08-29T15:09:40.688298+00:00", "last_reported": "2025-08-29T15:09:40.688298+00:00", "last_updated": "2025-08-29T15:09:40.688298+00:00", "context": {"id": "01K3V614EGJA6WSKD1SW99BWVF", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "6.60", "native_unit_of_measurement": "% available"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_ssid", "state": "PKDKLF", "attributes": {"icon": "mdi:wifi", "friendly_name": "<PERSON><PERSON>s iPhone 16 Pro SSID"}, "last_changed": "2025-08-29T15:04:28.012856+00:00", "last_reported": "2025-08-29T15:09:40.688448+00:00", "last_updated": "2025-08-29T15:04:28.012856+00:00", "context": {"id": "01K3V5QK3CBV5VGY1ZFVWPHCT2", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "PKDKLF", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_bssid", "state": "72:a7:41:cc:9e:f7", "attributes": {"icon": "mdi:wifi-star", "friendly_name": "<PERSON><PERSON>s iPhone 16 Pro BSSID"}, "last_changed": "2025-08-29T15:04:28.012899+00:00", "last_reported": "2025-08-29T15:09:40.688521+00:00", "last_updated": "2025-08-29T15:04:28.012899+00:00", "context": {"id": "01K3V5QK3CXZ86C1T8S059YQJ1", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "72:a7:41:cc:9e:f7", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_connection_type", "state": "Wi-Fi", "attributes": {"icon": "mdi:wifi", "friendly_name": "<PERSON><PERSON>s iPhone 16 Pro Connection Type"}, "last_changed": "2025-08-29T15:04:28.012937+00:00", "last_reported": "2025-08-29T15:09:40.688588+00:00", "last_updated": "2025-08-29T15:04:28.012937+00:00", "context": {"id": "01K3V5QK3C63Y6157A9TBWBDEZ", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "Wi-Fi", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_sim_1", "state": "--", "attributes": {"Allows VoIP": true, "Carrier ID": "0000000100000001", "Carrier Name": "--", "Current Radio Technology": "Long-Term Evolution (LTE)", "ISO Country Code": "--", "Mobile Country Code": "65535", "Mobile Network Code": "65535", "icon": "mdi:sim", "friendly_name": "<PERSON>’s iPhone 16 Pro SIM 1"}, "last_changed": "2025-08-29T15:04:28.012972+00:00", "last_reported": "2025-08-29T15:09:40.688801+00:00", "last_updated": "2025-08-29T15:04:28.012972+00:00", "context": {"id": "01K3V5QK3CNCMH2X289W003H84", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "--", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_sim_2", "state": "--", "attributes": {"Allows VoIP": true, "Carrier ID": "0000000100000002", "Carrier Name": "--", "ISO Country Code": "--", "Mobile Country Code": "65535", "Mobile Network Code": "65535", "icon": "mdi:sim", "friendly_name": "<PERSON><PERSON>s iPhone 16 Pro SIM 2"}, "last_changed": "2025-08-29T15:04:28.013010+00:00", "last_reported": "2025-08-29T15:09:40.688702+00:00", "last_updated": "2025-08-29T15:04:28.013010+00:00", "context": {"id": "01K3V5QK3D88QN4E4SQ38PWVXD", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "--", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_geocoded_location", "state": "台灣\n202012 基隆市 中正區\n調和街266巷23弄1–3號", "attributes": {"Administrative Area": "基隆市", "Areas Of Interest": ["臺灣島"], "Country": "台灣", "Inland Water": "N/A", "ISO Country Code": "TW", "Locality": "中正區", "Location": [25.12800568419237, 121.79796554808982], "Name": "調和街266巷23弄1–3號", "Ocean": "N/A", "Postal Code": "202012", "Sub Administrative Area": "基隆市", "Sub Locality": "砂子里", "Sub Thoroughfare": "1–3", "Thoroughfare": "調和街266巷23弄", "Time Zone": "Asia/Taipei", "Zones": ["PKDKLF"], "icon": "mdi:map", "friendly_name": "<PERSON>’s iPhone 16 Pro Geocoded Location"}, "last_changed": "2025-08-29T15:04:28.013047+00:00", "last_reported": "2025-08-29T15:09:40.688893+00:00", "last_updated": "2025-08-29T15:09:40.688893+00:00", "context": {"id": "01K3V614EGF6HYKSYCGYJE1M8K", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "台灣\n202012 基隆市 中正區\n調和街266巷23弄1–3號", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_last_update_trigger", "state": "Background Fetch", "attributes": {"icon": "mdi:cellphone-wireless", "friendly_name": "<PERSON><PERSON>s iPhone 16 Pro Last Update Trigger"}, "last_changed": "2025-08-29T15:09:40.689291+00:00", "last_reported": "2025-08-29T15:09:40.689291+00:00", "last_updated": "2025-08-29T15:09:40.689291+00:00", "context": {"id": "01K3V614EHH9SHDN8RKN7AJHPR", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "Background Fetch", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_app_version", "state": "2025.7.2", "attributes": {"icon": "mdi:cellphone", "friendly_name": "<PERSON><PERSON>s iPhone 16 Pro App Version"}, "last_changed": "2025-08-29T15:04:28.013119+00:00", "last_reported": "2025-08-29T15:09:40.689427+00:00", "last_updated": "2025-08-29T15:04:28.013119+00:00", "context": {"id": "01K3V5QK3DC0SC2P3Z2PBPT0J7", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "2025.7.2", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_location_permission", "state": "Authorized Always", "attributes": {"icon": "mdi:map", "friendly_name": "<PERSON>’s iPhone 16 Pro Location permission"}, "last_changed": "2025-08-29T15:04:28.013156+00:00", "last_reported": "2025-08-29T15:09:40.689512+00:00", "last_updated": "2025-08-29T15:04:28.013156+00:00", "context": {"id": "01K3V5QK3DR3RM8XBR1P875R78", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "Authorized Always", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_audio_output", "state": "Built-in Speaker", "attributes": {"icon": "mdi:volume-high", "friendly_name": "<PERSON><PERSON>s iPhone 16 Pro Audio Output"}, "last_changed": "2025-08-29T15:04:28.013194+00:00", "last_reported": "2025-08-29T15:09:40.691629+00:00", "last_updated": "2025-08-29T15:04:28.013194+00:00", "context": {"id": "01K3V5QK3DWMW237FF2Y0D0YK1", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "Built-in Speaker", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "device_tracker.phibz_iphone15", "state": "home", "attributes": {"source_type": "gps", "battery_level": 70, "latitude": 25.128284539486707, "longitude": 121.7974940422, "gps_accuracy": 19, "altitude": 58.55434554242834, "vertical_accuracy": 30, "friendly_name": "Phibz iPhone15"}, "last_changed": "2025-08-29T15:04:28.013419+00:00", "last_reported": "2025-08-29T15:04:28.013419+00:00", "last_updated": "2025-08-29T15:04:28.013419+00:00", "context": {"id": "01K3V5QK3DPT6971FA6SMWCBEX", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.phibz_iphone15_app_version", "state": "2025.7.2", "attributes": {"icon": "mdi:cellphone", "friendly_name": "Phibz iPhone15 App Version"}, "last_changed": "2025-08-29T15:04:28.013575+00:00", "last_reported": "2025-08-29T15:04:28.013575+00:00", "last_updated": "2025-08-29T15:04:28.013575+00:00", "context": {"id": "01K3V5QK3D0KD0NP2F8Y9S2S8D", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "2025.7.2", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.phibz_iphone15_ssid", "state": "Not Connected", "attributes": {"icon": "mdi:wifi-off", "friendly_name": "Phibz iPhone15 SSID"}, "last_changed": "2025-08-29T15:04:28.013641+00:00", "last_reported": "2025-08-29T15:04:28.013641+00:00", "last_updated": "2025-08-29T15:04:28.013641+00:00", "context": {"id": "01K3V5QK3DYY9F2Z63JEGS71A7", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "Not Connected", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.phibz_iphone15_bssid", "state": "Not Connected", "attributes": {"icon": "mdi:wifi-off", "friendly_name": "Phibz iPhone15 BSSID"}, "last_changed": "2025-08-29T15:04:28.013703+00:00", "last_reported": "2025-08-29T15:04:28.013703+00:00", "last_updated": "2025-08-29T15:04:28.013703+00:00", "context": {"id": "01K3V5QK3DGWYA0D0TN6KNE771", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "Not Connected", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.phibz_iphone15_battery_level", "state": "70", "attributes": {"unit_of_measurement": "%", "device_class": "battery", "icon": "mdi:battery-70", "friendly_name": "Phibz iPhone15 Battery Level"}, "last_changed": "2025-08-29T15:04:28.013776+00:00", "last_reported": "2025-08-29T15:04:28.013776+00:00", "last_updated": "2025-08-29T15:04:28.013776+00:00", "context": {"id": "01K3V5QK3DYYEQRRNWC2SQHZX3", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": 70, "native_unit_of_measurement": "%"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.phibz_iphone15_location_permission", "state": "Authorized Always", "attributes": {"icon": "mdi:map", "friendly_name": "Phibz iPhone15 Location permission"}, "last_changed": "2025-08-29T15:04:28.013825+00:00", "last_reported": "2025-08-29T15:04:28.013825+00:00", "last_updated": "2025-08-29T15:04:28.013825+00:00", "context": {"id": "01K3V5QK3DPV3CGH28H6D3D10J", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "Authorized Always", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.phibz_iphone15_audio_output", "state": "unavailable", "attributes": {"icon": "mdi:dots-square", "friendly_name": "Phibz iPhone15 Audio Output"}, "last_changed": "2025-08-29T15:04:28.013873+00:00", "last_reported": "2025-08-29T15:04:28.013873+00:00", "last_updated": "2025-08-29T15:04:28.013873+00:00", "context": {"id": "01K3V5QK3DRNQDPG4D6DDEJH8R", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "unavailable", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.phibz_iphone15_sim_2", "state": "unavailable", "attributes": {"icon": "mdi:dots-square", "friendly_name": "Phibz iPhone15 SIM 2"}, "last_changed": "2025-08-29T15:04:28.013915+00:00", "last_reported": "2025-08-29T15:04:28.013915+00:00", "last_updated": "2025-08-29T15:04:28.013915+00:00", "context": {"id": "01K3V5QK3DJG1X3E701HKV9C8D", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "unavailable", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.phibz_iphone15_sim_1", "state": "unavailable", "attributes": {"icon": "mdi:dots-square", "friendly_name": "Phibz iPhone15 SIM 1"}, "last_changed": "2025-08-29T15:04:28.013960+00:00", "last_reported": "2025-08-29T15:04:28.013960+00:00", "last_updated": "2025-08-29T15:04:28.013960+00:00", "context": {"id": "01K3V5QK3DVG051QT1HV0KFRNW", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "unavailable", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.phibz_iphone15_storage", "state": "unavailable", "attributes": {"unit_of_measurement": "% available", "icon": "mdi:dots-square", "friendly_name": "Phibz iPhone15 Storage"}, "last_changed": "2025-08-29T15:04:28.013994+00:00", "last_reported": "2025-08-29T15:04:28.013994+00:00", "last_updated": "2025-08-29T15:04:28.013994+00:00", "context": {"id": "01K3V5QK3DKHNFA3190QC3QPKH", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "unavailable", "native_unit_of_measurement": "% available"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.phibz_iphone15_geocoded_location", "state": "Taiwan\n202012 Keelung City Zhongzheng District\nNo. 16 調和街266巷57弄", "attributes": {"Administrative Area": "Keelung City", "Areas Of Interest": ["Taiwan"], "Country": "Taiwan", "Inland Water": "N/A", "ISO Country Code": "TW", "Locality": "Zhongzheng District", "Location": [25.128284539486707, 121.7974940422], "Name": "No. 16 調和街266巷57弄", "Ocean": "N/A", "Postal Code": "202012", "Sub Administrative Area": "Keelung City", "Sub Locality": "<PERSON><PERSON><PERSON>", "Sub Thoroughfare": "No. 16", "Thoroughfare": "調和街266巷57弄", "Time Zone": "Asia/Taipei", "Zones": ["PKDKLF"], "icon": "mdi:map", "friendly_name": "Phibz iPhone15 Geocoded Location"}, "last_changed": "2025-08-29T15:04:28.014031+00:00", "last_reported": "2025-08-29T15:04:28.014031+00:00", "last_updated": "2025-08-29T15:04:28.014031+00:00", "context": {"id": "01K3V5QK3EBT8GR2TK7R2Q25EX", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "Taiwan\n202012 Keelung City Zhongzheng District\nNo. 16 調和街266巷57弄", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.phibz_iphone15_connection_type", "state": "unavailable", "attributes": {"icon": "mdi:dots-square", "friendly_name": "Phibz iPhone15 Connection Type"}, "last_changed": "2025-08-29T15:04:28.014071+00:00", "last_reported": "2025-08-29T15:04:28.014071+00:00", "last_updated": "2025-08-29T15:04:28.014071+00:00", "context": {"id": "01K3V5QK3EPDQ9Q9RZSE0BWWTD", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "unavailable", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.phibz_iphone15_battery_state", "state": "Not Charging", "attributes": {"Low Power Mode": false, "icon": "mdi:battery-70", "friendly_name": "Phibz iPhone15 Battery State"}, "last_changed": "2025-08-29T15:04:28.014108+00:00", "last_reported": "2025-08-29T15:04:28.014108+00:00", "last_updated": "2025-08-29T15:04:28.014108+00:00", "context": {"id": "01K3V5QK3EB7EY2FMSM5T7TEJN", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "Not Charging", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.phibz_iphone15_last_update_trigger", "state": "unavailable", "attributes": {"icon": "mdi:dots-square", "friendly_name": "Phibz iPhone15 Last Update Trigger"}, "last_changed": "2025-08-29T15:04:28.014142+00:00", "last_reported": "2025-08-29T15:04:28.014142+00:00", "last_updated": "2025-08-29T15:04:28.014142+00:00", "context": {"id": "01K3V5QK3ED5APG42TSMCBCKP1", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "unavailable", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "binary_sensor.ipad_mini_focus", "state": "off", "attributes": {"icon": "mdi:dots-square", "friendly_name": "iPad mini Focus"}, "last_changed": "2025-08-29T15:04:28.014303+00:00", "last_reported": "2025-08-29T15:04:28.014303+00:00", "last_updated": "2025-08-29T15:04:28.014303+00:00", "context": {"id": "01K3V5QK3EZH7YKS4VH0AWBGGB", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "device_tracker.ipad_mini", "state": "home", "attributes": {"source_type": "gps", "battery_level": 100, "latitude": 25.127997676562536, "longitude": 121.79795142624778, "gps_accuracy": 19, "altitude": 55.87594032287598, "vertical_accuracy": 30, "friendly_name": "iPad mini"}, "last_changed": "2025-08-29T15:04:28.014401+00:00", "last_reported": "2025-08-29T15:04:28.014401+00:00", "last_updated": "2025-08-29T15:04:28.014401+00:00", "context": {"id": "01K3V5QK3EZP9VZZE96R1S7JF7", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.ipad_mini_activity", "state": "unavailable", "attributes": {"icon": "mdi:dots-square", "friendly_name": "iPad mini Activity"}, "last_changed": "2025-08-29T15:04:28.014535+00:00", "last_reported": "2025-08-29T15:04:28.014535+00:00", "last_updated": "2025-08-29T15:04:28.014535+00:00", "context": {"id": "01K3V5QK3E6CY397KPWWFXK9N0", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "unavailable", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.ipad_mini_ssid", "state": "unavailable", "attributes": {"icon": "mdi:dots-square", "friendly_name": "iPad mini SSID"}, "last_changed": "2025-08-29T15:04:28.014594+00:00", "last_reported": "2025-08-29T15:04:28.014594+00:00", "last_updated": "2025-08-29T15:04:28.014594+00:00", "context": {"id": "01K3V5QK3E1ZZH08Y6N7RW4761", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "unavailable", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.ipad_mini_bssid", "state": "unavailable", "attributes": {"icon": "mdi:dots-square", "friendly_name": "iPad mini BSSID"}, "last_changed": "2025-08-29T15:04:28.014632+00:00", "last_reported": "2025-08-29T15:04:28.014632+00:00", "last_updated": "2025-08-29T15:04:28.014632+00:00", "context": {"id": "01K3V5QK3EHZWWJ8AMS1NFBA94", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "unavailable", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.ipad_mini_connection_type", "state": "unavailable", "attributes": {"icon": "mdi:dots-square", "friendly_name": "iPad mini Connection Type"}, "last_changed": "2025-08-29T15:04:28.014670+00:00", "last_reported": "2025-08-29T15:04:28.014670+00:00", "last_updated": "2025-08-29T15:04:28.014670+00:00", "context": {"id": "01K3V5QK3ERK4MZ3H1FBYGR8MY", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "unavailable", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.ipad_mini_storage", "state": "unavailable", "attributes": {"unit_of_measurement": "% available", "icon": "mdi:dots-square", "friendly_name": "iPad mini Storage"}, "last_changed": "2025-08-29T15:04:28.014709+00:00", "last_reported": "2025-08-29T15:04:28.014709+00:00", "last_updated": "2025-08-29T15:04:28.014709+00:00", "context": {"id": "01K3V5QK3ED8XHXFWTK0Y77S20", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "unavailable", "native_unit_of_measurement": "% available"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.ipad_mini_battery_level", "state": "100", "attributes": {"unit_of_measurement": "%", "device_class": "battery", "icon": "mdi:battery", "friendly_name": "iPad mini Battery Level"}, "last_changed": "2025-08-29T15:04:28.014771+00:00", "last_reported": "2025-08-29T15:04:28.014771+00:00", "last_updated": "2025-08-29T15:04:28.014771+00:00", "context": {"id": "01K3V5QK3EEKP09MMD394ZCR8R", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": 100, "native_unit_of_measurement": "%"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.ipad_mini_battery_state", "state": "Full", "attributes": {"Low Power Mode": false, "icon": "mdi:battery", "friendly_name": "iPad mini Battery State"}, "last_changed": "2025-08-29T15:04:28.014810+00:00", "last_reported": "2025-08-29T15:04:28.014810+00:00", "last_updated": "2025-08-29T15:04:28.014810+00:00", "context": {"id": "01K3V5QK3E0B4RPNV3EP8202BS", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "Full", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.ipad_mini_geocoded_location", "state": "unavailable", "attributes": {"icon": "mdi:dots-square", "friendly_name": "iPad mini Geocoded Location"}, "last_changed": "2025-08-29T15:04:28.014844+00:00", "last_reported": "2025-08-29T15:04:28.014844+00:00", "last_updated": "2025-08-29T15:04:28.014844+00:00", "context": {"id": "01K3V5QK3EGK4DPDD452JRRRQD", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "unavailable", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.ipad_mini_last_update_trigger", "state": "unavailable", "attributes": {"icon": "mdi:dots-square", "friendly_name": "iPad mini Last Update Trigger"}, "last_changed": "2025-08-29T15:04:28.014879+00:00", "last_reported": "2025-08-29T15:04:28.014879+00:00", "last_updated": "2025-08-29T15:04:28.014879+00:00", "context": {"id": "01K3V5QK3E7A2MBDXPG3H3Z9ET", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "unavailable", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.ipad_mini_app_version", "state": "2025.7.2", "attributes": {"icon": "mdi:cellphone", "friendly_name": "iPad mini App Version"}, "last_changed": "2025-08-29T15:04:28.014922+00:00", "last_reported": "2025-08-29T15:04:28.014922+00:00", "last_updated": "2025-08-29T15:04:28.014922+00:00", "context": {"id": "01K3V5QK3EF7865VY30S88QTGW", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "2025.7.2", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.ipad_mini_audio_output", "state": "unavailable", "attributes": {"icon": "mdi:dots-square", "friendly_name": "iPad mini Audio Output"}, "last_changed": "2025-08-29T15:04:28.014970+00:00", "last_reported": "2025-08-29T15:04:28.014970+00:00", "last_updated": "2025-08-29T15:04:28.014970+00:00", "context": {"id": "01K3V5QK3ECKMVQ07A40REKVD0", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "unavailable", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.ipad_mini_location_permission", "state": "Authorized Always", "attributes": {"icon": "mdi:map", "friendly_name": "iPad mini Location permission"}, "last_changed": "2025-08-29T15:04:28.015005+00:00", "last_reported": "2025-08-29T15:04:28.015005+00:00", "last_updated": "2025-08-29T15:04:28.015005+00:00", "context": {"id": "01K3V5QK3F71TWXD0Y9RY0WAYA", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "Authorized Always", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "update.tesla_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "v3.25.2", "in_progress": false, "latest_version": "v3.25.2", "release_summary": null, "release_url": "https://github.com/alandtse/tesla/releases/v3.25.2", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/tesla_custom/icon.png", "friendly_name": "Tesla update", "supported_features": 23}, "last_changed": "2025-08-29T15:04:28.143089+00:00", "last_reported": "2025-08-29T15:04:28.143089+00:00", "last_updated": "2025-08-29T15:04:28.143089+00:00", "context": {"id": "01K3V5QK7F0Z07MV9DD2V181MC", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "update.samsungtv_smart_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "v0.14.4", "in_progress": false, "latest_version": "v0.14.4", "release_summary": null, "release_url": "https://github.com/ollo69/ha-samsungtv-smart/releases/v0.14.4", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/samsungtv_smart/icon.png", "friendly_name": "SamsungTV Smart update", "supported_features": 23}, "last_changed": "2025-08-29T15:04:28.143212+00:00", "last_reported": "2025-08-29T15:04:28.143212+00:00", "last_updated": "2025-08-29T15:04:28.143212+00:00", "context": {"id": "01K3V5QK7FBEJ58P8Q5DTBHE2G", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "update.dyson_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "v1.7.0", "in_progress": false, "latest_version": "v1.7.0", "release_summary": null, "release_url": "https://github.com/libdyson-wg/ha-dyson/releases/v1.7.0", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/dyson_local/icon.png", "friendly_name": "Dyson update", "supported_features": 23}, "last_changed": "2025-08-29T15:04:28.143277+00:00", "last_reported": "2025-08-29T15:04:28.143277+00:00", "last_updated": "2025-08-29T15:04:28.143277+00:00", "context": {"id": "01K3V5QK7F38EMN9PQAQEHB480", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "update.mushroom_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "v4.5.0", "in_progress": false, "latest_version": "v4.5.0", "release_summary": null, "release_url": "https://github.com/piitaya/lovelace-mushroom/releases/v4.5.0", "skipped_version": null, "title": null, "update_percentage": null, "friendly_name": "Mushroom update", "supported_features": 23}, "last_changed": "2025-08-29T15:04:28.143347+00:00", "last_reported": "2025-08-29T15:04:28.143347+00:00", "last_updated": "2025-08-29T15:04:28.143347+00:00", "context": {"id": "01K3V5QK7FGT3R223GFTKKEJ2X", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "update.kiosk_mode_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "v7.2.0", "in_progress": false, "latest_version": "v7.2.0", "release_summary": null, "release_url": "https://github.com/NemesisRE/kiosk-mode/releases/v7.2.0", "skipped_version": null, "title": null, "update_percentage": null, "friendly_name": "Kiosk Mode update", "supported_features": 23}, "last_changed": "2025-08-29T15:04:28.143402+00:00", "last_reported": "2025-08-29T15:04:28.143402+00:00", "last_updated": "2025-08-29T15:04:28.143402+00:00", "context": {"id": "01K3V5QK7FZ7BCVA4B9BCCHSD7", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "update.xiaomi_gateway_3_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "v4.1.2", "in_progress": false, "latest_version": "v4.1.2", "release_summary": null, "release_url": "https://github.com/AlexxIT/XiaomiGateway3/releases/v4.1.2", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/xiaomi_gateway3/icon.png", "friendly_name": "Xiaomi Gateway 3 update", "supported_features": 23}, "last_changed": "2025-08-29T15:04:28.143461+00:00", "last_reported": "2025-08-29T15:04:28.143461+00:00", "last_updated": "2025-08-29T15:04:28.143461+00:00", "context": {"id": "01K3V5QK7F0E4GGTKSANZ4QBP7", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "update.sonos_cloud_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "0.3.5", "in_progress": false, "latest_version": "0.3.5", "release_summary": null, "release_url": "https://github.com/jjlawren/sonos_cloud/releases/0.3.5", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/sonos_cloud/icon.png", "friendly_name": "Sonos Cloud update", "supported_features": 23}, "last_changed": "2025-08-29T15:04:28.143502+00:00", "last_reported": "2025-08-29T15:04:28.143502+00:00", "last_updated": "2025-08-29T15:04:28.143502+00:00", "context": {"id": "01K3V5QK7FM4GJF118Z304P7E9", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "update.electrolux_wellbeing_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "v1.3.9", "in_progress": false, "latest_version": "v1.3.9", "release_summary": null, "release_url": "https://github.com/JohNan/homeassistant-wellbeing/releases/v1.3.9", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/wellbeing/icon.png", "friendly_name": "Electrolux Wellbeing update", "supported_features": 23}, "last_changed": "2025-08-29T15:04:28.143544+00:00", "last_reported": "2025-08-29T15:04:28.143544+00:00", "last_updated": "2025-08-29T15:04:28.143544+00:00", "context": {"id": "01K3V5QK7FYY2VAH2HS4VWHQSC", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "update.panasonic_smart_app_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "v2.12.0", "in_progress": false, "latest_version": "v2.12.0", "release_summary": null, "release_url": "https://github.com/osk2/panasonic_smart_app/releases/v2.12.0", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/panasonic_smart_app/icon.png", "friendly_name": "Panasonic Smart App update", "supported_features": 23}, "last_changed": "2025-08-29T15:04:28.143588+00:00", "last_reported": "2025-08-29T15:04:28.143588+00:00", "last_updated": "2025-08-29T15:04:28.143588+00:00", "context": {"id": "01K3V5QK7FQ8JC3SZZNC9APDR7", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "update.tapo_controller_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "3.2.1", "in_progress": false, "latest_version": "3.2.1", "release_summary": null, "release_url": "https://github.com/petretiandrea/home-assistant-tapo-p100/releases/3.2.1", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/tapo/icon.png", "friendly_name": "Tapo Controller update", "supported_features": 23}, "last_changed": "2025-08-29T15:04:28.143629+00:00", "last_reported": "2025-08-29T15:04:28.143629+00:00", "last_updated": "2025-08-29T15:04:28.143629+00:00", "context": {"id": "01K3V5QK7F3XDNRFSZDVD4C318", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "update.adaptive_lighting_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "v1.26.0", "in_progress": false, "latest_version": "v1.26.0", "release_summary": null, "release_url": "https://github.com/basnijholt/adaptive-lighting/releases/v1.26.0", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/adaptive_lighting/icon.png", "friendly_name": "Adaptive Lighting update", "supported_features": 23}, "last_changed": "2025-08-29T15:04:28.143664+00:00", "last_reported": "2025-08-29T15:04:28.143664+00:00", "last_updated": "2025-08-29T15:04:28.143664+00:00", "context": {"id": "01K3V5QK7FJKW1YC05RXPTBSQ8", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "update.smartthinq_lge_sensors_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "v0.41.1", "in_progress": false, "latest_version": "v0.41.1", "release_summary": null, "release_url": "https://github.com/ollo69/ha-smartthinq-sensors/releases/v0.41.1", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/smartthinq_sensors/icon.png", "friendly_name": "SmartThinQ LGE Sensors update", "supported_features": 23}, "last_changed": "2025-08-29T15:04:28.143700+00:00", "last_reported": "2025-08-29T15:04:28.143700+00:00", "last_updated": "2025-08-29T15:04:28.143700+00:00", "context": {"id": "01K3V5QK7FZ359EYGNH1JS4Z7W", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "update.tapo_cameras_control_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "7.0.0", "in_progress": false, "latest_version": "7.0.0", "release_summary": null, "release_url": "https://github.com/JurajNyiri/HomeAssistant-Tapo-Control/releases/7.0.0", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/tapo_control/icon.png", "friendly_name": "Tapo: Cameras Control update", "supported_features": 23}, "last_changed": "2025-08-29T15:04:28.143755+00:00", "last_reported": "2025-08-29T15:04:28.143755+00:00", "last_updated": "2025-08-29T15:04:28.143755+00:00", "context": {"id": "01K3V5QK7FQYB7796Z02R2KW48", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "update.tuya_local_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "2025.8.0", "in_progress": false, "latest_version": "2025.8.0", "release_summary": null, "release_url": "https://github.com/make-all/tuya-local/releases/2025.8.0", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/tuya_local/icon.png", "friendly_name": "Tuya Local update", "supported_features": 23}, "last_changed": "2025-08-29T15:04:28.143792+00:00", "last_reported": "2025-08-29T15:04:28.143792+00:00", "last_updated": "2025-08-29T15:04:28.143792+00:00", "context": {"id": "01K3V5QK7F1DGQQQNWNNPSA739", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "update.bubble_card_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "v3.0.3", "in_progress": false, "latest_version": "v3.0.3", "release_summary": null, "release_url": "https://github.com/Clooos/Bubble-Card/releases/v3.0.3", "skipped_version": null, "title": null, "update_percentage": null, "friendly_name": "Bubble Card update", "supported_features": 23}, "last_changed": "2025-08-29T15:04:28.143830+00:00", "last_reported": "2025-08-29T15:04:28.143830+00:00", "last_updated": "2025-08-29T15:04:28.143830+00:00", "context": {"id": "01K3V5QK7FCM24C3SNRBGWAYXR", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "update.bambu_lab_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "v2.1.27", "in_progress": false, "latest_version": "v2.1.27", "release_summary": null, "release_url": "https://github.com/greghesp/ha-bambulab/releases/v2.1.27", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/bambu_lab/icon.png", "friendly_name": "Bambu Lab update", "supported_features": 23}, "last_changed": "2025-08-29T15:04:28.143863+00:00", "last_reported": "2025-08-29T15:04:28.143863+00:00", "last_updated": "2025-08-29T15:04:28.143863+00:00", "context": {"id": "01K3V5QK7F1R3YFV5MATJ8M1EF", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "update.webrtc_camera_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "v3.6.1", "in_progress": false, "latest_version": "v3.6.1", "release_summary": null, "release_url": "https://github.com/AlexxIT/WebRTC/releases/v3.6.1", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/webrtc/icon.png", "friendly_name": "WebRTC Camera update", "supported_features": 23}, "last_changed": "2025-08-29T15:04:28.143894+00:00", "last_reported": "2025-08-29T15:04:28.143894+00:00", "last_updated": "2025-08-29T15:04:28.143894+00:00", "context": {"id": "01K3V5QK7F5NT8FQFD6BZ0WPXW", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "update.aqara_gateway_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "0.3.2", "in_progress": false, "latest_version": "0.3.2", "release_summary": null, "release_url": "https://github.com/niceboygithub/AqaraGateway/releases/0.3.2", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/aqara_gateway/icon.png", "friendly_name": "Aqara Gateway update", "supported_features": 23}, "last_changed": "2025-08-29T15:04:28.143934+00:00", "last_reported": "2025-08-29T15:04:28.143934+00:00", "last_updated": "2025-08-29T15:04:28.143934+00:00", "context": {"id": "01K3V5QK7FA0CJSDJZGTB03X3E", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "update.local_tuya_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "v5.2.4", "in_progress": false, "latest_version": "v5.2.4", "release_summary": null, "release_url": "https://github.com/rospogrigio/localtuya/releases/v5.2.4", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/localtuya/icon.png", "friendly_name": "Local Tuya update", "supported_features": 23}, "last_changed": "2025-08-29T15:04:28.143986+00:00", "last_reported": "2025-08-29T15:04:28.143986+00:00", "last_updated": "2025-08-29T15:04:28.143986+00:00", "context": {"id": "01K3V5QK7FRDYC38N8FB4E8PVW", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "update.hacs_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "2.0.5", "in_progress": false, "latest_version": "2.0.5", "release_summary": null, "release_url": "https://github.com/hacs/integration/releases/2.0.5", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/hacs/icon.png", "friendly_name": "HACS update", "supported_features": 23}, "last_changed": "2025-08-29T15:04:28.144031+00:00", "last_reported": "2025-08-29T15:04:28.144031+00:00", "last_updated": "2025-08-29T15:04:28.144031+00:00", "context": {"id": "01K3V5QK7G293QEPEJB9G2P2MS", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.climate_h_91e0ec_safe_mode_boot", "state": "unknown", "attributes": {"device_class": "restart", "icon": "mdi:restart-alert", "friendly_name": "Climate-H 91e0ec Safe Mode Boot"}, "last_changed": "2025-08-29T15:04:40.807189+00:00", "last_reported": "2025-08-29T15:04:40.807189+00:00", "last_updated": "2025-08-29T15:04:40.807189+00:00", "context": {"id": "01K3V5QZK714XM4ZKP2BVQGGZN", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.climate_h_91e0ec_restart", "state": "unknown", "attributes": {"device_class": "restart", "icon": "mdi:restart", "friendly_name": "Climate-H 91e0ec Restart"}, "last_changed": "2025-08-29T15:04:40.806801+00:00", "last_reported": "2025-08-29T15:04:40.806801+00:00", "last_updated": "2025-08-29T15:04:40.806801+00:00", "context": {"id": "01K3V5QZK6K95V992QQF6BK3YG", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.climate_h_91e0ec_get_info", "state": "unknown", "attributes": {"icon": "mdi:button-pointer", "friendly_name": "Climate-H 91e0ec Get Info"}, "last_changed": "2025-08-29T15:04:40.807344+00:00", "last_reported": "2025-08-29T15:04:40.807344+00:00", "last_updated": "2025-08-29T15:04:40.807344+00:00", "context": {"id": "01K3V5QZK72BRDS3Q9NFA033WR", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "binary_sensor.mailbox", "state": "off", "attributes": {"icon": "mdi:mailbox", "friendly_name": "Mailbox"}, "last_changed": "2025-08-29T15:10:29.217811+00:00", "last_reported": "2025-08-29T15:10:29.217811+00:00", "last_updated": "2025-08-29T15:10:29.217811+00:00", "context": {"id": "01K3V62KV1TN205D3FG7WBTKW5", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.bubble_card_modules", "state": "saved", "attributes": {"modules": {"home-assistant-default": {"id": "home-assistant-default", "name": "Home Assistant default styling", "version": "v1.3", "creator": "C<PERSON><PERSON>", "link": "https://github.com/Clooos/Bubble-Card/discussions/1230", "description": "This module applies Home Assistant's default styling to Bubble Card. To set it as the default, move it under <code>default:</code> in the <code>bubble-modules.yaml</code> file.", "code": ":host {\n    --bubble-button-accent-color: rgba(0,140,255,0.3); /* Edit this color if needed */\n    --bubble-main-background-color: var(--ha-card-background, var(--card-background-color, #fff));\n    --bubble-border-radius: var(--ha-card-border-radius, 12px);\n    --bubble-icon-border-radius: 32px;\n    --bubble-button-border-radius: var(--bubble-border-radius);\n    --bubble-climate-button-background-color: var(--bubble-icon-background-color);\n    --bubble-border: var(--ha-card-border-width, 1px) solid var(--ha-card-border-color, var(--divider-color, #e0e0e0));\n    --bubble-secondary-background-color: transparent;\n}\n\n.bubble-container {\n    -webkit-backdrop-filter: var(--ha-card-backdrop-filter, none);\n    backdrop-filter: var(--ha-card-backdrop-filter, none);\n    box-shadow: var(--ha-card-box-shadow, none);\n    box-sizing: border-box;\n}\n\n.bubble-icon-container, \n.large .bubble-icon-container {\n    --mdc-icon-size: 22px;\n    min-width: 36px !important;\n    min-height: 36px !important;\n}\n\n.large .bubble-cover-card-container > .bubble-buttons {\n    --bubble-cover-main-background-color: none;\n}\n\n.bubble-range-fill {\n    --bubble-accent-color: var(--bubble-button-accent-color);\n}\n\n.bubble-sub-button.background-on::before,\n.bubble-sub-button.background-off::before,\n.bubble-temperature-container::before,\n.bubble-icon-container::before {\n    content: \"\";\n    position: absolute;\n    top: 0; \n    left: 0;\n    width: 100%; \n    height: 100%;  \n    opacity: var(--control-number-buttons-background-opacity, .2);\n    border-radius: var(--bubble-border-radius);\n    background: var(--control-number-buttons-background-color, var(--disabled-color));\n}\n\n.is-on {\n    --bubble-icon-background-color: var(--view-background,var(--lovelace-background,var(--primary-background-color)));\n    transition: all ease-in 0.3s !important;\n}\n\n.bubble-icon-container::before {\n    background: var(--state-inactive-color);\n    border-radius: var(--bubble-icon-border-radius);\n}\n\n.bubble-sub-button {\n    border: 0px solid transparent !important;\n}\n\n.no-icon-select-arrow {\n    right: 4px !important;\n}\n\n.bubble-select.bubble-wrapper {\n    margin: 0 -2px;\n}\n\n.large .bubble-icon-container {\n     margin-left: 9px;\n}\n\n.bubble-state {\n    opacity: 1;\n    font-weight: 400;\n    font-size: 12px;\n    letter-spacing: .4px;\n}\n\n:not(.bubble-separator) > .bubble-name {\n    font-weight: 500;\n    font-size: 14px;\n    letter-spacing: 0.1px;\n}\n\n.bubble-pop-up-background { \n    filter: brightness(0.96); /* Improve pop-up background contrast */\n    --bubble-pop-up-border-radius: calc(var(--ha-card-border-radius, 12px) * 1.4);\n}\n\n.bubble-header-container {\n    --bubble-secondary-background-color: var(--background-color-2); \n}\n\nha-select {\n    --bubble-list-item-accent-color: none !important;\n    --mdc-theme-surface: var(--card-background-color);\n}\n\nmwc-list-item[selected] {\n    color: inherit !important;\n    --mdc-ripple-press-opacity: 0 !important;\n}\n\nmwc-list-item[selected]::before {\n    content: \"\";\n    position: absolute;\n    top: 0; \n    left: 0;\n    width: 100%; \n    height: 100%;  \n    background-color: var(--primary-color);\n    opacity: 0.24;\n}\n"}, "get_state_attribute": {"id": "get_state_attribute", "name": "Advanced example: Get state/attribute from other entities", "version": "v1.3", "creator": "C<PERSON><PERSON>", "link": "https://github.com/Clooos/Bubble-Card/discussions/1232", "supported": ["pop-up", "cover", "button", "media-player", "climate", "select"], "description": "Get state/attribute from other entities and replace the default state/attribute field.\n<img class=\"example\" src=\"https://github.com/Clooos/Bubble-Card/blob/main/img/get_state_template_example.png?raw=true\" />\nConfigure this module via the editor or in YAML, for example:\n<br><br>\n<code-block><pre>\nget_state_attribute:\n    - entity: weather.home\n    - entity: sensor.weather_station\n      attribute: humidity\n    - entity: sensor.weather_station\n      attribute: temperature\n</pre></code-block>\n<br>\n<b>If it doesn't work, make sure at least one of \"Show state\" or \"Show attribute\" is turned on in your card configuration.</b>\n", "code": "${(() => {\n  // Retrieve the configuration or use an empty array by default\n  const config = this.config.get_state_attribute || [];\n\n  // Format the retrieved value from the entity for each entry\n  const values = config\n    .map(cfg => {\n      const entity = hass.states[cfg.entity];\n      if (entity) {\n        let rawValue;\n        if (cfg.attribute) {\n          rawValue = entity.attributes[cfg.attribute];\n          if (rawValue !== undefined && rawValue !== 'unknown' && rawValue !== 'unavailable' && rawValue !== 'null' && rawValue !== '') {\n            return hass.formatEntityAttributeValue(entity, cfg.attribute);\n          }\n        } else {\n          rawValue = entity.state;\n          if (rawValue !== undefined && rawValue !== 'unknown' && rawValue !== 'unavailable' && rawValue !== 'null' && rawValue !== '') {\n            return hass.formatEntityState(entity);\n          }\n        }\n      }\n      return null;\n    })\n    // Remove null values and empty strings or strings with only spaces\n    .filter(value => value !== null && value !== \"\" && value.trim() !== \"\");\n\n  // Update the DOM element with the class 'bubble-state'\n  // displaying values separated by ' • '\n  card.querySelector('.bubble-state').innerText = values.join(' • ');\n})()}\n", "editor": [{"type": "expandable", "title": "Select entities and attributes", "icon": "mdi:list-box-outline", "schema": [{"name": "0", "type": "expandable", "title": "Entity 1", "schema": [{"name": "entity", "label": "Entity", "selector": {"entity": {}}}, {"name": "attribute", "label": "Attribute", "selector": {"attribute": {}}}]}, {"name": "1", "type": "expandable", "title": "Entity 2", "schema": [{"name": "entity", "label": "Entity", "selector": {"entity": {}}}, {"name": "attribute", "label": "Attribute", "selector": {"attribute": {}}}]}, {"name": "2", "type": "expandable", "title": "Entity 3", "schema": [{"name": "entity", "label": "Entity", "selector": {"entity": {}}}, {"name": "attribute", "label": "Attribute", "selector": {"attribute": {}}}]}, {"name": "3", "type": "expandable", "title": "Entity 4", "schema": [{"name": "entity", "label": "Entity", "selector": {"entity": {}}}, {"name": "attribute", "label": "Attribute", "selector": {"attribute": {}}}]}]}]}}, "last_updated": "2025-04-29T15:17:07.819Z", "friendly_name": "Bubble Card Modules"}, "last_changed": "2025-08-29T15:04:40.284358+00:00", "last_reported": "2025-08-29T15:04:40.284358+00:00", "last_updated": "2025-08-29T15:04:40.284358+00:00", "context": {"id": "01K3V5QZ2WGVV46E3W38FP18JB", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "saved", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "automation.3f_zi_dong_lou_ti_deng", "state": "on", "attributes": {"id": "1740766955668", "last_triggered": "2025-08-29T15:33:50.162872+00:00", "mode": "restart", "current": 0, "friendly_name": "3F 自動樓梯燈"}, "last_changed": "2025-08-29T15:04:40.512853+00:00", "last_reported": "2025-08-29T15:35:16.452391+00:00", "last_updated": "2025-08-29T15:35:16.452391+00:00", "context": {"id": "01K3V7G0744N4KEEJE709RJ9ST", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "automation.ke_ting_kan_deng", "state": "on", "attributes": {"id": "1740984869662", "last_triggered": "2025-08-29T15:55:53.544607+00:00", "mode": "restart", "current": 0, "friendly_name": "客廳崁燈"}, "last_changed": "2025-08-29T15:04:40.513013+00:00", "last_reported": "2025-08-29T15:55:53.546203+00:00", "last_updated": "2025-08-29T15:55:53.546203+00:00", "context": {"id": "01K3V8NRA5RGYRQS1D9ZRVHT1C", "parent_id": "01K3V8NR9S488XG7MBBE1GT09F", "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "automation.ke_ting_diao_deng", "state": "on", "attributes": {"id": "1740984964318", "last_triggered": "2025-08-29T15:55:53.544469+00:00", "mode": "restart", "current": 0, "friendly_name": "客廳吊燈"}, "last_changed": "2025-08-29T15:04:40.513096+00:00", "last_reported": "2025-08-29T15:55:53.546185+00:00", "last_updated": "2025-08-29T15:55:53.546185+00:00", "context": {"id": "01K3V8NRA5RDX9WSJJ4G8M5T36", "parent_id": "01K3V8NR9S9CSHPD985E1SP6FF", "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "automation.qing_kong_deng", "state": "on", "attributes": {"id": "1740987782030", "last_triggered": "2025-08-29T15:55:53.541620+00:00", "mode": "restart", "current": 0, "friendly_name": "晴空燈"}, "last_changed": "2025-08-29T15:04:40.513146+00:00", "last_reported": "2025-08-29T15:55:53.558318+00:00", "last_updated": "2025-08-29T15:55:53.558318+00:00", "context": {"id": "01K3V8NRA5BEHNVRKFQ0Y8QC69", "parent_id": "01K3V8NR9SVW3RNVG5ZG3D5VMB", "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "automation.ri_luo_zi_dong_kai_lou_ti_bi_deng", "state": "on", "attributes": {"id": "1741240299234", "last_triggered": "2025-08-29T09:59:39.124957+00:00", "mode": "single", "current": 0, "friendly_name": "自動樓梯壁燈"}, "last_changed": "2025-08-29T15:04:40.513204+00:00", "last_reported": "2025-08-29T15:04:40.513204+00:00", "last_updated": "2025-08-29T15:04:40.513204+00:00", "context": {"id": "01K3V5QZA14Q8KE6MJZ0EDZ7ZJ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "automation.zi_dong_kai_guan_che_ku_men", "state": "on", "attributes": {"id": "1741241552349", "last_triggered": "2025-08-29T10:31:37.941715+00:00", "mode": "single", "current": 0, "friendly_name": "自動開關車庫門"}, "last_changed": "2025-08-29T15:04:40.513236+00:00", "last_reported": "2025-08-29T15:04:40.513236+00:00", "last_updated": "2025-08-29T15:04:40.513236+00:00", "context": {"id": "01K3V5QZA1YTXMGM21RCZ4KDA7", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "automation.mailbox", "state": "on", "attributes": {"id": "1741539922722", "last_triggered": "2025-08-29T10:34:13.038415+00:00", "mode": "single", "current": 0, "friendly_name": "Mailbox"}, "last_changed": "2025-08-29T15:04:40.513267+00:00", "last_reported": "2025-08-29T15:04:40.513267+00:00", "last_updated": "2025-08-29T15:04:40.513267+00:00", "context": {"id": "01K3V5QZA1VV442WGFT282ZT3A", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "automation.men_kai_qi_hou_kai_deng_2", "state": "on", "attributes": {"id": "1741834329889", "last_triggered": "2025-08-29T14:11:01.399396+00:00", "mode": "single", "current": 0, "friendly_name": "門開啟後開燈"}, "last_changed": "2025-08-29T15:04:40.513315+00:00", "last_reported": "2025-08-29T15:04:40.513315+00:00", "last_updated": "2025-08-29T15:04:40.513315+00:00", "context": {"id": "01K3V5QZA10NTEQV8FZETSDQAH", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "automation.can_ting_deng", "state": "on", "attributes": {"id": "1742129441705", "last_triggered": "2025-08-29T15:55:53.544304+00:00", "mode": "restart", "current": 0, "friendly_name": "餐廳燈"}, "last_changed": "2025-08-29T15:04:40.513372+00:00", "last_reported": "2025-08-29T15:55:53.546165+00:00", "last_updated": "2025-08-29T15:55:53.546165+00:00", "context": {"id": "01K3V8NR9ZZFY32Q4WHA14SF33", "parent_id": "01K3V8NR9SSVTQGP4P56SC73NE", "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "automation.zi_dong_guan_rong_zhu_deng", "state": "on", "attributes": {"id": "1746689048551", "last_triggered": "2025-08-29T15:37:19.587543+00:00", "mode": "single", "current": 1, "friendly_name": "自動關融燭燈"}, "last_changed": "2025-08-29T15:04:40.513441+00:00", "last_reported": "2025-08-29T15:37:19.589949+00:00", "last_updated": "2025-08-29T15:37:19.587821+00:00", "context": {"id": "01K3V7KRF2NJJ42NR4SSTJWB32", "parent_id": "01K3V7KREZBHN5JPYPBD57CZ9P", "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "automation.zi_dong_xuan_guan_deng", "state": "on", "attributes": {"id": "1747027764081", "last_triggered": "2025-08-29T15:35:19.788286+00:00", "mode": "restart", "current": 0, "friendly_name": "自動玄關燈"}, "last_changed": "2025-08-29T15:04:40.513497+00:00", "last_reported": "2025-08-29T15:39:22.534794+00:00", "last_updated": "2025-08-29T15:39:22.534794+00:00", "context": {"id": "01K3V7QGH6ND1A8W4K2D1036DQ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "automation.wo_shi_deng", "state": "on", "attributes": {"id": "1748529345949", "last_triggered": "2025-08-29T15:55:53.544085+00:00", "mode": "restart", "current": 0, "friendly_name": "臥室燈"}, "last_changed": "2025-08-29T15:04:40.513548+00:00", "last_reported": "2025-08-29T15:55:53.546135+00:00", "last_updated": "2025-08-29T15:55:53.546135+00:00", "context": {"id": "01K3V8NR9ZKKZ8NE2F3HSZYK78", "parent_id": "01K3V8NR9SSYGS7MM632AHMHBH", "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "automation.che_ku_men_guan_bi_hou_yan_chi_guan_deng", "state": "on", "attributes": {"id": "1717518265000", "last_triggered": "2025-08-29T15:57:53.004594+00:00", "mode": "restart", "current": 0, "friendly_name": "車庫門關閉後延遲關燈"}, "last_changed": "2025-08-29T15:04:40.513607+00:00", "last_reported": "2025-08-29T15:57:53.007226+00:00", "last_updated": "2025-08-29T15:57:53.007226+00:00", "context": {"id": "01K3V8SCZC14CK7STSAC43VH1W", "parent_id": "01K3V8SCZC9CSEED03JA4361JB", "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "automation.da_men_deng", "state": "on", "attributes": {"id": "1750523301886", "last_triggered": "2025-08-29T12:30:00.233059+00:00", "mode": "single", "current": 0, "friendly_name": "大門燈"}, "last_changed": "2025-08-29T15:04:40.513640+00:00", "last_reported": "2025-08-29T15:04:40.513640+00:00", "last_updated": "2025-08-29T15:04:40.513640+00:00", "context": {"id": "01K3V5QZA1VHHJ8WA088HJ8SW2", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "automation.mail_notifier", "state": "on", "attributes": {"id": "1754650959484", "last_triggered": "2025-08-29T03:49:59.885313+00:00", "mode": "single", "current": 0, "friendly_name": "Mail notifier"}, "last_changed": "2025-08-29T15:04:40.513706+00:00", "last_reported": "2025-08-29T15:04:40.513706+00:00", "last_updated": "2025-08-29T15:04:40.513706+00:00", "context": {"id": "01K3V5QZA1BTDNX3E8ES7XFH26", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.a1mini_0309ca4c1901915_pause_printing", "state": "unavailable", "attributes": {"icon": "mdi:pause", "friendly_name": "A1MINI_0309CA4C1901915 Pause printing"}, "last_changed": "2025-08-29T15:04:42.724141+00:00", "last_reported": "2025-08-29T15:10:28.584368+00:00", "last_updated": "2025-08-29T15:04:42.724141+00:00", "context": {"id": "01K3V5R1F4YQ1XN71XZWXPBA7N", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.a1mini_0309ca4c1901915_resume_printing", "state": "unavailable", "attributes": {"icon": "mdi:play", "friendly_name": "A1MINI_0309CA4C1901915 Resume printing"}, "last_changed": "2025-08-29T15:04:42.724198+00:00", "last_reported": "2025-08-29T15:10:28.584494+00:00", "last_updated": "2025-08-29T15:04:42.724198+00:00", "context": {"id": "01K3V5R1F4WD7TJPYBWPNS8EDK", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.a1mini_0309ca4c1901915_stop_printing", "state": "unavailable", "attributes": {"icon": "mdi:stop", "friendly_name": "A1MINI_0309CA4C1901915 Stop printing"}, "last_changed": "2025-08-29T15:04:42.724250+00:00", "last_reported": "2025-08-29T15:10:28.584532+00:00", "last_updated": "2025-08-29T15:04:42.724250+00:00", "context": {"id": "01K3V5R1F4DKA8SFK9ENZSGESB", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.a1mini_0309ca4c1901915_force_refresh_data", "state": "unknown", "attributes": {"icon": "mdi:refresh", "friendly_name": "A1MINI_0309CA4C1901915 Force refresh data"}, "last_changed": "2025-08-29T15:04:42.724302+00:00", "last_reported": "2025-08-29T15:10:28.584565+00:00", "last_updated": "2025-08-29T15:04:42.724302+00:00", "context": {"id": "01K3V5R1F4WTKQZWGKQ78XM5HE", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "binary_sensor.zigbee2mqtt_bridge_connection_state", "state": "on", "attributes": {"device_class": "connectivity", "friendly_name": "Zigbee2MQTT Bridge Connection state"}, "last_changed": "2025-08-29T15:55:53.526365+00:00", "last_reported": "2025-08-29T15:55:53.526365+00:00", "last_updated": "2025-08-29T15:55:53.526365+00:00", "context": {"id": "01K3V8NR9PVRE6B4K1TC30J397", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "binary_sensor.xuan_guan_ren_zai_gan_ying_presence", "state": "on", "attributes": {"device_class": "presence", "friendly_name": "玄關人在感應 Presence"}, "last_changed": "2025-08-29T15:55:53.526433+00:00", "last_reported": "2025-08-29T15:55:53.526433+00:00", "last_updated": "2025-08-29T15:55:53.526433+00:00", "context": {"id": "01K3V8NR9PEG647C1PK3S7FBQQ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "binary_sensor.xuan_guan_dong_zuo_gan_ying_occupancy", "state": "off", "attributes": {"device_class": "occupancy", "friendly_name": "玄關動作感應 Occupancy"}, "last_changed": "2025-08-29T15:55:53.526449+00:00", "last_reported": "2025-08-29T15:55:53.526449+00:00", "last_updated": "2025-08-29T15:55:53.526449+00:00", "context": {"id": "01K3V8NR9PRYZ7P4H32WVPT6VS", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "binary_sensor.0xa4c138ba2d3d0757_contact", "state": "off", "attributes": {"device_class": "door", "friendly_name": "信箱門感應 Door"}, "last_changed": "2025-08-29T15:55:53.526466+00:00", "last_reported": "2025-08-29T15:55:53.526466+00:00", "last_updated": "2025-08-29T15:55:53.526466+00:00", "context": {"id": "01K3V8NR9P4TXP7HAXPW006X0X", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "binary_sensor.garage_contact", "state": "off", "attributes": {"device_class": "door", "friendly_name": "車庫門感應 Door"}, "last_changed": "2025-08-29T15:55:53.526480+00:00", "last_reported": "2025-08-29T15:55:53.526480+00:00", "last_updated": "2025-08-29T15:55:53.526480+00:00", "context": {"id": "01K3V8NR9PZ8HDYN47M3SSGZR1", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "binary_sensor.garage_side_door", "state": "off", "attributes": {"device_class": "door", "friendly_name": "車庫側門感應 Door"}, "last_changed": "2025-08-29T15:55:53.526505+00:00", "last_reported": "2025-08-29T15:55:53.526505+00:00", "last_updated": "2025-08-29T15:55:53.526505+00:00", "context": {"id": "01K3V8NR9P0Q39S6QKTQR51577", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "binary_sensor.0x00158d000708748f_contact", "state": "off", "attributes": {"device_class": "door", "friendly_name": "信箱感應 Door"}, "last_changed": "2025-08-29T15:55:53.526517+00:00", "last_reported": "2025-08-29T15:55:53.526517+00:00", "last_updated": "2025-08-29T15:55:53.526517+00:00", "context": {"id": "01K3V8NR9P2QMD7EMHB0Z7TP0W", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "binary_sensor.3f_dong_zuo_gan_ying_occupancy", "state": "off", "attributes": {"device_class": "occupancy", "friendly_name": "3F 動作感應 Occupancy"}, "last_changed": "2025-08-29T15:55:53.526529+00:00", "last_reported": "2025-08-29T15:55:53.526529+00:00", "last_updated": "2025-08-29T15:55:53.526529+00:00", "context": {"id": "01K3V8NR9PC82H2RJZ1N4X2JKX", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "binary_sensor.3f_ren_zai_gan_ying_presence", "state": "off", "attributes": {"device_class": "presence", "friendly_name": "3F 人在感應 Presence"}, "last_changed": "2025-08-29T15:55:53.526540+00:00", "last_reported": "2025-08-29T15:55:53.526540+00:00", "last_updated": "2025-08-29T15:55:53.526540+00:00", "context": {"id": "01K3V8NR9PHH6P9GTR0KKMEZJ9", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "binary_sensor.0xa4c138b04b602c88_presence", "state": "on", "attributes": {"device_class": "presence", "friendly_name": "0xa4c138b04b602c88 Presence"}, "last_changed": "2025-08-29T15:55:53.526551+00:00", "last_reported": "2025-08-29T15:55:53.526551+00:00", "last_updated": "2025-08-29T15:55:53.526551+00:00", "context": {"id": "01K3V8NR9P5DTPHAYGWDZES1K0", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.zigbee2mqtt_bridge_restart", "state": "unknown", "attributes": {"device_class": "restart", "friendly_name": "Zigbee2MQTT Bridge Restart"}, "last_changed": "2025-08-29T15:55:53.526571+00:00", "last_reported": "2025-08-29T15:55:53.526571+00:00", "last_updated": "2025-08-29T15:55:53.526571+00:00", "context": {"id": "01K3V8NR9PK3RXP7XBBG77S0MW", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.chuang_lian_you_factory_reset", "state": "unknown", "attributes": {"friendly_name": "窗簾右 Factory reset"}, "last_changed": "2025-08-29T15:55:53.526585+00:00", "last_reported": "2025-08-29T15:55:53.526585+00:00", "last_updated": "2025-08-29T15:55:53.526585+00:00", "context": {"id": "01K3V8NR9P32G003G7R469Y58D", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.chuang_lian_zuo_factory_reset", "state": "unknown", "attributes": {"friendly_name": "窗簾左 Factory reset"}, "last_changed": "2025-08-29T15:55:53.526595+00:00", "last_reported": "2025-08-29T15:55:53.526595+00:00", "last_updated": "2025-08-29T15:55:53.526595+00:00", "context": {"id": "01K3V8NR9PDD2H37SEDFNV31SK", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "light.4f_yu_shi_deng", "state": "on", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": "color_temp", "brightness": 217, "color_temp_kelvin": 2000, "color_temp": 500, "hs_color": [30.601, 94.547], "rgb_color": [255, 137, 14], "xy_color": [0.598, 0.383], "friendly_name": "4F 浴室燈", "supported_features": 44}, "last_changed": "2025-08-29T15:55:53.526826+00:00", "last_reported": "2025-08-29T15:55:53.526826+00:00", "last_updated": "2025-08-29T15:55:53.526826+00:00", "context": {"id": "01K3V8NR9PM05QB414W7ED86FC", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "light.can_ting_zhong_jian_kan_deng", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "餐廳中間崁燈", "supported_features": 44}, "last_changed": "2025-08-29T15:55:53.526876+00:00", "last_reported": "2025-08-29T15:55:53.526876+00:00", "last_updated": "2025-08-29T15:55:53.526876+00:00", "context": {"id": "01K3V8NR9PN10DY622S4N2T3NT", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "light.can_ting_you_kan_deng", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "餐廳右崁燈", "supported_features": 44}, "last_changed": "2025-08-29T15:55:53.526910+00:00", "last_reported": "2025-08-29T15:55:53.526910+00:00", "last_updated": "2025-08-29T15:55:53.526910+00:00", "context": {"id": "01K3V8NR9P2KWQPHKZ22K61JJC", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "light.ke_ting_zuo_diao_deng", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "客廳左吊燈", "supported_features": 44}, "last_changed": "2025-08-29T15:55:53.526933+00:00", "last_reported": "2025-08-29T15:55:53.526933+00:00", "last_updated": "2025-08-29T15:55:53.526933+00:00", "context": {"id": "01K3V8NR9PQ321B1W1CK0E7J79", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "light.ke_ting_you_diao_deng", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "客廳右吊燈", "supported_features": 44}, "last_changed": "2025-08-29T15:55:53.526948+00:00", "last_reported": "2025-08-29T15:55:53.526948+00:00", "last_updated": "2025-08-29T15:55:53.526948+00:00", "context": {"id": "01K3V8NR9PZ4ZPWKV7FF4K81E2", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "light.can_ting_zuo_kan_deng", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "餐廳左崁燈", "supported_features": 44}, "last_changed": "2025-08-29T15:55:53.526965+00:00", "last_reported": "2025-08-29T15:55:53.526965+00:00", "last_updated": "2025-08-29T15:55:53.526965+00:00", "context": {"id": "01K3V8NR9P4QXK3271Z8K7PHVA", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "light.ke_ting_zuo_xia_kan_deng", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "客廳左下崁燈", "supported_features": 44}, "last_changed": "2025-08-29T15:55:53.526980+00:00", "last_reported": "2025-08-29T15:55:53.526980+00:00", "last_updated": "2025-08-29T15:55:53.526980+00:00", "context": {"id": "01K3V8NR9PP0X9TT81SCBSM7E5", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "light.ke_ting_zuo_shang_kan_deng", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "客廳左上崁燈", "supported_features": 44}, "last_changed": "2025-08-29T15:55:53.526995+00:00", "last_reported": "2025-08-29T15:55:53.526995+00:00", "last_updated": "2025-08-29T15:55:53.526995+00:00", "context": {"id": "01K3V8NR9PFSFABAKDZK9YHJ5B", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "light.ke_ting_you_shang_kan_deng", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "客廳右上崁燈", "supported_features": 44}, "last_changed": "2025-08-29T15:55:53.527009+00:00", "last_reported": "2025-08-29T15:55:53.527009+00:00", "last_updated": "2025-08-29T15:55:53.527009+00:00", "context": {"id": "01K3V8NR9QG524E6NPTG9WYCMB", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "light.ke_ting_you_xia_kan_deng", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "客廳右下崁燈", "supported_features": 44}, "last_changed": "2025-08-29T15:55:53.527026+00:00", "last_reported": "2025-08-29T15:55:53.527026+00:00", "last_updated": "2025-08-29T15:55:53.527026+00:00", "context": {"id": "01K3V8NR9QG0GF8VF4Y7YXCG82", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "light.ceiling", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "客廳吊燈", "supported_features": 44}, "last_changed": "2025-08-29T15:55:53.527042+00:00", "last_reported": "2025-08-29T15:55:53.527042+00:00", "last_updated": "2025-08-29T15:55:53.527042+00:00", "context": {"id": "01K3V8NR9Q8TTSSY48XJGG6NT1", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "light.dining", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "餐廳燈", "supported_features": 44}, "last_changed": "2025-08-29T15:55:53.527061+00:00", "last_reported": "2025-08-29T15:55:53.527061+00:00", "last_updated": "2025-08-29T15:55:53.527061+00:00", "context": {"id": "01K3V8NR9QQDARXTW9B9SR1SPK", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "light.downlight", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "客廳崁燈", "supported_features": 44}, "last_changed": "2025-08-29T15:55:53.527078+00:00", "last_reported": "2025-08-29T15:55:53.527078+00:00", "last_updated": "2025-08-29T15:55:53.527078+00:00", "context": {"id": "01K3V8NR9QK0VMBETXFGRXZ7GE", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.xuan_guan_ren_zai_gan_ying_large_motion_detection_sensitivity", "state": "2", "attributes": {"min": 0.0, "max": 10.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "x", "friendly_name": "玄關人在感應 Large motion detection sensitivity"}, "last_changed": "2025-08-29T15:55:53.527109+00:00", "last_reported": "2025-08-29T15:55:53.527109+00:00", "last_updated": "2025-08-29T15:55:53.527109+00:00", "context": {"id": "01K3V8NR9Q5Y9A6PJBRGAZXA18", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 10.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "x", "native_value": 2}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.xuan_guan_ren_zai_gan_ying_large_motion_detection_distance", "state": "4", "attributes": {"min": 0.0, "max": 10.0, "step": 0.01, "mode": "auto", "unit_of_measurement": "m", "friendly_name": "玄關人在感應 Large motion detection distance"}, "last_changed": "2025-08-29T15:55:53.527126+00:00", "last_reported": "2025-08-29T15:55:53.527126+00:00", "last_updated": "2025-08-29T15:55:53.527126+00:00", "context": {"id": "01K3V8NR9QWVSHAD0CQT47ZTFN", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 10.0, "native_min_value": 0.0, "native_step": 0.01, "native_unit_of_measurement": "m", "native_value": 4}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.xuan_guan_ren_zai_gan_ying_fading_time", "state": "60", "attributes": {"min": 0.0, "max": 28800.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "s", "friendly_name": "玄關人在感應 Fading time"}, "last_changed": "2025-08-29T15:55:53.527137+00:00", "last_reported": "2025-08-29T15:55:53.527137+00:00", "last_updated": "2025-08-29T15:55:53.527137+00:00", "context": {"id": "01K3V8NR9QRKXN2TPFZ0H8ZB38", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 28800.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "s", "native_value": 60}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.xuan_guan_ren_zai_gan_ying_medium_motion_detection_distance", "state": "3", "attributes": {"min": 0.0, "max": 6.0, "step": 0.01, "mode": "auto", "unit_of_measurement": "m", "friendly_name": "玄關人在感應 Medium motion detection distance"}, "last_changed": "2025-08-29T15:55:53.527149+00:00", "last_reported": "2025-08-29T15:55:53.527149+00:00", "last_updated": "2025-08-29T15:55:53.527149+00:00", "context": {"id": "01K3V8NR9QBZVVD3DYJQPFFBMY", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 6.0, "native_min_value": 0.0, "native_step": 0.01, "native_unit_of_measurement": "m", "native_value": 3}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.xuan_guan_ren_zai_gan_ying_medium_motion_detection_sensitivity", "state": "5", "attributes": {"min": 0.0, "max": 10.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "x", "friendly_name": "玄關人在感應 Medium motion detection sensitivity"}, "last_changed": "2025-08-29T15:55:53.527160+00:00", "last_reported": "2025-08-29T15:55:53.527160+00:00", "last_updated": "2025-08-29T15:55:53.527160+00:00", "context": {"id": "01K3V8NR9QHGJQM7RRPT26BB02", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 10.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "x", "native_value": 5}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.xuan_guan_ren_zai_gan_ying_small_detection_distance", "state": "3", "attributes": {"min": 0.0, "max": 6.0, "step": 0.01, "mode": "auto", "unit_of_measurement": "m", "friendly_name": "玄關人在感應 Small detection distance"}, "last_changed": "2025-08-29T15:55:53.527172+00:00", "last_reported": "2025-08-29T15:55:53.527172+00:00", "last_updated": "2025-08-29T15:55:53.527172+00:00", "context": {"id": "01K3V8NR9QWBT9JHZYG4YEKHJW", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 6.0, "native_min_value": 0.0, "native_step": 0.01, "native_unit_of_measurement": "m", "native_value": 3}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.xuan_guan_ren_zai_gan_ying_small_detection_sensitivity", "state": "5", "attributes": {"min": 0.0, "max": 10.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "x", "friendly_name": "玄關人在感應 Small detection sensitivity"}, "last_changed": "2025-08-29T15:55:53.527183+00:00", "last_reported": "2025-08-29T15:55:53.527183+00:00", "last_updated": "2025-08-29T15:55:53.527183+00:00", "context": {"id": "01K3V8NR9QD27N0A9CT5H12MW5", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 10.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "x", "native_value": 5}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.xuan_guan_ren_zai_gan_ying_minimum_range", "state": "unknown", "attributes": {"min": 0.0, "max": 6.0, "step": 0.01, "mode": "auto", "unit_of_measurement": "m", "friendly_name": "玄關人在感應 Minimum range"}, "last_changed": "2025-08-29T15:55:53.527195+00:00", "last_reported": "2025-08-29T15:55:53.527195+00:00", "last_updated": "2025-08-29T15:55:53.527195+00:00", "context": {"id": "01K3V8NR9QCNEJRGHY041GM8JX", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 6.0, "native_min_value": 0.0, "native_step": 0.01, "native_unit_of_measurement": "m", "native_value": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.xuan_guan_dong_zuo_gan_ying_motion_timeout", "state": "unknown", "attributes": {"min": 5.0, "max": 60.0, "step": 1.0, "mode": "auto", "friendly_name": "玄關動作感應 Motion timeout"}, "last_changed": "2025-08-29T15:55:53.527206+00:00", "last_reported": "2025-08-29T15:55:53.527206+00:00", "last_updated": "2025-08-29T15:55:53.527206+00:00", "context": {"id": "01K3V8NR9QQVQRGA850KVKB2FV", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 60.0, "native_min_value": 5.0, "native_step": 1.0, "native_unit_of_measurement": null, "native_value": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.che_ku_deng_kai_guan_countdown_l1", "state": "0", "attributes": {"min": 0.0, "max": 43200.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "s", "friendly_name": "車庫燈開關 Countdown l1"}, "last_changed": "2025-08-29T15:55:53.527218+00:00", "last_reported": "2025-08-29T15:55:53.527218+00:00", "last_updated": "2025-08-29T15:55:53.527218+00:00", "context": {"id": "01K3V8NR9QEM32SQZEYJ7B6KKV", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 43200.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "s", "native_value": 0}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.che_ku_deng_kai_guan_countdown_l2", "state": "0", "attributes": {"min": 0.0, "max": 43200.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "s", "friendly_name": "車庫燈開關 Countdown l2"}, "last_changed": "2025-08-29T15:55:53.527229+00:00", "last_reported": "2025-08-29T15:55:53.527229+00:00", "last_updated": "2025-08-29T15:55:53.527229+00:00", "context": {"id": "01K3V8NR9QB8YFTXX801N62X8C", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 43200.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "s", "native_value": 0}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.xin_xiang_men_gan_ying_illuminance_interval", "state": "60", "attributes": {"min": 1.0, "max": 720.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "minutes", "friendly_name": "信箱門感應 Illuminance interval"}, "last_changed": "2025-08-29T15:55:53.527239+00:00", "last_reported": "2025-08-29T15:55:53.527239+00:00", "last_updated": "2025-08-29T15:55:53.527239+00:00", "context": {"id": "01K3V8NR9QX3V1ZAF3DW9F1Q3M", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 720.0, "native_min_value": 1.0, "native_step": 1.0, "native_unit_of_measurement": "minutes", "native_value": 60}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.che_ku_men_gan_ying_illuminance_interval", "state": "60", "attributes": {"min": 1.0, "max": 720.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "minutes", "friendly_name": "車庫門感應 Illuminance interval"}, "last_changed": "2025-08-29T15:55:53.527251+00:00", "last_reported": "2025-08-29T15:55:53.527251+00:00", "last_updated": "2025-08-29T15:55:53.527251+00:00", "context": {"id": "01K3V8NR9QFAC4E8411NVYGXX8", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 720.0, "native_min_value": 1.0, "native_step": 1.0, "native_unit_of_measurement": "minutes", "native_value": 60}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.che_ku_ce_men_gan_ying_illuminance_interval", "state": "60", "attributes": {"min": 1.0, "max": 720.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "minutes", "friendly_name": "車庫側門感應 Illuminance interval"}, "last_changed": "2025-08-29T15:55:53.527261+00:00", "last_reported": "2025-08-29T15:55:53.527261+00:00", "last_updated": "2025-08-29T15:55:53.527261+00:00", "context": {"id": "01K3V8NR9QZ3WYFVFX2NXSQZ5H", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 720.0, "native_min_value": 1.0, "native_step": 1.0, "native_unit_of_measurement": "minutes", "native_value": 60}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.3f_dong_zuo_gan_ying_motion_timeout", "state": "unknown", "attributes": {"min": 5.0, "max": 60.0, "step": 1.0, "mode": "auto", "friendly_name": "3F 動作感應 Motion timeout"}, "last_changed": "2025-08-29T15:55:53.527271+00:00", "last_reported": "2025-08-29T15:55:53.527271+00:00", "last_updated": "2025-08-29T15:55:53.527271+00:00", "context": {"id": "01K3V8NR9Q8V8NZ9C78WRZB8Y4", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 60.0, "native_min_value": 5.0, "native_step": 1.0, "native_unit_of_measurement": null, "native_value": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.3f_ren_zai_gan_ying_large_motion_detection_sensitivity", "state": "6", "attributes": {"min": 0.0, "max": 10.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "x", "friendly_name": "3F 人在感應 Large motion detection sensitivity"}, "last_changed": "2025-08-29T15:55:53.527281+00:00", "last_reported": "2025-08-29T15:55:53.527281+00:00", "last_updated": "2025-08-29T15:55:53.527281+00:00", "context": {"id": "01K3V8NR9QN2E9Z2KQ2Z39SCCC", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 10.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "x", "native_value": 6}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.3f_ren_zai_gan_ying_large_motion_detection_distance", "state": "6", "attributes": {"min": 0.0, "max": 10.0, "step": 0.01, "mode": "auto", "unit_of_measurement": "m", "friendly_name": "3F 人在感應 Large motion detection distance"}, "last_changed": "2025-08-29T15:55:53.527291+00:00", "last_reported": "2025-08-29T15:55:53.527291+00:00", "last_updated": "2025-08-29T15:55:53.527291+00:00", "context": {"id": "01K3V8NR9Q2NC3SMFB9W8JFKKB", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 10.0, "native_min_value": 0.0, "native_step": 0.01, "native_unit_of_measurement": "m", "native_value": 6}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.3f_ren_zai_gan_ying_fading_time", "state": "60", "attributes": {"min": 0.0, "max": 28800.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "s", "friendly_name": "3F 人在感應 Fading time"}, "last_changed": "2025-08-29T15:55:53.527301+00:00", "last_reported": "2025-08-29T15:55:53.527301+00:00", "last_updated": "2025-08-29T15:55:53.527301+00:00", "context": {"id": "01K3V8NR9Q7ZE72XPW91MN4XY8", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 28800.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "s", "native_value": 60}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.3f_ren_zai_gan_ying_medium_motion_detection_distance", "state": "4", "attributes": {"min": 0.0, "max": 6.0, "step": 0.01, "mode": "auto", "unit_of_measurement": "m", "friendly_name": "3F 人在感應 Medium motion detection distance"}, "last_changed": "2025-08-29T15:55:53.527311+00:00", "last_reported": "2025-08-29T15:55:53.527311+00:00", "last_updated": "2025-08-29T15:55:53.527311+00:00", "context": {"id": "01K3V8NR9QFHTY9QC32APE7QKT", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 6.0, "native_min_value": 0.0, "native_step": 0.01, "native_unit_of_measurement": "m", "native_value": 4}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.3f_ren_zai_gan_ying_medium_motion_detection_sensitivity", "state": "8", "attributes": {"min": 0.0, "max": 10.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "x", "friendly_name": "3F 人在感應 Medium motion detection sensitivity"}, "last_changed": "2025-08-29T15:55:53.527322+00:00", "last_reported": "2025-08-29T15:55:53.527322+00:00", "last_updated": "2025-08-29T15:55:53.527322+00:00", "context": {"id": "01K3V8NR9QWMVMG0Q8XAW39A7E", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 10.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "x", "native_value": 8}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.3f_ren_zai_gan_ying_small_detection_distance", "state": "4", "attributes": {"min": 0.0, "max": 6.0, "step": 0.01, "mode": "auto", "unit_of_measurement": "m", "friendly_name": "3F 人在感應 Small detection distance"}, "last_changed": "2025-08-29T15:55:53.527332+00:00", "last_reported": "2025-08-29T15:55:53.527332+00:00", "last_updated": "2025-08-29T15:55:53.527332+00:00", "context": {"id": "01K3V8NR9QTG156NHND2HBX399", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 6.0, "native_min_value": 0.0, "native_step": 0.01, "native_unit_of_measurement": "m", "native_value": 4}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.3f_ren_zai_gan_ying_small_detection_sensitivity", "state": "8", "attributes": {"min": 0.0, "max": 10.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "x", "friendly_name": "3F 人在感應 Small detection sensitivity"}, "last_changed": "2025-08-29T15:55:53.527342+00:00", "last_reported": "2025-08-29T15:55:53.527342+00:00", "last_updated": "2025-08-29T15:55:53.527342+00:00", "context": {"id": "01K3V8NR9Q9EYMNWFTQ35MY4K4", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 10.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "x", "native_value": 8}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.3f_ren_zai_gan_ying_minimum_range", "state": "unknown", "attributes": {"min": 0.0, "max": 6.0, "step": 0.01, "mode": "auto", "unit_of_measurement": "m", "friendly_name": "3F 人在感應 Minimum range"}, "last_changed": "2025-08-29T15:55:53.527353+00:00", "last_reported": "2025-08-29T15:55:53.527353+00:00", "last_updated": "2025-08-29T15:55:53.527353+00:00", "context": {"id": "01K3V8NR9QGYYHTP8GJX98CH9D", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 6.0, "native_min_value": 0.0, "native_step": 0.01, "native_unit_of_measurement": "m", "native_value": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.4f_lou_ti_kai_guan_countdown_l1", "state": "0", "attributes": {"min": 0.0, "max": 43200.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "s", "friendly_name": "4F 樓梯開關 Countdown l1"}, "last_changed": "2025-08-29T15:55:53.527364+00:00", "last_reported": "2025-08-29T15:55:53.527364+00:00", "last_updated": "2025-08-29T15:55:53.527364+00:00", "context": {"id": "01K3V8NR9QT7GTKH6Q7WYM7X3E", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 43200.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "s", "native_value": 0}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.4f_lou_ti_kai_guan_countdown_l2", "state": "0", "attributes": {"min": 0.0, "max": 43200.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "s", "friendly_name": "4F 樓梯開關 Countdown l2"}, "last_changed": "2025-08-29T15:55:53.527375+00:00", "last_reported": "2025-08-29T15:55:53.527375+00:00", "last_updated": "2025-08-29T15:55:53.527375+00:00", "context": {"id": "01K3V8NR9Q84MTYK7NXMXPGDX5", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 43200.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "s", "native_value": 0}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.0xa4c138b04b602c88_fading_time", "state": "30", "attributes": {"min": 0.0, "max": 28800.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "s", "friendly_name": "0xa4c138b04b602c88 Fading time"}, "last_changed": "2025-08-29T15:55:53.527385+00:00", "last_reported": "2025-08-29T15:55:53.527385+00:00", "last_updated": "2025-08-29T15:55:53.527385+00:00", "context": {"id": "01K3V8NR9QR5XMRSQCDKDHE56G", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 28800.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "s", "native_value": 30}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.0xa4c138b04b602c88_static_detection_distance", "state": "5", "attributes": {"min": 0.0, "max": 10.0, "step": 0.01, "mode": "auto", "unit_of_measurement": "m", "friendly_name": "0xa4c138b04b602c88 Static detection distance"}, "last_changed": "2025-08-29T15:55:53.527394+00:00", "last_reported": "2025-08-29T15:55:53.527394+00:00", "last_updated": "2025-08-29T15:55:53.527394+00:00", "context": {"id": "01K3V8NR9Q7T0P8G3G0PTBCS4W", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 10.0, "native_min_value": 0.0, "native_step": 0.01, "native_unit_of_measurement": "m", "native_value": 5}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.0xa4c138b04b602c88_static_detection_sensitivity", "state": "6", "attributes": {"min": 0.0, "max": 10.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "x", "friendly_name": "0xa4c138b04b602c88 Static detection sensitivity"}, "last_changed": "2025-08-29T15:55:53.527405+00:00", "last_reported": "2025-08-29T15:55:53.527405+00:00", "last_updated": "2025-08-29T15:55:53.527405+00:00", "context": {"id": "01K3V8NR9QSHEXED95YV0BG58J", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 10.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "x", "native_value": 6}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.0xa4c138b04b602c88_motion_detection_sensitivity", "state": "5", "attributes": {"min": 0.0, "max": 10.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "x", "friendly_name": "0xa4c138b04b602c88 Motion detection sensitivity"}, "last_changed": "2025-08-29T15:55:53.527415+00:00", "last_reported": "2025-08-29T15:55:53.527415+00:00", "last_updated": "2025-08-29T15:55:53.527415+00:00", "context": {"id": "01K3V8NR9QXHRMEZC8M3B4RS3Q", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 10.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "x", "native_value": 5}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.zigbee2mqtt_bridge_log_level", "state": "info", "attributes": {"options": ["error", "warning", "info", "debug"], "friendly_name": "Zigbee2MQTT Bridge Log level"}, "last_changed": "2025-08-29T15:55:53.527434+00:00", "last_reported": "2025-08-29T15:55:53.527434+00:00", "last_updated": "2025-08-29T15:55:53.527434+00:00", "context": {"id": "01K3V8NR9QFNQKFH2JNQGVPVW1", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.2f_ce_suo_kai_guan_power_on_behavior", "state": "previous", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "2F 廁所開關 Power-on behavior"}, "last_changed": "2025-08-29T15:55:53.527447+00:00", "last_reported": "2025-08-29T15:55:53.527447+00:00", "last_updated": "2025-08-29T15:55:53.527447+00:00", "context": {"id": "01K3V8NR9QKE1C0PJJZWHPW993", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.3f_bi_deng_kai_guan_power_on_behavior", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "3F 壁燈開關 Power-on behavior"}, "last_changed": "2025-08-29T15:55:53.527459+00:00", "last_reported": "2025-08-29T15:55:53.527459+00:00", "last_updated": "2025-08-29T15:55:53.527459+00:00", "context": {"id": "01K3V8NR9QC81FZNNBXME4X779", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.che_ku_deng_kai_guan_power_on_behavior_l1", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "車庫燈開關 Power-on behavior l1"}, "last_changed": "2025-08-29T15:55:53.527467+00:00", "last_reported": "2025-08-29T15:55:53.527467+00:00", "last_updated": "2025-08-29T15:55:53.527467+00:00", "context": {"id": "01K3V8NR9Q7PY3M55XCRQT6BPK", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.che_ku_deng_kai_guan_power_on_behavior_l2", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "車庫燈開關 Power-on behavior l2"}, "last_changed": "2025-08-29T15:55:53.527478+00:00", "last_reported": "2025-08-29T15:55:53.527478+00:00", "last_updated": "2025-08-29T15:55:53.527478+00:00", "context": {"id": "01K3V8NR9Q8AWN3RBBYF9J1E5P", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.che_ku_deng_kai_guan_switch_type", "state": "unknown", "attributes": {"options": ["toggle", "state", "momentary"], "icon": "mdi:tune", "friendly_name": "車庫燈開關 Switch type"}, "last_changed": "2025-08-29T15:55:53.527487+00:00", "last_reported": "2025-08-29T15:55:53.527487+00:00", "last_updated": "2025-08-29T15:55:53.527487+00:00", "context": {"id": "01K3V8NR9QCAQJH9RBBR0YEX82", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.che_ku_deng_kai_guan_indicator_mode", "state": "on/off", "attributes": {"options": ["off", "off/on", "on/off", "on"], "friendly_name": "車庫燈開關 Indicator mode"}, "last_changed": "2025-08-29T15:55:53.527497+00:00", "last_reported": "2025-08-29T15:55:53.527497+00:00", "last_updated": "2025-08-29T15:55:53.527497+00:00", "context": {"id": "01K3V8NR9QHVMTSHZP6BCFN4GY", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.che_ku_men_switch_type", "state": "state", "attributes": {"options": ["toggle", "state", "momentary"], "icon": "mdi:tune", "friendly_name": "車庫門 Switch type"}, "last_changed": "2025-08-29T15:55:53.527507+00:00", "last_reported": "2025-08-29T15:55:53.527507+00:00", "last_updated": "2025-08-29T15:55:53.527507+00:00", "context": {"id": "01K3V8NR9Q2BXMNQDNY9A64NTD", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.che_ku_men_power_on_behavior", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "車庫門 Power-on behavior"}, "last_changed": "2025-08-29T15:55:53.527516+00:00", "last_reported": "2025-08-29T15:55:53.527516+00:00", "last_updated": "2025-08-29T15:55:53.527516+00:00", "context": {"id": "01K3V8NR9QJPWMX8RM73G98DN5", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.shu_fang_deng_kai_guan_power_on_behavior", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "書房燈開關 Power-on behavior"}, "last_changed": "2025-08-29T15:55:53.527527+00:00", "last_reported": "2025-08-29T15:55:53.527527+00:00", "last_updated": "2025-08-29T15:55:53.527527+00:00", "context": {"id": "01K3V8NR9Q5WDKBMCSWX07W1TX", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.3f_yu_shi_deng_kai_guan_power_on_behavior", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "3F 浴室燈開關 Power-on behavior"}, "last_changed": "2025-08-29T15:55:53.527536+00:00", "last_reported": "2025-08-29T15:55:53.527536+00:00", "last_updated": "2025-08-29T15:55:53.527536+00:00", "context": {"id": "01K3V8NR9QCZCJZAKQT1QTCSGZ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.geng_yi_shi_kai_guan_power_on_behavior", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "更衣室開關 Power-on behavior"}, "last_changed": "2025-08-29T15:55:53.527545+00:00", "last_reported": "2025-08-29T15:55:53.527545+00:00", "last_updated": "2025-08-29T15:55:53.527545+00:00", "context": {"id": "01K3V8NR9Q8X58AZR0JTFJZDTT", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.4f_lou_ti_kai_guan_power_on_behavior_l1", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "4F 樓梯開關 Power-on behavior l1"}, "last_changed": "2025-08-29T15:55:53.527554+00:00", "last_reported": "2025-08-29T15:55:53.527554+00:00", "last_updated": "2025-08-29T15:55:53.527554+00:00", "context": {"id": "01K3V8NR9Q2PS3AACJ96X9QJFN", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.4f_lou_ti_kai_guan_power_on_behavior_l2", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "4F 樓梯開關 Power-on behavior l2"}, "last_changed": "2025-08-29T15:55:53.527563+00:00", "last_reported": "2025-08-29T15:55:53.527563+00:00", "last_updated": "2025-08-29T15:55:53.527563+00:00", "context": {"id": "01K3V8NR9QYZZCXE7C8MA14GYX", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.4f_lou_ti_kai_guan_switch_type", "state": "unknown", "attributes": {"options": ["toggle", "state", "momentary"], "icon": "mdi:tune", "friendly_name": "4F 樓梯開關 Switch type"}, "last_changed": "2025-08-29T15:55:53.527582+00:00", "last_reported": "2025-08-29T15:55:53.527582+00:00", "last_updated": "2025-08-29T15:55:53.527582+00:00", "context": {"id": "01K3V8NR9QTNZXRR4SYT590BEE", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.4f_lou_ti_kai_guan_indicator_mode", "state": "on/off", "attributes": {"options": ["off", "off/on", "on/off", "on"], "friendly_name": "4F 樓梯開關 Indicator mode"}, "last_changed": "2025-08-29T15:55:53.527594+00:00", "last_reported": "2025-08-29T15:55:53.527594+00:00", "last_updated": "2025-08-29T15:55:53.527594+00:00", "context": {"id": "01K3V8NR9Q1DT4ZHEKJS9ZCEZS", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.4f_yang_tai_deng_kai_guan_power_on_behavior", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "4F 陽台燈開關 Power-on behavior"}, "last_changed": "2025-08-29T15:55:53.527604+00:00", "last_reported": "2025-08-29T15:55:53.527604+00:00", "last_updated": "2025-08-29T15:55:53.527604+00:00", "context": {"id": "01K3V8NR9QRZ6W16RGR00V6C6H", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.wo_shi_deng_men_bian_kai_guan_power_on_behavior", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "臥室燈門邊開關 Power-on behavior"}, "last_changed": "2025-08-29T15:55:53.527620+00:00", "last_reported": "2025-08-29T15:55:53.527620+00:00", "last_updated": "2025-08-29T15:55:53.527620+00:00", "context": {"id": "01K3V8NR9QCXKYRFQEPGSF9KBZ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.wo_shi_deng_qiang_mian_kai_guan_power_on_behavior", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "臥室燈牆面開關 Power-on behavior"}, "last_changed": "2025-08-29T15:55:53.527630+00:00", "last_reported": "2025-08-29T15:55:53.527630+00:00", "last_updated": "2025-08-29T15:55:53.527630+00:00", "context": {"id": "01K3V8NR9QP9CWQEWFMJAMNFKZ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.4f_yu_shi_deng_kai_guan_power_on_behavior", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "4F 浴室燈開關 Power-on behavior"}, "last_changed": "2025-08-29T15:55:53.527640+00:00", "last_reported": "2025-08-29T15:55:53.527640+00:00", "last_updated": "2025-08-29T15:55:53.527640+00:00", "context": {"id": "01K3V8NR9QR302MX0MQXSBZG5K", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.4f_yu_shi_deng_color_power_on_behavior", "state": "previous", "attributes": {"options": ["initial", "previous", "customized"], "icon": "mdi:palette", "friendly_name": "4F 浴室燈 Color power on behavior"}, "last_changed": "2025-08-29T15:55:53.527650+00:00", "last_reported": "2025-08-29T15:55:53.527650+00:00", "last_updated": "2025-08-29T15:55:53.527650+00:00", "context": {"id": "01K3V8NR9Q3XSFN57Q5X48JVAR", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.can_ting_zhong_jian_kan_deng_color_power_on_behavior", "state": "unknown", "attributes": {"options": ["initial", "previous", "customized"], "icon": "mdi:palette", "friendly_name": "餐廳中間崁燈 Color power on behavior"}, "last_changed": "2025-08-29T15:55:53.527660+00:00", "last_reported": "2025-08-29T15:55:53.527660+00:00", "last_updated": "2025-08-29T15:55:53.527660+00:00", "context": {"id": "01K3V8NR9Q36VBDZSTWZ83C2GS", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.can_ting_you_kan_deng_color_power_on_behavior", "state": "unknown", "attributes": {"options": ["initial", "previous", "customized"], "icon": "mdi:palette", "friendly_name": "餐廳右崁燈 Color power on behavior"}, "last_changed": "2025-08-29T15:55:53.527670+00:00", "last_reported": "2025-08-29T15:55:53.527670+00:00", "last_updated": "2025-08-29T15:55:53.527670+00:00", "context": {"id": "01K3V8NR9QGX99RV6DBQPZHEMD", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.ke_ting_zuo_diao_deng_power_on_behavior", "state": "off", "attributes": {"options": ["off", "on", "toggle", "previous"], "icon": "mdi:power-settings", "friendly_name": "客廳左吊燈 Power-on behavior"}, "last_changed": "2025-08-29T15:55:53.527680+00:00", "last_reported": "2025-08-29T15:55:53.527680+00:00", "last_updated": "2025-08-29T15:55:53.527680+00:00", "context": {"id": "01K3V8NR9Q94SAKMGDM3VZAZ0Z", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.ke_ting_you_diao_deng_color_power_on_behavior", "state": "unknown", "attributes": {"options": ["initial", "previous", "customized"], "icon": "mdi:palette", "friendly_name": "客廳右吊燈 Color power on behavior"}, "last_changed": "2025-08-29T15:55:53.527689+00:00", "last_reported": "2025-08-29T15:55:53.527689+00:00", "last_updated": "2025-08-29T15:55:53.527689+00:00", "context": {"id": "01K3V8NR9Q1NKVP4A3JXK3FMS6", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.can_ting_zuo_kan_deng_color_power_on_behavior", "state": "unknown", "attributes": {"options": ["initial", "previous", "customized"], "icon": "mdi:palette", "friendly_name": "餐廳左崁燈 Color power on behavior"}, "last_changed": "2025-08-29T15:55:53.527699+00:00", "last_reported": "2025-08-29T15:55:53.527699+00:00", "last_updated": "2025-08-29T15:55:53.527699+00:00", "context": {"id": "01K3V8NR9Q556MFP05BXDDW3B2", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.ke_ting_zuo_xia_kan_deng_color_power_on_behavior", "state": "unknown", "attributes": {"options": ["initial", "previous", "customized"], "icon": "mdi:palette", "friendly_name": "客廳左下崁燈 Color power on behavior"}, "last_changed": "2025-08-29T15:55:53.527707+00:00", "last_reported": "2025-08-29T15:55:53.527707+00:00", "last_updated": "2025-08-29T15:55:53.527707+00:00", "context": {"id": "01K3V8NR9QKVBBEHZ0R6FCT8X9", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.ke_ting_zuo_shang_kan_deng_color_power_on_behavior", "state": "unknown", "attributes": {"options": ["initial", "previous", "customized"], "icon": "mdi:palette", "friendly_name": "客廳左上崁燈 Color power on behavior"}, "last_changed": "2025-08-29T15:55:53.527716+00:00", "last_reported": "2025-08-29T15:55:53.527716+00:00", "last_updated": "2025-08-29T15:55:53.527716+00:00", "context": {"id": "01K3V8NR9Q9KJSJW66SDYSEY6M", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.ke_ting_you_shang_kan_deng_color_power_on_behavior", "state": "unknown", "attributes": {"options": ["initial", "previous", "customized"], "icon": "mdi:palette", "friendly_name": "客廳右上崁燈 Color power on behavior"}, "last_changed": "2025-08-29T15:55:53.527736+00:00", "last_reported": "2025-08-29T15:55:53.527736+00:00", "last_updated": "2025-08-29T15:55:53.527736+00:00", "context": {"id": "01K3V8NR9QDX1ZD36Z2S07VAK1", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.ke_ting_you_xia_kan_deng_color_power_on_behavior", "state": "unknown", "attributes": {"options": ["initial", "previous", "customized"], "icon": "mdi:palette", "friendly_name": "客廳右下崁燈 Color power on behavior"}, "last_changed": "2025-08-29T15:55:53.527746+00:00", "last_reported": "2025-08-29T15:55:53.527746+00:00", "last_updated": "2025-08-29T15:55:53.527746+00:00", "context": {"id": "01K3V8NR9QKYP6W71EW59CA3FF", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.chuang_lian_you_opening_mode", "state": "lift", "attributes": {"options": ["tilt", "lift"], "friendly_name": "窗簾右 Opening mode"}, "last_changed": "2025-08-29T15:55:53.527756+00:00", "last_reported": "2025-08-29T15:55:53.527756+00:00", "last_updated": "2025-08-29T15:55:53.527756+00:00", "context": {"id": "01K3V8NR9QC8BCCBRY0FY32KPF", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.chuang_lian_you_motor_direction", "state": "right", "attributes": {"options": ["left", "right"], "friendly_name": "窗簾右 Motor direction"}, "last_changed": "2025-08-29T15:55:53.527764+00:00", "last_reported": "2025-08-29T15:55:53.527764+00:00", "last_updated": "2025-08-29T15:55:53.527764+00:00", "context": {"id": "01K3V8NR9QA7ZES5WX996JQ4N6", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.chuang_lian_you_set_upper_limit", "state": "stop", "attributes": {"options": ["start", "stop"], "friendly_name": "窗簾右 Set upper limit"}, "last_changed": "2025-08-29T15:55:53.527773+00:00", "last_reported": "2025-08-29T15:55:53.527773+00:00", "last_updated": "2025-08-29T15:55:53.527773+00:00", "context": {"id": "01K3V8NR9Q0C2MC0Q0J1A4HBJC", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.chuang_lian_zuo_opening_mode", "state": "lift", "attributes": {"options": ["tilt", "lift"], "friendly_name": "窗簾左 Opening mode"}, "last_changed": "2025-08-29T15:55:53.527792+00:00", "last_reported": "2025-08-29T15:55:53.527792+00:00", "last_updated": "2025-08-29T15:55:53.527792+00:00", "context": {"id": "01K3V8NR9QJBJFEFZQX1EVTR35", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.chuang_lian_zuo_motor_direction", "state": "right", "attributes": {"options": ["left", "right"], "friendly_name": "窗簾左 Motor direction"}, "last_changed": "2025-08-29T15:55:53.527804+00:00", "last_reported": "2025-08-29T15:55:53.527804+00:00", "last_updated": "2025-08-29T15:55:53.527804+00:00", "context": {"id": "01K3V8NR9QM46B231WNSKQ4CAR", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.chuang_lian_zuo_set_upper_limit", "state": "stop", "attributes": {"options": ["start", "stop"], "friendly_name": "窗簾左 Set upper limit"}, "last_changed": "2025-08-29T15:55:53.527812+00:00", "last_reported": "2025-08-29T15:55:53.527812+00:00", "last_updated": "2025-08-29T15:55:53.527812+00:00", "context": {"id": "01K3V8NR9Q30E9E1SKTMEX2WWP", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.0xa4c138b04b602c88_motion_detection_mode", "state": "only_pir", "attributes": {"options": ["only_pir", "pir_and_radar", "only_radar"], "friendly_name": "0xa4c138b04b602c88 Motion detection mode"}, "last_changed": "2025-08-29T15:55:53.527821+00:00", "last_reported": "2025-08-29T15:55:53.527821+00:00", "last_updated": "2025-08-29T15:55:53.527821+00:00", "context": {"id": "01K3V8NR9QK8M1ASX23FM6Z70X", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.ke_ting_kai_guan_power_on_behavior_l1", "state": "unknown", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "客廳開關 Power-on behavior l1"}, "last_changed": "2025-08-29T15:55:53.527829+00:00", "last_reported": "2025-08-29T15:55:53.527829+00:00", "last_updated": "2025-08-29T15:55:53.527829+00:00", "context": {"id": "01K3V8NR9QA5M8WKZPF6DP6K81", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.ke_ting_kai_guan_power_on_behavior_l2", "state": "unknown", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "客廳開關 Power-on behavior l2"}, "last_changed": "2025-08-29T15:55:53.527838+00:00", "last_reported": "2025-08-29T15:55:53.527838+00:00", "last_updated": "2025-08-29T15:55:53.527838+00:00", "context": {"id": "01K3V8NR9Q9VNK12BFK2C2TT1S", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.ke_ting_kai_guan_power_on_behavior_l3", "state": "unknown", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "客廳開關 Power-on behavior l3"}, "last_changed": "2025-08-29T15:55:53.527847+00:00", "last_reported": "2025-08-29T15:55:53.527847+00:00", "last_updated": "2025-08-29T15:55:53.527847+00:00", "context": {"id": "01K3V8NR9QXR7FBH7QW2D8YC4W", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.ke_ting_kai_guan_indicator_mode", "state": "unknown", "attributes": {"options": ["off", "off/on", "on/off", "on"], "friendly_name": "客廳開關 Indicator mode"}, "last_changed": "2025-08-29T15:55:53.527855+00:00", "last_reported": "2025-08-29T15:55:53.527855+00:00", "last_updated": "2025-08-29T15:55:53.527855+00:00", "context": {"id": "01K3V8NR9QWRHDJWFDKFWGDQ6F", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.2f_lou_ti_kai_guan_power_on_behavior_l1", "state": "unknown", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "2F 樓梯開關 Power-on behavior l1"}, "last_changed": "2025-08-29T15:55:53.527865+00:00", "last_reported": "2025-08-29T15:55:53.527865+00:00", "last_updated": "2025-08-29T15:55:53.527865+00:00", "context": {"id": "01K3V8NR9Q3AJJY8BREE61Q5Q6", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.2f_lou_ti_kai_guan_power_on_behavior_l2", "state": "unknown", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "2F 樓梯開關 Power-on behavior l2"}, "last_changed": "2025-08-29T15:55:53.527874+00:00", "last_reported": "2025-08-29T15:55:53.527874+00:00", "last_updated": "2025-08-29T15:55:53.527874+00:00", "context": {"id": "01K3V8NR9Q4S4ZX7QT7C8Y411P", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.2f_lou_ti_kai_guan_power_on_behavior_l3", "state": "unknown", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "2F 樓梯開關 Power-on behavior l3"}, "last_changed": "2025-08-29T15:55:53.527887+00:00", "last_reported": "2025-08-29T15:55:53.527887+00:00", "last_updated": "2025-08-29T15:55:53.527887+00:00", "context": {"id": "01K3V8NR9QNTTM2T9FYR1Q82AX", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.2f_lou_ti_kai_guan_indicator_mode", "state": "unknown", "attributes": {"options": ["off", "off/on", "on/off", "on"], "friendly_name": "2F 樓梯開關 Indicator mode"}, "last_changed": "2025-08-29T15:55:53.527897+00:00", "last_reported": "2025-08-29T15:55:53.527897+00:00", "last_updated": "2025-08-29T15:55:53.527897+00:00", "context": {"id": "01K3V8NR9QBXJXS3812T8X4YPH", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.chu_fang_kai_guan_power_on_behavior_l1", "state": "unknown", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "廚房開關 Power-on behavior l1"}, "last_changed": "2025-08-29T15:55:53.527905+00:00", "last_reported": "2025-08-29T15:55:53.527905+00:00", "last_updated": "2025-08-29T15:55:53.527905+00:00", "context": {"id": "01K3V8NR9Q1KCEVDGP37C8XAHH", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.chu_fang_kai_guan_power_on_behavior_l2", "state": "unknown", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "廚房開關 Power-on behavior l2"}, "last_changed": "2025-08-29T15:55:53.527914+00:00", "last_reported": "2025-08-29T15:55:53.527914+00:00", "last_updated": "2025-08-29T15:55:53.527914+00:00", "context": {"id": "01K3V8NR9Q3M760PG367N846MA", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.chu_fang_kai_guan_power_on_behavior_l3", "state": "unknown", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "廚房開關 Power-on behavior l3"}, "last_changed": "2025-08-29T15:55:53.527922+00:00", "last_reported": "2025-08-29T15:55:53.527922+00:00", "last_updated": "2025-08-29T15:55:53.527922+00:00", "context": {"id": "01K3V8NR9QXE5ZSZHMHS97XHQ6", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.chu_fang_kai_guan_indicator_mode", "state": "unknown", "attributes": {"options": ["off", "off/on", "on/off", "on"], "friendly_name": "廚房開關 Indicator mode"}, "last_changed": "2025-08-29T15:55:53.527930+00:00", "last_reported": "2025-08-29T15:55:53.527930+00:00", "last_updated": "2025-08-29T15:55:53.527930+00:00", "context": {"id": "01K3V8NR9QMT35PCVKERACCPEN", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.xuan_guan_kai_guan_power_on_behavior_l1", "state": "unknown", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "玄關開關 Power-on behavior l1"}, "last_changed": "2025-08-29T15:55:53.527939+00:00", "last_reported": "2025-08-29T15:55:53.527939+00:00", "last_updated": "2025-08-29T15:55:53.527939+00:00", "context": {"id": "01K3V8NR9QKQ3NVY5HNWS0NECM", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.xuan_guan_kai_guan_power_on_behavior_l2", "state": "unknown", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "玄關開關 Power-on behavior l2"}, "last_changed": "2025-08-29T15:55:53.527948+00:00", "last_reported": "2025-08-29T15:55:53.527948+00:00", "last_updated": "2025-08-29T15:55:53.527948+00:00", "context": {"id": "01K3V8NR9QFPPGDWTRCW43ZDJF", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.xuan_guan_kai_guan_power_on_behavior_l3", "state": "unknown", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "玄關開關 Power-on behavior l3"}, "last_changed": "2025-08-29T15:55:53.527957+00:00", "last_reported": "2025-08-29T15:55:53.527957+00:00", "last_updated": "2025-08-29T15:55:53.527957+00:00", "context": {"id": "01K3V8NR9QJ1NXP14XC7EK58X2", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.xuan_guan_kai_guan_indicator_mode", "state": "unknown", "attributes": {"options": ["off", "off/on", "on/off", "on"], "friendly_name": "玄關開關 Indicator mode"}, "last_changed": "2025-08-29T15:55:53.527966+00:00", "last_reported": "2025-08-29T15:55:53.527966+00:00", "last_updated": "2025-08-29T15:55:53.527966+00:00", "context": {"id": "01K3V8NR9QWXZWBGR53ZS913AT", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.3f_lou_ti_kai_guan_power_on_behavior_l1", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "3F 樓梯開關 Power-on behavior l1"}, "last_changed": "2025-08-29T15:55:53.527975+00:00", "last_reported": "2025-08-29T15:55:53.527975+00:00", "last_updated": "2025-08-29T15:55:53.527975+00:00", "context": {"id": "01K3V8NR9QMP09T71XXRJNQQ16", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.3f_lou_ti_kai_guan_power_on_behavior_l2", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "3F 樓梯開關 Power-on behavior l2"}, "last_changed": "2025-08-29T15:55:53.527984+00:00", "last_reported": "2025-08-29T15:55:53.527984+00:00", "last_updated": "2025-08-29T15:55:53.527984+00:00", "context": {"id": "01K3V8NR9Q3SQMGXMWT5ZKGWQH", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.3f_lou_ti_kai_guan_power_on_behavior_l3", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "3F 樓梯開關 Power-on behavior l3"}, "last_changed": "2025-08-29T15:55:53.527993+00:00", "last_reported": "2025-08-29T15:55:53.527993+00:00", "last_updated": "2025-08-29T15:55:53.527993+00:00", "context": {"id": "01K3V8NR9QFKMWDGXPDZPRNN3S", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "select.3f_lou_ti_kai_guan_indicator_mode", "state": "on/off", "attributes": {"options": ["off", "off/on", "on/off", "on"], "friendly_name": "3F 樓梯開關 Indicator mode"}, "last_changed": "2025-08-29T15:55:53.528006+00:00", "last_reported": "2025-08-29T15:55:53.528006+00:00", "last_updated": "2025-08-29T15:55:53.528006+00:00", "context": {"id": "01K3V8NR9RQVCMKXA8PG2YA19K", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.zigbee2mqtt_bridge_version", "state": "2.6.0", "attributes": {"icon": "mdi:zigbee", "friendly_name": "Zigbee2MQTT Bridge Version"}, "last_changed": "2025-08-29T15:55:53.528039+00:00", "last_reported": "2025-08-29T15:55:53.528039+00:00", "last_updated": "2025-08-29T15:55:53.528039+00:00", "context": {"id": "01K3V8NR9R0XHMQWWNPV8HGKMH", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "2.6.0", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.xuan_guan_ren_zai_gan_ying_illuminance", "state": "1896", "attributes": {"state_class": "measurement", "unit_of_measurement": "lx", "device_class": "illuminance", "friendly_name": "玄關人在感應 Illuminance"}, "last_changed": "2025-08-29T15:55:53.528063+00:00", "last_reported": "2025-08-29T15:55:53.528063+00:00", "last_updated": "2025-08-29T15:55:53.528063+00:00", "context": {"id": "01K3V8NR9RYXJP1N91VZ29A5HX", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "1896", "native_unit_of_measurement": "lx"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.xuan_guan_ren_zai_gan_ying_motion_state", "state": "large", "attributes": {"friendly_name": "玄關人在感應 Motion state"}, "last_changed": "2025-08-29T15:55:53.528076+00:00", "last_reported": "2025-08-29T15:55:53.528076+00:00", "last_updated": "2025-08-29T15:55:53.528076+00:00", "context": {"id": "01K3V8NR9RZ8DA6TD9CM1RXG19", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "large", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.xuan_guan_ren_zai_gan_ying_target_distance", "state": "unknown", "attributes": {"unit_of_measurement": "m", "friendly_name": "玄關人在感應 Target distance"}, "last_changed": "2025-08-29T15:55:53.528088+00:00", "last_reported": "2025-08-29T15:55:53.528088+00:00", "last_updated": "2025-08-29T15:55:53.528088+00:00", "context": {"id": "01K3V8NR9R3F3CCNAHW8C2M8HB", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": null, "native_unit_of_measurement": "m"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.xuan_guan_dong_zuo_gan_ying_illumination", "state": "dim", "attributes": {"friendly_name": "玄關動作感應 Illumination"}, "last_changed": "2025-08-29T15:55:53.528098+00:00", "last_reported": "2025-08-29T15:55:53.528098+00:00", "last_updated": "2025-08-29T15:55:53.528098+00:00", "context": {"id": "01K3V8NR9RXN2X7RM1Z6MHW0H0", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "dim", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.xuan_guan_dong_zuo_gan_ying_battery", "state": "100", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "玄關動作感應 Battery"}, "last_changed": "2025-08-29T15:55:53.528111+00:00", "last_reported": "2025-08-29T15:55:53.528111+00:00", "last_updated": "2025-08-29T15:55:53.528111+00:00", "context": {"id": "01K3V8NR9RN88Z07FJV4WRKQCA", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "100", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.xuan_guan_dong_zuo_gan_ying_voltage", "state": "3000", "attributes": {"state_class": "measurement", "unit_of_measurement": "mV", "device_class": "voltage", "friendly_name": "玄關動作感應 Voltage"}, "last_changed": "2025-08-29T15:55:53.528142+00:00", "last_reported": "2025-08-29T15:55:53.528142+00:00", "last_updated": "2025-08-29T15:55:53.528142+00:00", "context": {"id": "01K3V8NR9R163WRAQV1DZVWRRH", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "3000", "native_unit_of_measurement": "mV"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.xiao_mi_wu_xian_kai_guan_battery", "state": "100", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "小米無線開關 Battery"}, "last_changed": "2025-08-29T15:55:53.528162+00:00", "last_reported": "2025-08-29T15:55:53.528162+00:00", "last_updated": "2025-08-29T15:55:53.528162+00:00", "context": {"id": "01K3V8NR9RNR93FV8DYE11NDC3", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "100", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.xiao_mi_wu_xian_kai_guan_voltage", "state": "3062", "attributes": {"state_class": "measurement", "unit_of_measurement": "mV", "device_class": "voltage", "friendly_name": "小米無線開關 Voltage"}, "last_changed": "2025-08-29T15:55:53.528174+00:00", "last_reported": "2025-08-29T15:55:53.528174+00:00", "last_updated": "2025-08-29T15:55:53.528174+00:00", "context": {"id": "01K3V8NR9RMKKT2QZJAF5BJ16F", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "3062", "native_unit_of_measurement": "mV"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.xin_xiang_men_gan_ying_illuminance", "state": "0", "attributes": {"state_class": "measurement", "unit_of_measurement": "lx", "device_class": "illuminance", "friendly_name": "信箱門感應 Illuminance"}, "last_changed": "2025-08-29T15:55:53.528186+00:00", "last_reported": "2025-08-29T15:55:53.528186+00:00", "last_updated": "2025-08-29T15:55:53.528186+00:00", "context": {"id": "01K3V8NR9REJC833M8H81RVRR1", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "0", "native_unit_of_measurement": "lx"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.xin_xiang_men_gan_ying_battery", "state": "100", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "信箱門感應 Battery"}, "last_changed": "2025-08-29T15:55:53.528198+00:00", "last_reported": "2025-08-29T15:55:53.528198+00:00", "last_updated": "2025-08-29T15:55:53.528198+00:00", "context": {"id": "01K3V8NR9R0YW315Q4C708EDQB", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "100", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.che_ku_men_gan_ying_illuminance", "state": "0", "attributes": {"state_class": "measurement", "unit_of_measurement": "lx", "device_class": "illuminance", "friendly_name": "車庫門感應 Illuminance"}, "last_changed": "2025-08-29T15:55:53.528210+00:00", "last_reported": "2025-08-29T15:55:53.528210+00:00", "last_updated": "2025-08-29T15:55:53.528210+00:00", "context": {"id": "01K3V8NR9R3FXB1305SPRNQCAS", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "0", "native_unit_of_measurement": "lx"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.che_ku_men_gan_ying_battery", "state": "100", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "車庫門感應 Battery"}, "last_changed": "2025-08-29T15:55:53.528221+00:00", "last_reported": "2025-08-29T15:55:53.528221+00:00", "last_updated": "2025-08-29T15:55:53.528221+00:00", "context": {"id": "01K3V8NR9R8PSVRF0DPHKYGWPP", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "100", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.che_ku_ce_men_gan_ying_illuminance", "state": "0", "attributes": {"state_class": "measurement", "unit_of_measurement": "lx", "device_class": "illuminance", "friendly_name": "車庫側門感應 Illuminance"}, "last_changed": "2025-08-29T15:55:53.528232+00:00", "last_reported": "2025-08-29T15:55:53.528232+00:00", "last_updated": "2025-08-29T15:55:53.528232+00:00", "context": {"id": "01K3V8NR9RF30G70N5EHG5BZ8Y", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "0", "native_unit_of_measurement": "lx"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.che_ku_ce_men_gan_ying_battery", "state": "100", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "車庫側門感應 Battery"}, "last_changed": "2025-08-29T15:55:53.529081+00:00", "last_reported": "2025-08-29T15:55:53.529081+00:00", "last_updated": "2025-08-29T15:55:53.529081+00:00", "context": {"id": "01K3V8NR9STS5Y8H9B48JYDMDW", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "100", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.xin_xiang_gan_ying_battery", "state": "100", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "信箱感應 Battery"}, "last_changed": "2025-08-29T15:55:53.529102+00:00", "last_reported": "2025-08-29T15:55:53.529102+00:00", "last_updated": "2025-08-29T15:55:53.529102+00:00", "context": {"id": "01K3V8NR9SD0NVK6CJJHPD5A0Q", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "100", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.xin_xiang_gan_ying_device_temperature", "state": "35", "attributes": {"state_class": "measurement", "unit_of_measurement": "°C", "device_class": "temperature", "friendly_name": "信箱感應 Temperature"}, "last_changed": "2025-08-29T15:55:53.529118+00:00", "last_reported": "2025-08-29T15:55:53.529118+00:00", "last_updated": "2025-08-29T15:55:53.529118+00:00", "context": {"id": "01K3V8NR9SP82BJZN94ENEGT76", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "35", "native_unit_of_measurement": "°C"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.xin_xiang_gan_ying_voltage", "state": "3045", "attributes": {"state_class": "measurement", "unit_of_measurement": "mV", "device_class": "voltage", "friendly_name": "信箱感應 Voltage"}, "last_changed": "2025-08-29T15:55:53.529130+00:00", "last_reported": "2025-08-29T15:55:53.529130+00:00", "last_updated": "2025-08-29T15:55:53.529130+00:00", "context": {"id": "01K3V8NR9SWT7KV5B4N7P3TG8X", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "3045", "native_unit_of_measurement": "mV"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.3f_dong_zuo_gan_ying_illumination", "state": "dim", "attributes": {"friendly_name": "3F 動作感應 Illumination"}, "last_changed": "2025-08-29T15:55:53.529142+00:00", "last_reported": "2025-08-29T15:55:53.529142+00:00", "last_updated": "2025-08-29T15:55:53.529142+00:00", "context": {"id": "01K3V8NR9SBXHH3GMMTKWEK485", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "dim", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.3f_dong_zuo_gan_ying_battery", "state": "100", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "3F 動作感應 Battery"}, "last_changed": "2025-08-29T15:55:53.529154+00:00", "last_reported": "2025-08-29T15:55:53.529154+00:00", "last_updated": "2025-08-29T15:55:53.529154+00:00", "context": {"id": "01K3V8NR9S97Y0ZHYXCT1B43G0", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "100", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.3f_dong_zuo_gan_ying_voltage", "state": "3000", "attributes": {"state_class": "measurement", "unit_of_measurement": "mV", "device_class": "voltage", "friendly_name": "3F 動作感應 Voltage"}, "last_changed": "2025-08-29T15:55:53.529165+00:00", "last_reported": "2025-08-29T15:55:53.529165+00:00", "last_updated": "2025-08-29T15:55:53.529165+00:00", "context": {"id": "01K3V8NR9S3YM4G0V41MAQ21EJ", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "3000", "native_unit_of_measurement": "mV"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.3f_ren_zai_gan_ying_illuminance", "state": "46", "attributes": {"state_class": "measurement", "unit_of_measurement": "lx", "device_class": "illuminance", "friendly_name": "3F 人在感應 Illuminance"}, "last_changed": "2025-08-29T15:55:53.529179+00:00", "last_reported": "2025-08-29T15:55:53.529179+00:00", "last_updated": "2025-08-29T15:55:53.529179+00:00", "context": {"id": "01K3V8NR9SCQHW08Y1HVNAMVAA", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "46", "native_unit_of_measurement": "lx"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.3f_ren_zai_gan_ying_motion_state", "state": "none", "attributes": {"friendly_name": "3F 人在感應 Motion state"}, "last_changed": "2025-08-29T15:55:53.529192+00:00", "last_reported": "2025-08-29T15:55:53.529192+00:00", "last_updated": "2025-08-29T15:55:53.529192+00:00", "context": {"id": "01K3V8NR9SH26E87BWXC1CHZR1", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "none", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.3f_ren_zai_gan_ying_target_distance", "state": "unknown", "attributes": {"unit_of_measurement": "m", "friendly_name": "3F 人在感應 Target distance"}, "last_changed": "2025-08-29T15:55:53.529203+00:00", "last_reported": "2025-08-29T15:55:53.529203+00:00", "last_updated": "2025-08-29T15:55:53.529203+00:00", "context": {"id": "01K3V8NR9SE2H03EKNKX0TYW67", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": null, "native_unit_of_measurement": "m"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.chuang_lian_you_work_state", "state": "standby", "attributes": {"friendly_name": "窗簾右 Work state"}, "last_changed": "2025-08-29T15:55:53.529226+00:00", "last_reported": "2025-08-29T15:55:53.529226+00:00", "last_updated": "2025-08-29T15:55:53.529226+00:00", "context": {"id": "01K3V8NR9SYRDYHR0Z7XC8Y8GX", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "standby", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.chuang_lian_you_battery", "state": "100", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "窗簾右 Battery"}, "last_changed": "2025-08-29T15:55:53.529247+00:00", "last_reported": "2025-08-29T15:55:53.529247+00:00", "last_updated": "2025-08-29T15:55:53.529247+00:00", "context": {"id": "01K3V8NR9SR0MZRNKE8H3B8GMH", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "100", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.chuang_lian_zuo_work_state", "state": "learning", "attributes": {"friendly_name": "窗簾左 Work state"}, "last_changed": "2025-08-29T15:55:53.529259+00:00", "last_reported": "2025-08-29T15:55:53.529259+00:00", "last_updated": "2025-08-29T15:55:53.529259+00:00", "context": {"id": "01K3V8NR9SWBJGS7XJ08820BVX", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "learning", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.chuang_lian_zuo_battery", "state": "50", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "窗簾左 Battery"}, "last_changed": "2025-08-29T15:55:53.529270+00:00", "last_reported": "2025-08-29T15:55:53.529270+00:00", "last_updated": "2025-08-29T15:55:53.529270+00:00", "context": {"id": "01K3V8NR9S2344XYXXYQSDNER4", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "50", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.0xa4c138b04b602c88_motion_state", "state": "small", "attributes": {"friendly_name": "0xa4c138b04b602c88 Motion state"}, "last_changed": "2025-08-29T16:08:00.927354+00:00", "last_reported": "2025-08-29T16:08:00.927354+00:00", "last_updated": "2025-08-29T16:08:00.927354+00:00", "context": {"id": "01K3V9BYMZE5GJ6233REDTXP5F", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "small", "native_unit_of_measurement": null}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.0xa4c138b04b602c88_illuminance", "state": "1489", "attributes": {"state_class": "measurement", "unit_of_measurement": "lx", "device_class": "illuminance", "friendly_name": "0xa4c138b04b602c88 Illuminance"}, "last_changed": "2025-08-29T16:08:56.718311+00:00", "last_reported": "2025-08-29T16:08:56.718311+00:00", "last_updated": "2025-08-29T16:08:56.718311+00:00", "context": {"id": "01K3V9DN4EVE38WE5ED1K0EF0E", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "1489", "native_unit_of_measurement": "lx"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "sensor.0xa4c138b04b602c88_battery", "state": "75", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "0xa4c138b04b602c88 Battery"}, "last_changed": "2025-08-29T15:55:53.529307+00:00", "last_reported": "2025-08-29T15:55:53.529307+00:00", "last_updated": "2025-08-29T15:55:53.529307+00:00", "context": {"id": "01K3V8NR9SEK7Z171BMYA8752P", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "75", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.zigbee2mqtt_bridge_permit_join", "state": "off", "attributes": {"icon": "mdi:human-greeting-proximity", "friendly_name": "Zigbee2MQTT Bridge Permit join"}, "last_changed": "2025-08-29T15:55:53.529324+00:00", "last_reported": "2025-08-29T15:55:53.529324+00:00", "last_updated": "2025-08-29T15:55:53.529324+00:00", "context": {"id": "01K3V8NR9SDR3SHJFZEYMCZ2VP", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.2f_restroom", "state": "off", "attributes": {"friendly_name": "2F 廁所開關"}, "last_changed": "2025-08-29T15:55:53.529335+00:00", "last_reported": "2025-08-29T15:55:53.529335+00:00", "last_updated": "2025-08-29T15:55:53.529335+00:00", "context": {"id": "01K3V8NR9SSKGH3HZM5K9T2CMN", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.3f_wall_lamps", "state": "on", "attributes": {"friendly_name": "3F 壁燈開關"}, "last_changed": "2025-08-29T15:55:53.529347+00:00", "last_reported": "2025-08-29T15:55:53.529347+00:00", "last_updated": "2025-08-29T15:55:53.529347+00:00", "context": {"id": "01K3V8NR9SC4115YNCBP97NT1F", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.xuan_guan_ren_zai_gan_ying_indicator", "state": "on", "attributes": {"friendly_name": "玄關人在感應 Indicator"}, "last_changed": "2025-08-29T15:55:53.529357+00:00", "last_reported": "2025-08-29T15:55:53.529357+00:00", "last_updated": "2025-08-29T15:55:53.529357+00:00", "context": {"id": "01K3V8NR9SF2PMDJC0TM9T502Z", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.garage_washer", "state": "off", "attributes": {"friendly_name": "冰箱燈"}, "last_changed": "2025-08-29T15:55:53.529366+00:00", "last_reported": "2025-08-29T15:55:53.529366+00:00", "last_updated": "2025-08-29T15:55:53.529366+00:00", "context": {"id": "01K3V8NR9SXKGFKG721TECF31J", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.garage", "state": "off", "attributes": {"friendly_name": "車庫燈"}, "last_changed": "2025-08-29T15:55:53.529376+00:00", "last_reported": "2025-08-29T15:55:53.529376+00:00", "last_updated": "2025-08-29T15:55:53.529376+00:00", "context": {"id": "01K3V8NR9SZ4Y0SHMX7ZM1EEQT", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.che_ku_deng_kai_guan_backlight_mode", "state": "on", "attributes": {"friendly_name": "車庫燈開關 Backlight mode"}, "last_changed": "2025-08-29T15:55:53.529385+00:00", "last_reported": "2025-08-29T15:55:53.529385+00:00", "last_updated": "2025-08-29T15:55:53.529385+00:00", "context": {"id": "01K3V8NR9S54JAHTQH2E13KTB4", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.garage_close", "state": "off", "attributes": {"friendly_name": "關"}, "last_changed": "2025-08-29T15:55:53.529393+00:00", "last_reported": "2025-08-29T15:55:53.529393+00:00", "last_updated": "2025-08-29T15:55:53.529393+00:00", "context": {"id": "01K3V8NR9SYG1ETVQ54AEJTS1X", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.garage_open", "state": "off", "attributes": {"friendly_name": "開"}, "last_changed": "2025-08-29T15:55:53.529401+00:00", "last_reported": "2025-08-29T15:55:53.529401+00:00", "last_updated": "2025-08-29T15:55:53.529401+00:00", "context": {"id": "01K3V8NR9SM0TVKVTQZNS0A4KZ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.studio", "state": "on", "attributes": {"friendly_name": "書房燈開關"}, "last_changed": "2025-08-29T15:55:53.529409+00:00", "last_reported": "2025-08-29T15:55:53.529409+00:00", "last_updated": "2025-08-29T15:55:53.529409+00:00", "context": {"id": "01K3V8NR9SFYP4QTWJNFJKSZSN", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.3f_bathroom", "state": "off", "attributes": {"friendly_name": "3F 浴室燈開關"}, "last_changed": "2025-08-29T15:55:53.529419+00:00", "last_reported": "2025-08-29T15:55:53.529419+00:00", "last_updated": "2025-08-29T15:55:53.529419+00:00", "context": {"id": "01K3V8NR9SWRNC6MYBVVAX8PM0", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.dress_room", "state": "off", "attributes": {"friendly_name": "更衣室開關"}, "last_changed": "2025-08-29T15:55:53.529428+00:00", "last_reported": "2025-08-29T15:55:53.529428+00:00", "last_updated": "2025-08-29T15:55:53.529428+00:00", "context": {"id": "01K3V8NR9S57C96HW7KF22SKHK", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.3f_ren_zai_gan_ying_indicator", "state": "on", "attributes": {"friendly_name": "3F 人在感應 Indicator"}, "last_changed": "2025-08-29T15:55:53.529438+00:00", "last_reported": "2025-08-29T15:55:53.529438+00:00", "last_updated": "2025-08-29T15:55:53.529438+00:00", "context": {"id": "01K3V8NR9SMA2S5QESKVGXCGT9", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.4f_lou_ti_kai_guan_l1", "state": "off", "attributes": {"friendly_name": "4F 樓梯開關 L1"}, "last_changed": "2025-08-29T15:55:53.529446+00:00", "last_reported": "2025-08-29T15:55:53.529446+00:00", "last_updated": "2025-08-29T15:55:53.529446+00:00", "context": {"id": "01K3V8NR9S5BTFAWVWZV2CZP53", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.4f_wall_lamp", "state": "on", "attributes": {"friendly_name": "4F 壁燈開關"}, "last_changed": "2025-08-29T15:55:53.529454+00:00", "last_reported": "2025-08-29T15:55:53.529454+00:00", "last_updated": "2025-08-29T15:55:53.529454+00:00", "context": {"id": "01K3V8NR9SCY0M88TXHVGDF0NG", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.4f_lou_ti_kai_guan_backlight_mode", "state": "on", "attributes": {"friendly_name": "4F 樓梯開關 Backlight mode"}, "last_changed": "2025-08-29T15:55:53.529464+00:00", "last_reported": "2025-08-29T15:55:53.529464+00:00", "last_updated": "2025-08-29T15:55:53.529464+00:00", "context": {"id": "01K3V8NR9SGX4ANDXW40MC36DR", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.4f_balcony", "state": "off", "attributes": {"friendly_name": "4F 陽台燈開關"}, "last_changed": "2025-08-29T15:55:53.529472+00:00", "last_reported": "2025-08-29T15:55:53.529472+00:00", "last_updated": "2025-08-29T15:55:53.529472+00:00", "context": {"id": "01K3V8NR9S81QH47H0PG14XV5N", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.bedroom_door", "state": "off", "attributes": {"friendly_name": "臥室燈門邊開關"}, "last_changed": "2025-08-29T15:55:53.529481+00:00", "last_reported": "2025-08-29T15:55:53.529481+00:00", "last_updated": "2025-08-29T15:55:53.529481+00:00", "context": {"id": "01K3V8NR9SKZB5GZMZEM3E9Z3B", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.bedroom_wall", "state": "off", "attributes": {"friendly_name": "臥室燈牆面開關"}, "last_changed": "2025-08-29T15:55:53.529490+00:00", "last_reported": "2025-08-29T15:55:53.529490+00:00", "last_updated": "2025-08-29T15:55:53.529490+00:00", "context": {"id": "01K3V8NR9SSYGS7MM632AHMHBH", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.4f_bathroom", "state": "off", "attributes": {"friendly_name": "4F 浴室燈開關"}, "last_changed": "2025-08-29T15:55:53.529499+00:00", "last_reported": "2025-08-29T15:55:53.529499+00:00", "last_updated": "2025-08-29T15:55:53.529499+00:00", "context": {"id": "01K3V8NR9SVF5YYNKQQTQM47T2", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.4f_yu_shi_deng_do_not_disturb", "state": "off", "attributes": {"friendly_name": "4F 浴室燈 Do not disturb"}, "last_changed": "2025-08-29T15:55:53.529509+00:00", "last_reported": "2025-08-29T15:55:53.529509+00:00", "last_updated": "2025-08-29T15:55:53.529509+00:00", "context": {"id": "01K3V8NR9SDR6QTA48P621GT60", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.can_ting_zhong_jian_kan_deng_do_not_disturb", "state": "on", "attributes": {"friendly_name": "餐廳中間崁燈 Do not disturb"}, "last_changed": "2025-08-29T15:55:53.529517+00:00", "last_reported": "2025-08-29T15:55:53.529517+00:00", "last_updated": "2025-08-29T15:55:53.529517+00:00", "context": {"id": "01K3V8NR9SKAWPW0ZJSWWQNCJ3", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.can_ting_you_kan_deng_do_not_disturb", "state": "on", "attributes": {"friendly_name": "餐廳右崁燈 Do not disturb"}, "last_changed": "2025-08-29T15:55:53.529526+00:00", "last_reported": "2025-08-29T15:55:53.529526+00:00", "last_updated": "2025-08-29T15:55:53.529526+00:00", "context": {"id": "01K3V8NR9SXFPNZXKKW5WJKMQA", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.ke_ting_you_diao_deng_do_not_disturb", "state": "on", "attributes": {"friendly_name": "客廳右吊燈 Do not disturb"}, "last_changed": "2025-08-29T15:55:53.529534+00:00", "last_reported": "2025-08-29T15:55:53.529534+00:00", "last_updated": "2025-08-29T15:55:53.529534+00:00", "context": {"id": "01K3V8NR9SAYYPGQ1T40JJZQMB", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.can_ting_zuo_kan_deng_do_not_disturb", "state": "on", "attributes": {"friendly_name": "餐廳左崁燈 Do not disturb"}, "last_changed": "2025-08-29T15:55:53.529542+00:00", "last_reported": "2025-08-29T15:55:53.529542+00:00", "last_updated": "2025-08-29T15:55:53.529542+00:00", "context": {"id": "01K3V8NR9SKPX8AG05RVEA7E3R", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.ke_ting_zuo_xia_kan_deng_do_not_disturb", "state": "on", "attributes": {"friendly_name": "客廳左下崁燈 Do not disturb"}, "last_changed": "2025-08-29T15:55:53.529558+00:00", "last_reported": "2025-08-29T15:55:53.529558+00:00", "last_updated": "2025-08-29T15:55:53.529558+00:00", "context": {"id": "01K3V8NR9SB3Q84B7B6YD4TJ2F", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.ke_ting_zuo_shang_kan_deng_do_not_disturb", "state": "off", "attributes": {"friendly_name": "客廳左上崁燈 Do not disturb"}, "last_changed": "2025-08-29T15:55:53.529568+00:00", "last_reported": "2025-08-29T15:55:53.529568+00:00", "last_updated": "2025-08-29T15:55:53.529568+00:00", "context": {"id": "01K3V8NR9SDHWMRH37WFWDA8KT", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.ke_ting_you_shang_kan_deng_do_not_disturb", "state": "on", "attributes": {"friendly_name": "客廳右上崁燈 Do not disturb"}, "last_changed": "2025-08-29T15:55:53.529576+00:00", "last_reported": "2025-08-29T15:55:53.529576+00:00", "last_updated": "2025-08-29T15:55:53.529576+00:00", "context": {"id": "01K3V8NR9S6RJ004DABSNWS9YR", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.ke_ting_you_xia_kan_deng_do_not_disturb", "state": "on", "attributes": {"friendly_name": "客廳右下崁燈 Do not disturb"}, "last_changed": "2025-08-29T15:55:53.529584+00:00", "last_reported": "2025-08-29T15:55:53.529584+00:00", "last_updated": "2025-08-29T15:55:53.529584+00:00", "context": {"id": "01K3V8NR9SBB95Y2Y5M3TCPMW7", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.0xa4c138b04b602c88_indicator", "state": "on", "attributes": {"friendly_name": "0xa4c138b04b602c88 Indicator"}, "last_changed": "2025-08-29T15:55:53.529592+00:00", "last_reported": "2025-08-29T15:55:53.529592+00:00", "last_updated": "2025-08-29T15:55:53.529592+00:00", "context": {"id": "01K3V8NR9SX0RAN0KVN8BMEJW2", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.dining", "state": "off", "attributes": {"friendly_name": "餐廳燈開關"}, "last_changed": "2025-08-29T15:55:53.529600+00:00", "last_reported": "2025-08-29T15:55:53.529600+00:00", "last_updated": "2025-08-29T15:55:53.529600+00:00", "context": {"id": "01K3V8NR9SSVTQGP4P56SC73NE", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.ceiling", "state": "off", "attributes": {"friendly_name": "客廳吊燈開關"}, "last_changed": "2025-08-29T15:55:53.529609+00:00", "last_reported": "2025-08-29T15:55:53.529609+00:00", "last_updated": "2025-08-29T15:55:53.529609+00:00", "context": {"id": "01K3V8NR9S9CSHPD985E1SP6FF", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.living_room_downlight", "state": "off", "attributes": {"friendly_name": "客廳嵌燈開關"}, "last_changed": "2025-08-29T15:55:53.529618+00:00", "last_reported": "2025-08-29T15:55:53.529618+00:00", "last_updated": "2025-08-29T15:55:53.529618+00:00", "context": {"id": "01K3V8NR9S488XG7MBBE1GT09F", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.ke_ting_kai_guan_backlight_mode", "state": "unknown", "attributes": {"friendly_name": "客廳開關 Backlight mode"}, "last_changed": "2025-08-29T15:55:53.529627+00:00", "last_reported": "2025-08-29T15:55:53.529627+00:00", "last_updated": "2025-08-29T15:55:53.529627+00:00", "context": {"id": "01K3V8NR9SVWQJCYX75K5001RR", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.map", "state": "off", "attributes": {"friendly_name": "地圖燈開關"}, "last_changed": "2025-08-29T15:55:53.529634+00:00", "last_reported": "2025-08-29T15:55:53.529634+00:00", "last_updated": "2025-08-29T15:55:53.529634+00:00", "context": {"id": "01K3V8NR9STPPYX36CAV0MQ864", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.2f_lou_ti_kai_guan_l2", "state": "off", "attributes": {"friendly_name": "2F 樓梯開關 L2"}, "last_changed": "2025-08-29T15:55:53.529643+00:00", "last_reported": "2025-08-29T15:55:53.529643+00:00", "last_updated": "2025-08-29T15:55:53.529643+00:00", "context": {"id": "01K3V8NR9SW31KSBRXEYFGPKQW", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.2f_landing", "state": "off", "attributes": {"friendly_name": "2F 樓梯間開關"}, "last_changed": "2025-08-29T15:55:53.529651+00:00", "last_reported": "2025-08-29T15:55:53.529651+00:00", "last_updated": "2025-08-29T15:55:53.529651+00:00", "context": {"id": "01K3V8NR9SE2RR3VC03B1G4WAP", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.2f_lou_ti_kai_guan_backlight_mode", "state": "unknown", "attributes": {"friendly_name": "2F 樓梯開關 Backlight mode"}, "last_changed": "2025-08-29T15:55:53.529660+00:00", "last_reported": "2025-08-29T15:55:53.529660+00:00", "last_updated": "2025-08-29T15:55:53.529660+00:00", "context": {"id": "01K3V8NR9SM68BQAD364Q9QV3E", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.bar", "state": "off", "attributes": {"friendly_name": "吧檯燈"}, "last_changed": "2025-08-29T15:55:53.529668+00:00", "last_reported": "2025-08-29T15:55:53.529668+00:00", "last_updated": "2025-08-29T15:55:53.529668+00:00", "context": {"id": "01K3V8NR9SJJGF3FY396ST6CKM", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.kitchen", "state": "off", "attributes": {"friendly_name": "廚房燈"}, "last_changed": "2025-08-29T15:55:53.529676+00:00", "last_reported": "2025-08-29T15:55:53.529676+00:00", "last_updated": "2025-08-29T15:55:53.529676+00:00", "context": {"id": "01K3V8NR9SVW3RNVG5ZG3D5VMB", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.balcony", "state": "off", "attributes": {"friendly_name": "後陽台燈"}, "last_changed": "2025-08-29T15:55:53.529684+00:00", "last_reported": "2025-08-29T15:55:53.529684+00:00", "last_updated": "2025-08-29T15:55:53.529684+00:00", "context": {"id": "01K3V8NR9SGGY1Z7SGJTPTJRYE", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.chu_fang_kai_guan_backlight_mode", "state": "unknown", "attributes": {"friendly_name": "廚房開關 Backlight mode"}, "last_changed": "2025-08-29T15:55:53.529693+00:00", "last_reported": "2025-08-29T15:55:53.529693+00:00", "last_updated": "2025-08-29T15:55:53.529693+00:00", "context": {"id": "01K3V8NR9SSTR9YPBP0553E3CS", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.xuan_guan_kai_guan_l1", "state": "off", "attributes": {"friendly_name": "玄關開關 L1"}, "last_changed": "2025-08-29T15:55:53.529700+00:00", "last_reported": "2025-08-29T15:55:53.529700+00:00", "last_updated": "2025-08-29T15:55:53.529700+00:00", "context": {"id": "01K3V8NR9SNXW0EFWWR33PGKXD", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.entrance", "state": "off", "attributes": {"friendly_name": "玄關燈"}, "last_changed": "2025-08-29T15:55:53.529708+00:00", "last_reported": "2025-08-29T15:55:53.529708+00:00", "last_updated": "2025-08-29T15:55:53.529708+00:00", "context": {"id": "01K3V8NR9SH307CDEG7VWGG0KA", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.door", "state": "on", "attributes": {"friendly_name": "大門燈開關"}, "last_changed": "2025-08-29T15:55:53.529716+00:00", "last_reported": "2025-08-29T15:55:53.529716+00:00", "last_updated": "2025-08-29T15:55:53.529716+00:00", "context": {"id": "01K3V8NR9S6Y0TYCADPDA2V43Y", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.xuan_guan_kai_guan_backlight_mode", "state": "off", "attributes": {"friendly_name": "玄關開關 Backlight mode"}, "last_changed": "2025-08-29T15:55:53.529740+00:00", "last_reported": "2025-08-29T15:55:53.529740+00:00", "last_updated": "2025-08-29T15:55:53.529740+00:00", "context": {"id": "01K3V8NR9SEJ4W2XHQBY60BXM1", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.3f_stairs", "state": "off", "attributes": {"friendly_name": "3F 樓梯燈"}, "last_changed": "2025-08-29T15:55:53.529748+00:00", "last_reported": "2025-08-29T15:55:53.529748+00:00", "last_updated": "2025-08-29T15:55:53.529748+00:00", "context": {"id": "01K3V8NR9SWJ4E89ZPG8VTSA26", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.3f_landing", "state": "off", "attributes": {"friendly_name": "3F 樓梯間"}, "last_changed": "2025-08-29T15:55:53.529757+00:00", "last_reported": "2025-08-29T15:55:53.529757+00:00", "last_updated": "2025-08-29T15:55:53.529757+00:00", "context": {"id": "01K3V8NR9SCQBPSXNG9KQZD1RG", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.2f_stairs", "state": "off", "attributes": {"friendly_name": "2F 樓梯燈"}, "last_changed": "2025-08-29T15:55:53.529765+00:00", "last_reported": "2025-08-29T15:55:53.529765+00:00", "last_updated": "2025-08-29T15:55:53.529765+00:00", "context": {"id": "01K3V8NR9SSP392E61T8MVD6SZ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "switch.3f_lou_ti_kai_guan_backlight_mode", "state": "on", "attributes": {"friendly_name": "3F 樓梯開關 Backlight mode"}, "last_changed": "2025-08-29T15:55:53.529774+00:00", "last_reported": "2025-08-29T15:55:53.529774+00:00", "last_updated": "2025-08-29T15:55:53.529774+00:00", "context": {"id": "01K3V8NR9SSETSHYATG2ZY8JHK", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "update.xuan_guan_dong_zuo_gan_ying", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "8705", "in_progress": false, "latest_version": "8705", "release_summary": null, "release_url": null, "skipped_version": null, "title": null, "update_percentage": null, "device_class": "firmware", "entity_picture": "https://github.com/Koenkk/zigbee2mqtt/raw/master/images/logo.png", "friendly_name": "玄關動作感應", "supported_features": 5}, "last_changed": "2025-08-29T15:55:53.529809+00:00", "last_reported": "2025-08-29T15:55:53.529809+00:00", "last_updated": "2025-08-29T15:55:53.529809+00:00", "context": {"id": "01K3V8NR9SM075PGHVG1H4E2D5", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "update.3f_dong_zuo_gan_ying", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "8705", "in_progress": false, "latest_version": "8705", "release_summary": null, "release_url": null, "skipped_version": null, "title": null, "update_percentage": null, "device_class": "firmware", "entity_picture": "https://github.com/Koenkk/zigbee2mqtt/raw/master/images/logo.png", "friendly_name": "3F 動作感應", "supported_features": 5}, "last_changed": "2025-08-29T15:55:53.529827+00:00", "last_reported": "2025-08-29T15:55:53.529827+00:00", "last_updated": "2025-08-29T15:55:53.529827+00:00", "context": {"id": "01K3V8NR9SP852KB5FSGBB3DFK", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.washtower_remote_start", "state": "unavailable", "attributes": {"device_class": "update", "icon": "mdi:play-circle-outline", "friendly_name": "WashTower Remote Start"}, "last_changed": "2025-08-29T15:04:46.707918+00:00", "last_reported": "2025-08-29T15:10:26.420650+00:00", "last_updated": "2025-08-29T15:04:46.707918+00:00", "context": {"id": "01K3V5R5BKJVY13XJY160ZS0M4", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.washtower_pause", "state": "unavailable", "attributes": {"device_class": "update", "icon": "mdi:pause-circle-outline", "friendly_name": "Wash<PERSON>ow<PERSON> Pause"}, "last_changed": "2025-08-29T15:04:46.707974+00:00", "last_reported": "2025-08-29T15:10:26.420679+00:00", "last_updated": "2025-08-29T15:04:46.707974+00:00", "context": {"id": "01K3V5R5BK2B8F0X7VBWJCBEW3", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.xi_yi_ji_remote_start", "state": "unavailable", "attributes": {"device_class": "update", "icon": "mdi:play-circle-outline", "friendly_name": "洗衣機 Remote Start"}, "last_changed": "2025-08-29T15:04:46.708008+00:00", "last_reported": "2025-08-29T15:10:10.420040+00:00", "last_updated": "2025-08-29T15:04:46.708008+00:00", "context": {"id": "01K3V5R5BM9XZ39BE3DA8W3D1J", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.xi_yi_ji_pause", "state": "unavailable", "attributes": {"device_class": "update", "icon": "mdi:pause-circle-outline", "friendly_name": "洗衣機 Pause"}, "last_changed": "2025-08-29T15:04:46.708042+00:00", "last_reported": "2025-08-29T15:10:10.420162+00:00", "last_updated": "2025-08-29T15:04:46.708042+00:00", "context": {"id": "01K3V5R5BM4ZRB86PR6Z0CQDDA", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.xi_ji_remote_start", "state": "unavailable", "attributes": {"device_class": "update", "icon": "mdi:play-circle-outline", "friendly_name": "洗機 Remote Start"}, "last_changed": "2025-08-29T15:04:46.708073+00:00", "last_reported": "2025-08-29T15:10:25.420498+00:00", "last_updated": "2025-08-29T15:04:46.708073+00:00", "context": {"id": "01K3V5R5BMCGNFKWCDGWWWRHST", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.xi_ji_pause", "state": "unavailable", "attributes": {"device_class": "update", "icon": "mdi:pause-circle-outline", "friendly_name": "洗機 Pause"}, "last_changed": "2025-08-29T15:04:46.708110+00:00", "last_reported": "2025-08-29T15:10:25.420529+00:00", "last_updated": "2025-08-29T15:04:46.708110+00:00", "context": {"id": "01K3V5R5BMMBVZDA2H75C649TW", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.s8_pro_ultra_quan_wu_qing_jie", "state": "unknown", "attributes": {"friendly_name": "S8 Pro Ultra 全屋清潔"}, "last_changed": "2025-08-29T15:04:47.921465+00:00", "last_reported": "2025-08-29T15:04:47.921465+00:00", "last_updated": "2025-08-29T15:04:47.921465+00:00", "context": {"id": "01K3V5R6HHS8NZGQHFXMH1WPQ0", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.dsm_reboot", "state": "unknown", "attributes": {"device_class": "restart", "friendly_name": "DSM Reboot"}, "last_changed": "2025-08-29T15:04:50.423141+00:00", "last_reported": "2025-08-29T15:04:50.423141+00:00", "last_updated": "2025-08-29T15:04:50.423141+00:00", "context": {"id": "01K3V5R8ZQ9ME9WQWKW3Y2NYZ9", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.dsm_shutdown", "state": "unknown", "attributes": {"icon": "mdi:power", "friendly_name": "DSM Shutdown"}, "last_changed": "2025-08-29T15:04:50.423250+00:00", "last_reported": "2025-08-29T15:04:50.423250+00:00", "last_updated": "2025-08-29T15:04:50.423250+00:00", "context": {"id": "01K3V5R8ZQPNF99C2AC99DGCM7", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "update.dsm_dsm_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "DSM 7.2.2-72806 Update 4", "in_progress": false, "latest_version": "DSM 7.2.2-72806 Update 4", "release_summary": null, "release_url": null, "skipped_version": null, "title": "Synology DSM", "update_percentage": null, "attribution": "Data provided by Synology", "entity_picture": "https://brands.home-assistant.io/_/synology_dsm/icon.png", "friendly_name": "DSM DSM update", "supported_features": 0}, "last_changed": "2025-08-29T15:35:08.001089+00:00", "last_reported": "2025-08-29T15:35:08.001089+00:00", "last_updated": "2025-08-29T15:35:08.001089+00:00", "context": {"id": "01K3V7FQZ1QH7PCETX0SGZ35MV", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_c220_bc79_reboot", "state": "unknown", "attributes": {"device_class": "restart", "friendly_name": "Tapo_C220_BC79 Reboot"}, "last_changed": "2025-08-29T15:05:43.577537+00:00", "last_reported": "2025-08-29T15:09:53.836332+00:00", "last_updated": "2025-08-29T15:05:43.577537+00:00", "context": {"id": "01K3V5SWWS8K1QFQ3MY2QYNWGW", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_c220_bc79_format_sd_card", "state": "unknown", "attributes": {"icon": "mdi:eraser", "friendly_name": "Tapo_C220_BC79 Format SD Card"}, "last_changed": "2025-08-29T15:05:43.577687+00:00", "last_reported": "2025-08-29T15:09:53.836461+00:00", "last_updated": "2025-08-29T15:05:43.577687+00:00", "context": {"id": "01K3V5SWWSF4R97X77WA9WS2T7", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_c220_bc79_manual_alarm_start", "state": "unknown", "attributes": {"icon": "mdi:alarm-light-outline", "friendly_name": "Tapo_C220_BC79 Manual Alarm Start"}, "last_changed": "2025-08-29T15:05:43.577801+00:00", "last_reported": "2025-08-29T15:09:53.836568+00:00", "last_updated": "2025-08-29T15:05:43.577801+00:00", "context": {"id": "01K3V5SWWSDFH389EREGK2XVE5", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_c220_bc79_manual_alarm_stop", "state": "unknown", "attributes": {"icon": "mdi:alarm-light-off-outline", "friendly_name": "Tapo_C220_BC79 Manual Alarm Stop"}, "last_changed": "2025-08-29T15:05:43.577898+00:00", "last_reported": "2025-08-29T15:09:53.836672+00:00", "last_updated": "2025-08-29T15:05:43.577898+00:00", "context": {"id": "01K3V5SWWSGM9ABVHP01Y9RCAY", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_c220_bc79_sync_time", "state": "unknown", "attributes": {"clock_data": {"local_time": "2025-08-30 00:10:22", "seconds_from_1970": 1756483822}, "dst_data": {"enabled": "1", "synced": "1", "has_rule": "0"}, "icon": "mdi:timer-sync-outline", "friendly_name": "Tapo_C220_BC79 Sync Time"}, "last_changed": "2025-08-29T15:32:22.419172+00:00", "last_reported": "2025-08-29T16:10:24.702667+00:00", "last_updated": "2025-08-29T16:10:24.702667+00:00", "context": {"id": "01K3V9GB1YHK74TE805YJGKAKD", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_c220_bc79_calibrate", "state": "unknown", "attributes": {"friendly_name": "Tapo_C220_BC79 Calibrate"}, "last_changed": "2025-08-29T15:32:22.419460+00:00", "last_reported": "2025-08-29T15:40:23.988516+00:00", "last_updated": "2025-08-29T15:32:22.419460+00:00", "context": {"id": "01K3V7AP8K9PVNJ5ZRYHMRPDSB", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_c220_bc79_move_up", "state": "unknown", "attributes": {"icon": "mdi:arrow-up", "friendly_name": "Tapo_C220_BC79 Move Up"}, "last_changed": "2025-08-29T15:32:22.419662+00:00", "last_reported": "2025-08-29T15:40:23.988528+00:00", "last_updated": "2025-08-29T15:32:22.419662+00:00", "context": {"id": "01K3V7AP8KVC71JE7Z3SYZV13D", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_c220_bc79_move_down", "state": "unknown", "attributes": {"icon": "mdi:arrow-down", "friendly_name": "Tapo_C220_BC79 Move Down"}, "last_changed": "2025-08-29T15:32:22.419822+00:00", "last_reported": "2025-08-29T15:40:23.988538+00:00", "last_updated": "2025-08-29T15:32:22.419822+00:00", "context": {"id": "01K3V7AP8KGF1993546BQSEMBC", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_c220_bc79_move_right", "state": "unknown", "attributes": {"icon": "mdi:arrow-right", "friendly_name": "Tapo_C220_BC79 Move Right"}, "last_changed": "2025-08-29T15:32:22.419931+00:00", "last_reported": "2025-08-29T15:40:23.988547+00:00", "last_updated": "2025-08-29T15:32:22.419931+00:00", "context": {"id": "01K3V7AP8KTF5MB3W6HW15ZDM8", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_c220_bc79_move_left", "state": "unknown", "attributes": {"icon": "mdi:arrow-left", "friendly_name": "Tapo_C220_BC79 Move Left"}, "last_changed": "2025-08-29T15:32:22.420033+00:00", "last_reported": "2025-08-29T15:40:23.988564+00:00", "last_updated": "2025-08-29T15:32:22.420033+00:00", "context": {"id": "01K3V7AP8M7YW0GRED28EREBKR", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.tapo_c220_bc79_movement_angle", "state": "15", "attributes": {"min": 5, "max": 120, "step": 5, "mode": "auto", "icon": "mdi:map-marker-distance", "friendly_name": "Tapo_C220_BC79 Movement Angle"}, "last_changed": "2025-08-29T15:05:43.579240+00:00", "last_reported": "2025-08-29T15:10:13.602041+00:00", "last_updated": "2025-08-29T15:05:43.579240+00:00", "context": {"id": "01K3V5SWWVEX2S6S6H1GY1S50B", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 120, "native_min_value": 5, "native_step": 5, "native_unit_of_measurement": null, "native_value": 15}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "update.tapo_c220_bc79_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "1.3.2 Build 250610 Rel.61762n", "in_progress": false, "latest_version": "1.3.2 Build 250610 Rel.61762n", "release_summary": null, "release_url": null, "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/tapo_control/icon.png", "friendly_name": "Tapo_C220_BC79 Update", "supported_features": 21}, "last_changed": "2025-08-29T15:05:43.581835+00:00", "last_reported": "2025-08-29T15:10:23.561260+00:00", "last_updated": "2025-08-29T15:05:43.581835+00:00", "context": {"id": "01K3V5SWWX3PWQC9QSDXT7M52X", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_c210_18a8_reboot", "state": "unknown", "attributes": {"device_class": "restart", "friendly_name": "Tapo_C210_18A8 Reboot"}, "last_changed": "2025-08-29T15:05:44.877226+00:00", "last_reported": "2025-08-29T15:10:29.173315+00:00", "last_updated": "2025-08-29T15:05:44.877226+00:00", "context": {"id": "01K3V5SY5DT22Q7BT4YCEEB7GJ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_c210_18a8_format_sd_card", "state": "unknown", "attributes": {"icon": "mdi:eraser", "friendly_name": "Tapo_C210_18A8 Format SD Card"}, "last_changed": "2025-08-29T15:05:44.877479+00:00", "last_reported": "2025-08-29T15:10:29.173520+00:00", "last_updated": "2025-08-29T15:05:44.877479+00:00", "context": {"id": "01K3V5SY5DY7QSF7HHXQR0SYHT", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_c210_18a8_manual_alarm_start", "state": "unknown", "attributes": {"icon": "mdi:alarm-light-outline", "friendly_name": "Tapo_C210_18A8 Manual Alarm Start"}, "last_changed": "2025-08-29T15:05:44.877644+00:00", "last_reported": "2025-08-29T15:10:29.173708+00:00", "last_updated": "2025-08-29T15:05:44.877644+00:00", "context": {"id": "01K3V5SY5D7TZVJNZ83VA3PXNV", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_c210_18a8_manual_alarm_stop", "state": "unknown", "attributes": {"icon": "mdi:alarm-light-off-outline", "friendly_name": "Tapo_C210_18A8 Manual Alarm Stop"}, "last_changed": "2025-08-29T15:05:44.877810+00:00", "last_reported": "2025-08-29T15:10:29.173937+00:00", "last_updated": "2025-08-29T15:05:44.877810+00:00", "context": {"id": "01K3V5SY5DAJVEY9XAEAV49FZ4", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_c210_18a8_sync_time", "state": "unknown", "attributes": {"clock_data": {"local_time": "2025-08-30 00:10:14", "seconds_from_1970": 1756483814}, "dst_data": {"enabled": "0", "synced": "0", "has_rule": "0"}, "icon": "mdi:timer-sync-outline", "friendly_name": "Tapo_C210_18A8 Sync Time"}, "last_changed": "2025-08-29T15:32:45.910550+00:00", "last_reported": "2025-08-29T16:10:16.059523+00:00", "last_updated": "2025-08-29T16:10:16.059523+00:00", "context": {"id": "01K3V9G2KVNDR3H6DZWVMH3Z26", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_c210_18a8_calibrate", "state": "unknown", "attributes": {"friendly_name": "Tapo_C210_18A8 Calibrate"}, "last_changed": "2025-08-29T15:32:45.911025+00:00", "last_reported": "2025-08-29T15:40:15.999108+00:00", "last_updated": "2025-08-29T15:32:45.911025+00:00", "context": {"id": "01K3V7BD6Q1PR31GNF4SZABPF8", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_c210_18a8_move_up", "state": "unknown", "attributes": {"icon": "mdi:arrow-up", "friendly_name": "Tapo_C210_18A8 Move Up"}, "last_changed": "2025-08-29T15:32:45.911185+00:00", "last_reported": "2025-08-29T15:40:15.999163+00:00", "last_updated": "2025-08-29T15:32:45.911185+00:00", "context": {"id": "01K3V7BD6QZJM463NDYK2VTTHM", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_c210_18a8_move_down", "state": "unknown", "attributes": {"icon": "mdi:arrow-down", "friendly_name": "Tapo_C210_18A8 Move Down"}, "last_changed": "2025-08-29T15:32:45.911292+00:00", "last_reported": "2025-08-29T15:40:15.999328+00:00", "last_updated": "2025-08-29T15:32:45.911292+00:00", "context": {"id": "01K3V7BD6QZSN9QN1DHHRF3M09", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_c210_18a8_move_right", "state": "unknown", "attributes": {"icon": "mdi:arrow-right", "friendly_name": "Tapo_C210_18A8 Move Right"}, "last_changed": "2025-08-29T15:32:45.911463+00:00", "last_reported": "2025-08-29T15:40:15.999439+00:00", "last_updated": "2025-08-29T15:32:45.911463+00:00", "context": {"id": "01K3V7BD6Q4PRR7DDCRR9P2KPK", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_c210_18a8_move_left", "state": "unknown", "attributes": {"icon": "mdi:arrow-left", "friendly_name": "Tapo_C210_18A8 Move Left"}, "last_changed": "2025-08-29T15:32:45.911649+00:00", "last_reported": "2025-08-29T15:40:15.999506+00:00", "last_updated": "2025-08-29T15:32:45.911649+00:00", "context": {"id": "01K3V7BD6QEBAQGJYPEGXQ9PVS", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.tapo_c210_18a8_movement_angle", "state": "15", "attributes": {"min": 5, "max": 120, "step": 5, "mode": "auto", "icon": "mdi:map-marker-distance", "friendly_name": "Tapo_C210_18A8 Movement Angle"}, "last_changed": "2025-08-29T15:05:44.879449+00:00", "last_reported": "2025-08-29T15:10:14.894570+00:00", "last_updated": "2025-08-29T15:05:44.879449+00:00", "context": {"id": "01K3V5SY5FZRMG5S8GFHJATXVP", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 120, "native_min_value": 5, "native_step": 5, "native_unit_of_measurement": null, "native_value": 15}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "update.tapo_c210_18a8_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "1.4.7 Build 250625 Rel.58841n", "in_progress": false, "latest_version": "1.4.7 Build 250625 Rel.58841n", "release_summary": null, "release_url": null, "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/tapo_control/icon.png", "friendly_name": "Tapo_C210_18A8 Update", "supported_features": 21}, "last_changed": "2025-08-29T15:05:44.882294+00:00", "last_reported": "2025-08-29T15:10:29.182566+00:00", "last_updated": "2025-08-29T15:05:44.882294+00:00", "context": {"id": "01K3V5SY5JDFCQZ3GYDV3ATZK2", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_camera_reboot", "state": "unknown", "attributes": {"device_class": "restart", "friendly_name": "Tapo_Camera Reboot"}, "last_changed": "2025-08-29T15:15:41.785994+00:00", "last_reported": "2025-08-29T15:15:41.785994+00:00", "last_updated": "2025-08-29T15:15:41.785994+00:00", "context": {"id": "01K3V6C52SN6NS7D7FNBK0J01G", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_camera_format_sd_card", "state": "unknown", "attributes": {"icon": "mdi:eraser", "friendly_name": "Tapo_Camera Format SD Card"}, "last_changed": "2025-08-29T15:15:41.786298+00:00", "last_reported": "2025-08-29T15:15:41.786298+00:00", "last_updated": "2025-08-29T15:15:41.786298+00:00", "context": {"id": "01K3V6C52TXYB7Y2E1F03EMT8C", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_camera_manual_alarm_start", "state": "unknown", "attributes": {"icon": "mdi:alarm-light-outline", "friendly_name": "Tapo_Camera Manual Alarm Start"}, "last_changed": "2025-08-29T15:15:41.786473+00:00", "last_reported": "2025-08-29T15:15:41.786473+00:00", "last_updated": "2025-08-29T15:15:41.786473+00:00", "context": {"id": "01K3V6C52TF9DR8QTJTCZ1HKZQ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_camera_manual_alarm_stop", "state": "unknown", "attributes": {"icon": "mdi:alarm-light-off-outline", "friendly_name": "Tapo_Camera Manual Alarm Stop"}, "last_changed": "2025-08-29T15:15:41.786672+00:00", "last_reported": "2025-08-29T15:15:41.786672+00:00", "last_updated": "2025-08-29T15:15:41.786672+00:00", "context": {"id": "01K3V6C52TRKP8Z3SJ4QGFX8D6", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_camera_sync_time", "state": "unknown", "attributes": {"clock_data": {"local_time": "2025-08-30 00:10:11", "seconds_from_1970": 1756483811}, "dst_data": {"enabled": "0", "synced": "0", "has_rule": "0"}, "icon": "mdi:timer-sync-outline", "friendly_name": "Tapo_Camera Sync Time"}, "last_changed": "2025-08-29T15:31:53.991320+00:00", "last_reported": "2025-08-29T16:10:12.892278+00:00", "last_updated": "2025-08-29T16:10:12.892278+00:00", "context": {"id": "01K3V9FZGWZ4M6Z1E65M5MKAT7", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_camera_calibrate", "state": "unknown", "attributes": {"friendly_name": "Tapo_Camera Calibrate"}, "last_changed": "2025-08-29T15:31:53.991626+00:00", "last_reported": "2025-08-29T15:40:12.831316+00:00", "last_updated": "2025-08-29T15:31:53.991626+00:00", "context": {"id": "01K3V79TG7ZT5YA418EBBW499D", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_camera_move_up", "state": "unknown", "attributes": {"icon": "mdi:arrow-up", "friendly_name": "Tapo_Camera Move Up"}, "last_changed": "2025-08-29T15:31:53.991827+00:00", "last_reported": "2025-08-29T15:40:12.831343+00:00", "last_updated": "2025-08-29T15:31:53.991827+00:00", "context": {"id": "01K3V79TG7ZPP3K0FMHCQXE2BV", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_camera_move_down", "state": "unknown", "attributes": {"icon": "mdi:arrow-down", "friendly_name": "Tapo_Camera Move Down"}, "last_changed": "2025-08-29T15:31:53.991993+00:00", "last_reported": "2025-08-29T15:40:12.831367+00:00", "last_updated": "2025-08-29T15:31:53.991993+00:00", "context": {"id": "01K3V79TG756YY4NVHJ6C0SWB8", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_camera_move_right", "state": "unknown", "attributes": {"icon": "mdi:arrow-right", "friendly_name": "Tapo_Camera Move Right"}, "last_changed": "2025-08-29T15:31:53.992123+00:00", "last_reported": "2025-08-29T15:40:12.831382+00:00", "last_updated": "2025-08-29T15:31:53.992123+00:00", "context": {"id": "01K3V79TG8GFZ95ZFX8F4WR2YP", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.tapo_camera_move_left", "state": "unknown", "attributes": {"icon": "mdi:arrow-left", "friendly_name": "Tapo_Camera Move Left"}, "last_changed": "2025-08-29T15:31:53.992244+00:00", "last_reported": "2025-08-29T15:40:12.831410+00:00", "last_updated": "2025-08-29T15:31:53.992244+00:00", "context": {"id": "01K3V79TG806WKP1Y0MJ7DREYC", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "number.tapo_camera_movement_angle", "state": "15", "attributes": {"min": 5, "max": 120, "step": 5, "mode": "auto", "icon": "mdi:map-marker-distance", "friendly_name": "Tapo_Camera Movement Angle"}, "last_changed": "2025-08-29T15:15:41.788649+00:00", "last_reported": "2025-08-29T15:15:41.788649+00:00", "last_updated": "2025-08-29T15:15:41.788649+00:00", "context": {"id": "01K3V6C52WGG7G8VKP40BT1BQY", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 120, "native_min_value": 5, "native_step": 5, "native_unit_of_measurement": null, "native_value": 15}, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "update.tapo_camera_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "1.4.7 Build 250625 Rel.58841n", "in_progress": false, "latest_version": "1.4.7 Build 250625 Rel.58841n", "release_summary": null, "release_url": null, "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/tapo_control/icon.png", "friendly_name": "Tapo_Camera Update", "supported_features": 21}, "last_changed": "2025-08-29T15:15:41.793876+00:00", "last_reported": "2025-08-29T15:15:41.793876+00:00", "last_updated": "2025-08-29T15:15:41.793876+00:00", "context": {"id": "01K3V6C531K5BME2H3SKGW6MBX", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T16:10:29.217576+00:00"}, {"state": {"entity_id": "button.lao_ya_hao_horn", "state": "unknown", "attributes": {"attribution": "Data provided by Tesla", "icon": "mdi:bullhorn", "friendly_name": "老鴨號 Horn"}, "last_changed": "2025-08-29T14:59:53.845565+00:00", "last_reported": "2025-08-29T14:59:54.390024+00:00", "last_updated": "2025-08-29T14:59:54.390024+00:00", "context": {"id": "01K3V5F7WPW79PD5953EMWWWE9", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T14:59:59.635418+00:00"}, {"state": {"entity_id": "button.lao_ya_hao_flash_lights", "state": "unknown", "attributes": {"attribution": "Data provided by Tesla", "icon": "mdi:car-light-high", "friendly_name": "老鴨號 Flash lights"}, "last_changed": "2025-08-29T14:59:53.845647+00:00", "last_reported": "2025-08-29T14:59:54.390038+00:00", "last_updated": "2025-08-29T14:59:54.390038+00:00", "context": {"id": "01K3V5F7WP5XWM80Y9MA1YDHT2", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T14:59:59.635418+00:00"}, {"state": {"entity_id": "button.lao_ya_hao_wake_up", "state": "unknown", "attributes": {"attribution": "Data provided by Tesla", "icon": "mdi:moon-waning-crescent", "friendly_name": "老鴨號 Wake up"}, "last_changed": "2025-08-29T14:59:53.845706+00:00", "last_reported": "2025-08-29T14:59:54.390051+00:00", "last_updated": "2025-08-29T14:59:54.390051+00:00", "context": {"id": "01K3V5F7WP0PK80FZZETQ2CGEN", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T14:59:59.635418+00:00"}, {"state": {"entity_id": "button.lao_ya_hao_force_data_update", "state": "2025-08-27T09:00:56.011094+00:00", "attributes": {"attribution": "Data provided by Tesla", "icon": "mdi:database-sync", "friendly_name": "老鴨號 Force data update"}, "last_changed": "2025-08-29T14:59:53.845773+00:00", "last_reported": "2025-08-29T14:59:54.390063+00:00", "last_updated": "2025-08-29T14:59:54.390063+00:00", "context": {"id": "01K3V5F7WPWDNB74QVX66D6F8F", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T14:59:59.635418+00:00"}, {"state": {"entity_id": "button.lao_ya_hao_remote_start", "state": "unknown", "attributes": {"attribution": "Data provided by Tesla", "icon": "mdi:power", "friendly_name": "老鴨號 Remote start"}, "last_changed": "2025-08-29T14:59:53.845852+00:00", "last_reported": "2025-08-29T14:59:54.390077+00:00", "last_updated": "2025-08-29T14:59:54.390077+00:00", "context": {"id": "01K3V5F7WPRA9XBY2K1D0Z16AD", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T14:59:59.635418+00:00"}, {"state": {"entity_id": "update.lao_ya_hao_software_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "2025.26.7", "in_progress": false, "latest_version": "2025.26.7", "release_summary": null, "release_url": "https://www.notateslaapp.com/software-updates/version/2025.26.7/release-notes", "skipped_version": null, "title": null, "update_percentage": null, "attribution": "Data provided by Tesla", "entity_picture": "https://brands.home-assistant.io/_/tesla_custom/icon.png", "friendly_name": "老鴨號 Software update", "supported_features": 4}, "last_changed": "2025-08-29T14:59:53.846638+00:00", "last_reported": "2025-08-29T14:59:54.390239+00:00", "last_updated": "2025-08-29T14:59:54.390239+00:00", "context": {"id": "01K3V5F7WPQBXK3P8K913BWGD4", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-29T14:59:59.635418+00:00"}, {"state": {"entity_id": "event.hong_yi_ji_error", "state": "unknown", "attributes": {"event_types": ["empty_water_alert_error", "high_temperature_detection_error", "door_sensor_error", "door_lock_error", "temperature_sensor_error", "compressor_error", "no_filter_error", "high_power_supply_error", "motor_lock_error", "drainmotor_error", "door_open_error"], "event_type": null, "friendly_name": "烘衣機 Error"}, "last_changed": "2025-08-29T14:59:53.926859+00:00", "last_reported": "2025-08-29T14:59:53.926859+00:00", "last_updated": "2025-08-29T14:59:53.926859+00:00", "context": {"id": "01K3V5F7E6NN76NY5ZC18P9Q62", "parent_id": null, "user_id": null}}, "extra_data": {"last_event_type": null, "last_event_attributes": null}, "last_seen": "2025-08-29T14:59:59.635418+00:00"}, {"state": {"entity_id": "event.hong_yi_ji_notification", "state": "2025-08-23T13:59:13.778+00:00", "attributes": {"event_types": ["drying_failed", "drying_is_complete"], "event_type": "drying_is_complete", "friendly_name": "烘衣機 Notification"}, "last_changed": "2025-08-29T14:59:53.926953+00:00", "last_reported": "2025-08-29T14:59:53.926953+00:00", "last_updated": "2025-08-29T14:59:53.926953+00:00", "context": {"id": "01K3V5F7E6427QNM0TGNK6BG6K", "parent_id": null, "user_id": null}}, "extra_data": {"last_event_type": "drying_is_complete", "last_event_attributes": null}, "last_seen": "2025-08-29T14:59:59.635418+00:00"}, {"state": {"entity_id": "event.xi_yi_ji_error", "state": "2025-08-12T14:04:13.841+00:00", "attributes": {"event_types": ["power_fail_error", "water_level_sensor_error", "locked_motor_error", "water_drain_error", "door_open_error", "water_supply_error", "unable_to_lock_error", "overfill_error", "temperature_sensor_error", "out_of_balance_error"], "event_type": "door_open_error", "friendly_name": "洗衣機 Error"}, "last_changed": "2025-08-29T14:59:53.927023+00:00", "last_reported": "2025-08-29T14:59:53.927023+00:00", "last_updated": "2025-08-29T14:59:53.927023+00:00", "context": {"id": "01K3V5F7E7GZT1QW4SSJWEQVJ2", "parent_id": null, "user_id": null}}, "extra_data": {"last_event_type": "door_open_error", "last_event_attributes": null}, "last_seen": "2025-08-29T14:59:59.635418+00:00"}, {"state": {"entity_id": "event.xi_yi_ji_notification", "state": "2025-08-23T11:21:07.807+00:00", "attributes": {"event_types": ["washing_is_complete", "error_during_washing"], "event_type": "washing_is_complete", "friendly_name": "洗衣機 Notification"}, "last_changed": "2025-08-29T14:59:53.927088+00:00", "last_reported": "2025-08-29T14:59:53.927088+00:00", "last_updated": "2025-08-29T14:59:53.927088+00:00", "context": {"id": "01K3V5F7E78K6XA9Y6BDYFBS3X", "parent_id": null, "user_id": null}}, "extra_data": {"last_event_type": "washing_is_complete", "last_event_attributes": null}, "last_seen": "2025-08-29T14:59:59.635418+00:00"}, {"state": {"entity_id": "event.bing_xiang_notification", "state": "unavailable", "attributes": {"event_types": ["time_to_change_water_filter", "door_is_open", "time_to_change_filter", "frozen_is_complete"], "friendly_name": "冰箱 Notification"}, "last_changed": "2025-08-29T14:59:53.927212+00:00", "last_reported": "2025-08-29T14:59:53.927212+00:00", "last_updated": "2025-08-29T14:59:53.927212+00:00", "context": {"id": "01K3V5F7E7985HATQYT69W3ZHT", "parent_id": null, "user_id": null}}, "extra_data": {"last_event_type": "time_to_change_water_filter", "last_event_attributes": null}, "last_seen": "2025-08-29T14:59:59.635418+00:00"}, {"state": {"entity_id": "event.xi_ji_error", "state": "unavailable", "attributes": {"event_types": ["unable_to_lock_error", "out_of_balance_error", "water_drain_error", "temperature_sensor_error", "locked_motor_error", "door_open_error", "overfill_error", "water_supply_error", "power_fail_error", "water_level_sensor_error"], "friendly_name": "洗機 Error"}, "last_changed": "2025-08-29T14:59:53.927279+00:00", "last_reported": "2025-08-29T14:59:53.927279+00:00", "last_updated": "2025-08-29T14:59:53.927279+00:00", "context": {"id": "01K3V5F7E7NCYHNQ5YDWAQQ5AE", "parent_id": null, "user_id": null}}, "extra_data": {"last_event_type": null, "last_event_attributes": null}, "last_seen": "2025-08-29T14:59:59.635418+00:00"}, {"state": {"entity_id": "event.xi_ji_notification", "state": "unavailable", "attributes": {"event_types": ["error_during_washing", "washing_is_complete"], "friendly_name": "洗機 Notification"}, "last_changed": "2025-08-29T14:59:53.927340+00:00", "last_reported": "2025-08-29T14:59:53.927340+00:00", "last_updated": "2025-08-29T14:59:53.927340+00:00", "context": {"id": "01K3V5F7E76F4CTSXFGRPJ5G88", "parent_id": null, "user_id": null}}, "extra_data": {"last_event_type": null, "last_event_attributes": null}, "last_seen": "2025-08-29T14:59:59.635418+00:00"}, {"state": {"entity_id": "event.dian_zi_yi_chu_error", "state": "unknown", "attributes": {"event_types": ["water_leaks_error", "steam_heat_error", "le_error", "temperature_sensor_error", "water_level_sensor_error", "le2_error", "door_close_error", "need_water_replenishment", "door_open_error", "need_water_drain"], "event_type": null, "friendly_name": "電子衣櫥 Error"}, "last_changed": "2025-08-29T14:59:53.927425+00:00", "last_reported": "2025-08-29T14:59:53.927425+00:00", "last_updated": "2025-08-29T14:59:53.927425+00:00", "context": {"id": "01K3V5F7E708ZQ3N787GM6BT2F", "parent_id": null, "user_id": null}}, "extra_data": {"last_event_type": null, "last_event_attributes": null}, "last_seen": "2025-08-29T14:59:59.635418+00:00"}, {"state": {"entity_id": "event.dian_zi_yi_chu_notification", "state": "unknown", "attributes": {"event_types": ["styling_is_complete", "error_has_occurred"], "event_type": null, "friendly_name": "電子衣櫥 Notification"}, "last_changed": "2025-08-29T14:59:53.927503+00:00", "last_reported": "2025-08-29T14:59:53.927503+00:00", "last_updated": "2025-08-29T14:59:53.927503+00:00", "context": {"id": "01K3V5F7E7XP6X2HA5KCRWXWM1", "parent_id": null, "user_id": null}}, "extra_data": {"last_event_type": null, "last_event_attributes": null}, "last_seen": "2025-08-29T14:59:59.635418+00:00"}, {"state": {"entity_id": "event.chu_shi_ji_notification", "state": "unknown", "attributes": {"event_types": ["water_is_full"], "event_type": null, "friendly_name": "除濕機 Notification"}, "last_changed": "2025-08-29T14:59:53.927557+00:00", "last_reported": "2025-08-29T14:59:53.927557+00:00", "last_updated": "2025-08-29T14:59:53.927557+00:00", "context": {"id": "01K3V5F7E79H5PDE8QK227B4Z3", "parent_id": null, "user_id": null}}, "extra_data": {"last_event_type": null, "last_event_attributes": null}, "last_seen": "2025-08-29T14:59:59.635418+00:00"}, {"state": {"entity_id": "binary_sensor.xin_xiang_men_gan_ying_contact", "state": "off", "attributes": {"device_class": "door", "friendly_name": "信箱門感應 Door"}, "last_changed": "2025-08-27T09:38:27.506474+00:00", "last_reported": "2025-08-27T09:38:27.506474+00:00", "last_updated": "2025-08-27T09:38:27.506474+00:00", "context": {"id": "01K3NE971JSR7Z53KRQ07WC0JH", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T09:54:11.795941+00:00"}, {"state": {"entity_id": "binary_sensor.che_ku_men_gan_ying_contact", "state": "off", "attributes": {"device_class": "door", "friendly_name": "車庫門感應 Door"}, "last_changed": "2025-08-27T09:38:27.506488+00:00", "last_reported": "2025-08-27T09:38:27.506488+00:00", "last_updated": "2025-08-27T09:38:27.506488+00:00", "context": {"id": "01K3NE971J7591S8PM71MCRCW4", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T10:00:55.719551+00:00"}, {"state": {"entity_id": "binary_sensor.che_ku_ce_men_gan_ying_contact", "state": "off", "attributes": {"device_class": "door", "friendly_name": "車庫側門感應 Door"}, "last_changed": "2025-08-27T09:38:27.506501+00:00", "last_reported": "2025-08-27T09:38:27.506501+00:00", "last_updated": "2025-08-27T09:38:27.506501+00:00", "context": {"id": "01K3NE971JMN052TQ2NGCRCCFX", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T09:59:28.107900+00:00"}, {"state": {"entity_id": "binary_sensor.xin_xiang_gan_ying_contact", "state": "off", "attributes": {"device_class": "door", "friendly_name": "信箱感應 Door"}, "last_changed": "2025-08-27T09:38:27.506514+00:00", "last_reported": "2025-08-27T09:38:27.506514+00:00", "last_updated": "2025-08-27T09:38:27.506514+00:00", "context": {"id": "01K3NE971JMP1BK5H7T397JNVH", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T09:54:00.268638+00:00"}, {"state": {"entity_id": "light.ke_ting_diao_deng", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "客廳吊燈", "supported_features": 44}, "last_changed": "2025-08-27T10:08:29.542818+00:00", "last_reported": "2025-08-27T10:08:29.542818+00:00", "last_updated": "2025-08-27T10:08:29.542818+00:00", "context": {"id": "01K3NG06V6A7N0BB9WBAMQXE2J", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T10:29:06.361136+00:00"}, {"state": {"entity_id": "light.can_ting_deng", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "餐廳燈", "supported_features": 44}, "last_changed": "2025-08-27T09:38:27.507159+00:00", "last_reported": "2025-08-27T09:38:27.507159+00:00", "last_updated": "2025-08-27T09:38:27.507159+00:00", "context": {"id": "01K3NE971KND4G7SG50SRYJYJ8", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T10:01:51.915808+00:00"}, {"state": {"entity_id": "light.ke_ting_kan_deng", "state": "on", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": "color_temp", "brightness": 33, "color_temp_kelvin": 2000, "color_temp": 500, "hs_color": [30.601, 94.547], "rgb_color": [255, 137, 14], "xy_color": [0.598, 0.383], "friendly_name": "客廳崁燈", "supported_features": 44}, "last_changed": "2025-08-27T10:23:06.013149+00:00", "last_reported": "2025-08-27T10:23:06.013149+00:00", "last_updated": "2025-08-27T10:23:06.013149+00:00", "context": {"id": "01K3NGTYGX66YTQXMHQXHB22E7", "parent_id": null, "user_id": "9b1cbb01e5804f8d8d27eeb071638aab"}}, "extra_data": null, "last_seen": "2025-08-27T10:23:27.004390+00:00"}, {"state": {"entity_id": "switch.2f_ce_suo_kai_guan", "state": "off", "attributes": {"friendly_name": "2F 廁所開關"}, "last_changed": "2025-08-27T09:38:27.508985+00:00", "last_reported": "2025-08-27T09:38:27.508985+00:00", "last_updated": "2025-08-27T09:38:27.508985+00:00", "context": {"id": "01K3NE971M6FXZZ461E25862VF", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T09:50:03.797204+00:00"}, {"state": {"entity_id": "switch.3f_bi_deng_kai_guan", "state": "on", "attributes": {"friendly_name": "3F 壁燈開關"}, "last_changed": "2025-08-27T09:38:27.509002+00:00", "last_reported": "2025-08-27T09:38:27.509002+00:00", "last_updated": "2025-08-27T09:38:27.509002+00:00", "context": {"id": "01K3NE971NPDY2X0NY3FZ5SYET", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T09:51:12.413595+00:00"}, {"state": {"entity_id": "switch.che_ku_deng_kai_guan_l1", "state": "off", "attributes": {"friendly_name": "冰箱燈"}, "last_changed": "2025-08-27T09:38:27.509023+00:00", "last_reported": "2025-08-27T09:38:27.509023+00:00", "last_updated": "2025-08-27T09:38:27.509023+00:00", "context": {"id": "01K3NE971N90MMB4RS361EM1FG", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T09:59:48.603110+00:00"}, {"state": {"entity_id": "switch.che_ku_deng_kai_guan_l2", "state": "off", "attributes": {"friendly_name": "車庫燈"}, "last_changed": "2025-08-27T09:38:27.509033+00:00", "last_reported": "2025-08-27T09:38:27.509033+00:00", "last_updated": "2025-08-27T09:38:27.509033+00:00", "context": {"id": "01K3NE971NVXR2MEE3HS6C0BNF", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T09:59:55.491518+00:00"}, {"state": {"entity_id": "switch.che_ku_men_l1", "state": "off", "attributes": {"friendly_name": "關"}, "last_changed": "2025-08-27T09:38:27.509053+00:00", "last_reported": "2025-08-27T09:38:27.509053+00:00", "last_updated": "2025-08-27T09:38:27.509053+00:00", "context": {"id": "01K3NE971NF0BE6NDY44JS9PCX", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T10:00:42.243330+00:00"}, {"state": {"entity_id": "switch.che_ku_men_l2", "state": "off", "attributes": {"friendly_name": "開"}, "last_changed": "2025-08-27T09:38:27.509063+00:00", "last_reported": "2025-08-27T09:38:27.509063+00:00", "last_updated": "2025-08-27T09:38:27.509063+00:00", "context": {"id": "01K3NE971NASADV71B86QMSVKF", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T10:00:34.900996+00:00"}, {"state": {"entity_id": "switch.shu_fang_deng_kai_guan", "state": "off", "attributes": {"friendly_name": "書房燈開關"}, "last_changed": "2025-08-27T09:57:32.038883+00:00", "last_reported": "2025-08-27T09:57:32.038883+00:00", "last_updated": "2025-08-27T09:57:32.038883+00:00", "context": {"id": "01K3NFC4MB1N97TTY6X0ZA64B5", "parent_id": null, "user_id": "9b1cbb01e5804f8d8d27eeb071638aab"}}, "extra_data": null, "last_seen": "2025-08-27T09:57:54.068981+00:00"}, {"state": {"entity_id": "switch.3f_yu_shi_deng_kai_guan", "state": "off", "attributes": {"friendly_name": "3F 浴室燈開關"}, "last_changed": "2025-08-27T09:38:27.509084+00:00", "last_reported": "2025-08-27T09:38:27.509084+00:00", "last_updated": "2025-08-27T09:38:27.509084+00:00", "context": {"id": "01K3NE971N2JG6P5HC8S8SF2SW", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T09:51:58.108902+00:00"}, {"state": {"entity_id": "switch.geng_yi_shi_kai_guan", "state": "off", "attributes": {"friendly_name": "更衣室開關"}, "last_changed": "2025-08-27T09:38:27.509096+00:00", "last_reported": "2025-08-27T09:38:27.509096+00:00", "last_updated": "2025-08-27T09:38:27.509096+00:00", "context": {"id": "01K3NE971NZFA2P8M3GA8W513N", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T09:57:05.152156+00:00"}, {"state": {"entity_id": "switch.4f_lou_ti_kai_guan_l2", "state": "on", "attributes": {"friendly_name": "4F 壁燈開關"}, "last_changed": "2025-08-27T09:38:27.509140+00:00", "last_reported": "2025-08-27T09:38:27.509140+00:00", "last_updated": "2025-08-27T09:38:27.509140+00:00", "context": {"id": "01K3NE971NMDXKQ5BBTFXR4WEW", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T09:52:26.868093+00:00"}, {"state": {"entity_id": "switch.4f_yang_tai_deng_kai_guan", "state": "off", "attributes": {"friendly_name": "4F 陽台燈開關"}, "last_changed": "2025-08-27T09:38:27.509161+00:00", "last_reported": "2025-08-27T09:38:27.509161+00:00", "last_updated": "2025-08-27T09:38:27.509161+00:00", "context": {"id": "01K3NE971NH3C49GB44RT6R0SR", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T09:53:04.043651+00:00"}, {"state": {"entity_id": "switch.wo_shi_deng_men_bian_kai_guan", "state": "off", "attributes": {"friendly_name": "臥室燈門邊開關"}, "last_changed": "2025-08-27T09:38:27.509172+00:00", "last_reported": "2025-08-27T09:38:27.509172+00:00", "last_updated": "2025-08-27T09:38:27.509172+00:00", "context": {"id": "01K3NE971N74PBZ1H2BT3J15JV", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T09:59:08.540350+00:00"}, {"state": {"entity_id": "switch.wo_shi_deng_qiang_mian_kai_guan", "state": "off", "attributes": {"friendly_name": "臥室燈牆面開關"}, "last_changed": "2025-08-27T09:38:27.509182+00:00", "last_reported": "2025-08-27T09:38:27.509182+00:00", "last_updated": "2025-08-27T09:38:27.509182+00:00", "context": {"id": "01K3NE971NCBG1FR0ZAFMTN9MH", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T09:58:55.694722+00:00"}, {"state": {"entity_id": "switch.4f_yu_shi_deng_kai_guan", "state": "off", "attributes": {"friendly_name": "4F 浴室燈開關"}, "last_changed": "2025-08-27T09:38:27.509193+00:00", "last_reported": "2025-08-27T09:38:27.509193+00:00", "last_updated": "2025-08-27T09:38:27.509193+00:00", "context": {"id": "01K3NE971N4NSF6HG0PG122ZGJ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T09:52:52.763610+00:00"}, {"state": {"entity_id": "switch.ke_ting_kai_guan_l1", "state": "off", "attributes": {"friendly_name": "餐廳燈開關"}, "last_changed": "2025-08-27T10:39:42.060748+00:00", "last_reported": "2025-08-27T10:39:42.060748+00:00", "last_updated": "2025-08-27T10:39:42.060748+00:00", "context": {"id": "01K3NHSBFC1VMR7N466SEWDKBS", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T10:41:13.334558+00:00"}, {"state": {"entity_id": "switch.ke_ting_kai_guan_l2", "state": "off", "attributes": {"friendly_name": "客廳吊燈開關"}, "last_changed": "2025-08-27T10:35:41.841164+00:00", "last_reported": "2025-08-27T10:37:41.658635+00:00", "last_updated": "2025-08-27T10:35:41.841164+00:00", "context": {"id": "01K3NHJ0WHX4F5RZ96F66WR6ES", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T10:38:04.494858+00:00"}, {"state": {"entity_id": "switch.ke_ting_kai_guan_l3", "state": "off", "attributes": {"friendly_name": "客廳嵌燈開關"}, "last_changed": "2025-08-27T10:35:40.311215+00:00", "last_reported": "2025-08-27T10:35:40.311215+00:00", "last_updated": "2025-08-27T10:35:40.311215+00:00", "context": {"id": "01K3NHHZCQ56HQ07EQZV9CJCM7", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T10:37:57.128870+00:00"}, {"state": {"entity_id": "switch.2f_lou_ti_kai_guan_l1", "state": "off", "attributes": {"friendly_name": "地圖燈開關"}, "last_changed": "2025-08-27T09:38:27.509382+00:00", "last_reported": "2025-08-27T09:38:27.509382+00:00", "last_updated": "2025-08-27T09:38:27.509382+00:00", "context": {"id": "01K3NE971NPWMWJV2BAM02SKRJ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T09:50:28.427874+00:00"}, {"state": {"entity_id": "switch.2f_lou_ti_kai_guan_l3", "state": "off", "attributes": {"friendly_name": "2F 樓梯間開關"}, "last_changed": "2025-08-27T09:38:27.509404+00:00", "last_reported": "2025-08-27T09:38:27.509404+00:00", "last_updated": "2025-08-27T09:38:27.509404+00:00", "context": {"id": "01K3NE971N6JFN31HP81Q5YFEM", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T09:50:19.354012+00:00"}, {"state": {"entity_id": "switch.chu_fang_kai_guan_l1", "state": "off", "attributes": {"friendly_name": "吧檯燈"}, "last_changed": "2025-08-27T09:38:27.509437+00:00", "last_reported": "2025-08-27T09:38:27.509437+00:00", "last_updated": "2025-08-27T09:38:27.509437+00:00", "context": {"id": "01K3NE971NDJ9ASGTYBJ9YFBB2", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T09:56:08.057434+00:00"}, {"state": {"entity_id": "switch.chu_fang_kai_guan_l2", "state": "off", "attributes": {"friendly_name": "廚房燈"}, "last_changed": "2025-08-27T09:38:27.509451+00:00", "last_reported": "2025-08-27T09:38:27.509451+00:00", "last_updated": "2025-08-27T09:38:27.509451+00:00", "context": {"id": "01K3NE971NFC3B1G8YD8TGGZWG", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T09:56:43.195482+00:00"}, {"state": {"entity_id": "switch.chu_fang_kai_guan_l3", "state": "off", "attributes": {"friendly_name": "後陽台燈"}, "last_changed": "2025-08-27T09:38:27.509472+00:00", "last_reported": "2025-08-27T09:38:27.509472+00:00", "last_updated": "2025-08-27T09:38:27.509472+00:00", "context": {"id": "01K3NE971NQ9WD1F6QD3KYRXRQ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T09:56:53.903763+00:00"}, {"state": {"entity_id": "switch.xuan_guan_kai_guan_l2", "state": "off", "attributes": {"friendly_name": "玄關燈"}, "last_changed": "2025-08-27T09:58:27.620433+00:00", "last_reported": "2025-08-27T09:58:27.620433+00:00", "last_updated": "2025-08-27T09:58:27.620433+00:00", "context": {"id": "01K3NFDTTM1RGFSRFZSEGXRBD9", "parent_id": null, "user_id": "9b1cbb01e5804f8d8d27eeb071638aab"}}, "extra_data": null, "last_seen": "2025-08-27T09:58:30.239651+00:00"}, {"state": {"entity_id": "switch.xuan_guan_kai_guan_l3", "state": "on", "attributes": {"friendly_name": "玄關開關 L3"}, "last_changed": "2025-08-27T09:38:27.510171+00:00", "last_reported": "2025-08-27T09:38:27.510171+00:00", "last_updated": "2025-08-27T09:38:27.510171+00:00", "context": {"id": "01K3NE971P7JSXC6SVRX4F7MVB", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T09:58:23.421671+00:00"}, {"state": {"entity_id": "switch.3f_lou_ti_kai_guan_l1", "state": "on", "attributes": {"friendly_name": "3F 樓梯燈"}, "last_changed": "2025-08-27T09:38:27.510193+00:00", "last_reported": "2025-08-27T09:38:27.510193+00:00", "last_updated": "2025-08-27T09:38:27.510193+00:00", "context": {"id": "01K3NE971PNBXFEFJ7FHEEV03K", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T09:51:40.677532+00:00"}, {"state": {"entity_id": "switch.3f_lou_ti_kai_guan_l2", "state": "off", "attributes": {"friendly_name": "3F 樓梯間"}, "last_changed": "2025-08-27T09:38:27.510203+00:00", "last_reported": "2025-08-27T09:38:27.510203+00:00", "last_updated": "2025-08-27T09:38:27.510203+00:00", "context": {"id": "01K3NE971P2AS1W7ZG6K2QGKC8", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T09:51:48.469965+00:00"}, {"state": {"entity_id": "switch.3f_lou_ti_kai_guan_l3", "state": "off", "attributes": {"friendly_name": "2F 樓梯燈"}, "last_changed": "2025-08-27T09:38:27.510213+00:00", "last_reported": "2025-08-27T09:38:27.510213+00:00", "last_updated": "2025-08-27T09:38:27.510213+00:00", "context": {"id": "01K3NE971PX1EFG0RZXNM3W1DG", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T09:51:25.548425+00:00"}, {"state": {"entity_id": "binary_sensor.0xa4c138d20c4e154d_presence", "state": "on", "attributes": {"device_class": "presence", "friendly_name": "玄關人在感應 Presence"}, "last_changed": "2025-08-25T10:32:21.215998+00:00", "last_reported": "2025-08-25T10:32:21.215998+00:00", "last_updated": "2025-08-25T10:32:21.215998+00:00", "context": {"id": "01K3GCJEYZPC4RZA5A2Z71TRNM", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "binary_sensor.0x842712fffe7cec33_occupancy", "state": "off", "attributes": {"device_class": "occupancy", "friendly_name": "玄關動作感應 Occupancy"}, "last_changed": "2025-08-26T15:37:40.392509+00:00", "last_reported": "2025-08-26T15:37:40.392509+00:00", "last_updated": "2025-08-26T15:37:40.392509+00:00", "context": {"id": "01K3KGE7S81NRTJ2KA7SJWJMFH", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "binary_sensor.0x842712fffe7ce6d3_occupancy", "state": "off", "attributes": {"device_class": "occupancy", "friendly_name": "3F 動作感應 Occupancy"}, "last_changed": "2025-08-26T15:32:53.187047+00:00", "last_reported": "2025-08-26T15:32:53.187047+00:00", "last_updated": "2025-08-26T15:32:53.187047+00:00", "context": {"id": "01K3KG5FA3R7A347A9ZC39YFXY", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "button.0xf84477fffe4d19bd_factory_reset", "state": "unknown", "attributes": {"friendly_name": "窗簾右 Factory reset"}, "last_changed": "2025-08-25T10:31:42.116917+00:00", "last_reported": "2025-08-25T10:31:42.116917+00:00", "last_updated": "2025-08-25T10:31:42.116917+00:00", "context": {"id": "01K3GCH8S4TCYYKQWS2YME09C3", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "button.0xf84477fffe40747a_factory_reset", "state": "unknown", "attributes": {"friendly_name": "窗簾左 Factory reset"}, "last_changed": "2025-08-25T10:31:42.118087+00:00", "last_reported": "2025-08-25T10:31:42.118087+00:00", "last_updated": "2025-08-25T10:31:42.118087+00:00", "context": {"id": "01K3GCH8S6H82JB1GZG74X90TJ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "light.4f_bathroom", "state": "on", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": "color_temp", "brightness": 217, "color_temp_kelvin": 2000, "color_temp": 500, "hs_color": [30.601, 94.547], "rgb_color": [255, 137, 14], "xy_color": [0.598, 0.383], "friendly_name": "4F 浴室燈", "supported_features": 44}, "last_changed": "2025-08-25T10:32:21.330890+00:00", "last_reported": "2025-08-25T10:32:21.330890+00:00", "last_updated": "2025-08-25T10:32:21.330890+00:00", "context": {"id": "01K3GCJF2JZ3NTAYH6BFENQX3C", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "light.0xa4c138ec6626cadd", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "餐廳中間崁燈", "supported_features": 44}, "last_changed": "2025-08-25T14:54:01.122945+00:00", "last_reported": "2025-08-25T14:54:01.122945+00:00", "last_updated": "2025-08-25T14:54:01.122945+00:00", "context": {"id": "01K3GVHJX238NEEBDQR7SMKECY", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "light.0xa4c13844a96c99c6", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "餐廳右崁燈", "supported_features": 44}, "last_changed": "2025-08-25T14:54:01.117712+00:00", "last_reported": "2025-08-25T14:54:01.117712+00:00", "last_updated": "2025-08-25T14:54:01.117712+00:00", "context": {"id": "01K3GVHJWX0YDEBCRCW0G9CRWT", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "light.0x70b3d52b601209fa", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "客廳左吊燈", "supported_features": 44}, "last_changed": "2025-08-25T16:41:30.520078+00:00", "last_reported": "2025-08-25T16:41:31.474104+00:00", "last_updated": "2025-08-25T16:41:30.520078+00:00", "context": {"id": "01K3H1PD4RJ2REEFJXV68ZNMTJ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "light.0xa4c13814b628056b", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "客廳右吊燈", "supported_features": 44}, "last_changed": "2025-08-25T16:41:30.495211+00:00", "last_reported": "2025-08-25T16:41:30.495211+00:00", "last_updated": "2025-08-25T16:41:30.495211+00:00", "context": {"id": "01K3H1PD3Z5BN13F7QYT2JVBS1", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "light.0xa4c138a6755b559d", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "餐廳左崁燈", "supported_features": 44}, "last_changed": "2025-08-25T14:54:01.125090+00:00", "last_reported": "2025-08-25T14:54:01.125090+00:00", "last_updated": "2025-08-25T14:54:01.125090+00:00", "context": {"id": "01K3GVHJX5S52FAEXNC0Y4TJZY", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "light.0xa4c138385587e524", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "客廳左下崁燈", "supported_features": 44}, "last_changed": "2025-08-26T15:39:18.579555+00:00", "last_reported": "2025-08-26T15:39:18.579555+00:00", "last_updated": "2025-08-26T15:39:18.579555+00:00", "context": {"id": "01K3KGH7NKEJEWBRD5VNZQM9QW", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "light.0xa4c13893935668b4", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "客廳左上崁燈", "supported_features": 44}, "last_changed": "2025-08-26T15:39:18.581791+00:00", "last_reported": "2025-08-26T15:39:18.581791+00:00", "last_updated": "2025-08-26T15:39:18.581791+00:00", "context": {"id": "01K3KGH7NNAPQZ9PP6M30KW5KB", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "light.0xa4c1380a172e5a6a", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "客廳右上崁燈", "supported_features": 44}, "last_changed": "2025-08-26T15:39:18.580755+00:00", "last_reported": "2025-08-26T15:39:18.580755+00:00", "last_updated": "2025-08-26T15:39:18.580755+00:00", "context": {"id": "01K3KGH7NM542Q7JN178719AYW", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "light.0xa4c138d449095da3", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "客廳右下崁燈", "supported_features": 44}, "last_changed": "2025-08-26T15:39:18.577199+00:00", "last_reported": "2025-08-26T15:39:18.577199+00:00", "last_updated": "2025-08-26T15:39:18.577199+00:00", "context": {"id": "01K3KGH7NHRR2F0EN02JCVWZVQ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0xa4c138d20c4e154d_large_motion_detection_sensitivity", "state": "2", "attributes": {"min": 0.0, "max": 10.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "x", "friendly_name": "玄關人在感應 Large motion detection sensitivity"}, "last_changed": "2025-08-25T10:32:21.215395+00:00", "last_reported": "2025-08-25T10:32:21.215395+00:00", "last_updated": "2025-08-25T10:32:21.215395+00:00", "context": {"id": "01K3GCJEYZ7SKFTQV55CZ3W9FH", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 10.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "x", "native_value": 2}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0xa4c138d20c4e154d_large_motion_detection_distance", "state": "4", "attributes": {"min": 0.0, "max": 10.0, "step": 0.01, "mode": "auto", "unit_of_measurement": "m", "friendly_name": "玄關人在感應 Large motion detection distance"}, "last_changed": "2025-08-25T10:32:21.215518+00:00", "last_reported": "2025-08-25T10:32:21.215518+00:00", "last_updated": "2025-08-25T10:32:21.215518+00:00", "context": {"id": "01K3GCJEYZ7BP3G77EFXGMF5D1", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 10.0, "native_min_value": 0.0, "native_step": 0.01, "native_unit_of_measurement": "m", "native_value": 4}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0xa4c138d20c4e154d_fading_time", "state": "60", "attributes": {"min": 0.0, "max": 28800.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "s", "friendly_name": "玄關人在感應 Fading time"}, "last_changed": "2025-08-25T10:32:21.215790+00:00", "last_reported": "2025-08-25T10:32:21.215790+00:00", "last_updated": "2025-08-25T10:32:21.215790+00:00", "context": {"id": "01K3GCJEYZM8XYB5S15SH2SA0N", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 28800.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "s", "native_value": 60}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0xa4c138d20c4e154d_medium_motion_detection_distance", "state": "3", "attributes": {"min": 0.0, "max": 6.0, "step": 0.01, "mode": "auto", "unit_of_measurement": "m", "friendly_name": "玄關人在感應 Medium motion detection distance"}, "last_changed": "2025-08-25T10:32:21.215721+00:00", "last_reported": "2025-08-25T10:32:21.215721+00:00", "last_updated": "2025-08-25T10:32:21.215721+00:00", "context": {"id": "01K3GCJEYZ6Y9VYXFRE0199MC7", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 6.0, "native_min_value": 0.0, "native_step": 0.01, "native_unit_of_measurement": "m", "native_value": 3}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0xa4c138d20c4e154d_medium_motion_detection_sensitivity", "state": "5", "attributes": {"min": 0.0, "max": 10.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "x", "friendly_name": "玄關人在感應 Medium motion detection sensitivity"}, "last_changed": "2025-08-25T10:32:21.215921+00:00", "last_reported": "2025-08-25T10:32:21.215921+00:00", "last_updated": "2025-08-25T10:32:21.215921+00:00", "context": {"id": "01K3GCJEYZF2S26B6PGGFTWX6S", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 10.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "x", "native_value": 5}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0xa4c138d20c4e154d_small_detection_distance", "state": "3", "attributes": {"min": 0.0, "max": 6.0, "step": 0.01, "mode": "auto", "unit_of_measurement": "m", "friendly_name": "玄關人在感應 Small detection distance"}, "last_changed": "2025-08-25T10:32:21.215126+00:00", "last_reported": "2025-08-25T10:32:21.215126+00:00", "last_updated": "2025-08-25T10:32:21.215126+00:00", "context": {"id": "01K3GCJEYZYDANET3CVDJHDBRX", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 6.0, "native_min_value": 0.0, "native_step": 0.01, "native_unit_of_measurement": "m", "native_value": 3}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0xa4c138d20c4e154d_small_detection_sensitivity", "state": "5", "attributes": {"min": 0.0, "max": 10.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "x", "friendly_name": "玄關人在感應 Small detection sensitivity"}, "last_changed": "2025-08-25T10:32:21.215578+00:00", "last_reported": "2025-08-25T10:32:21.215578+00:00", "last_updated": "2025-08-25T10:32:21.215578+00:00", "context": {"id": "01K3GCJEYZ07EK1P6SC64F2J0E", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 10.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "x", "native_value": 5}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0x842712fffe7cec33_motion_timeout", "state": "unknown", "attributes": {"min": 5.0, "max": 60.0, "step": 1.0, "mode": "auto", "friendly_name": "玄關動作感應 Motion timeout"}, "last_changed": "2025-08-25T10:31:42.119079+00:00", "last_reported": "2025-08-25T10:31:42.119079+00:00", "last_updated": "2025-08-25T10:31:42.119079+00:00", "context": {"id": "01K3GCH8S7MJC0JRP0ZVTQ2HA5", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 60.0, "native_min_value": 5.0, "native_step": 1.0, "native_unit_of_measurement": null, "native_value": null}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0x6cfd22fffe19abea_countdown_l1", "state": "0", "attributes": {"min": 0.0, "max": 43200.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "s", "friendly_name": "車庫燈開關 Countdown l1"}, "last_changed": "2025-08-25T10:32:21.236213+00:00", "last_reported": "2025-08-25T10:32:21.236213+00:00", "last_updated": "2025-08-25T10:32:21.236213+00:00", "context": {"id": "01K3GCJEZMTHZ65NXRTWQ04FHX", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 43200.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "s", "native_value": 0}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0x6cfd22fffe19abea_countdown_l2", "state": "0", "attributes": {"min": 0.0, "max": 43200.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "s", "friendly_name": "車庫燈開關 Countdown l2"}, "last_changed": "2025-08-25T10:32:21.235941+00:00", "last_reported": "2025-08-25T10:32:21.235941+00:00", "last_updated": "2025-08-25T10:32:21.235941+00:00", "context": {"id": "01K3GCJEZK5KHQYVNFF0W7TEA7", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 43200.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "s", "native_value": 0}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0xa4c138ba2d3d0757_illuminance_interval", "state": "60", "attributes": {"min": 1.0, "max": 720.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "minutes", "friendly_name": "信箱門感應 Illuminance interval"}, "last_changed": "2025-08-25T10:32:21.277708+00:00", "last_reported": "2025-08-25T10:32:21.277708+00:00", "last_updated": "2025-08-25T10:32:21.277708+00:00", "context": {"id": "01K3GCJF0X16B8SBPKQME0J4YE", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 720.0, "native_min_value": 1.0, "native_step": 1.0, "native_unit_of_measurement": "minutes", "native_value": 60}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0xa4c13870139ddef7_illuminance_interval", "state": "60", "attributes": {"min": 1.0, "max": 720.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "minutes", "friendly_name": "車庫門感應 Illuminance interval"}, "last_changed": "2025-08-25T10:32:21.279139+00:00", "last_reported": "2025-08-25T10:32:21.279139+00:00", "last_updated": "2025-08-25T10:32:21.279139+00:00", "context": {"id": "01K3GCJF0ZP6ZRCSV5HSZ07P6C", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 720.0, "native_min_value": 1.0, "native_step": 1.0, "native_unit_of_measurement": "minutes", "native_value": 60}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0xa4c138591f9083d0_illuminance_interval", "state": "60", "attributes": {"min": 1.0, "max": 720.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "minutes", "friendly_name": "車庫側門感應 Illuminance interval"}, "last_changed": "2025-08-25T10:32:21.286031+00:00", "last_reported": "2025-08-25T10:32:21.286031+00:00", "last_updated": "2025-08-25T10:32:21.286031+00:00", "context": {"id": "01K3GCJF16V89D51K5S4ZHP037", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 720.0, "native_min_value": 1.0, "native_step": 1.0, "native_unit_of_measurement": "minutes", "native_value": 60}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0x842712fffe7ce6d3_motion_timeout", "state": "unknown", "attributes": {"min": 5.0, "max": 60.0, "step": 1.0, "mode": "auto", "friendly_name": "3F 動作感應 Motion timeout"}, "last_changed": "2025-08-25T10:31:42.110447+00:00", "last_reported": "2025-08-25T10:31:42.110447+00:00", "last_updated": "2025-08-25T10:31:42.110447+00:00", "context": {"id": "01K3GCH8RYGT8J46FK2WP89PS8", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 60.0, "native_min_value": 5.0, "native_step": 1.0, "native_unit_of_measurement": null, "native_value": null}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0x70c59cfffe264997_countdown_l1", "state": "0", "attributes": {"min": 0.0, "max": 43200.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "s", "friendly_name": "4F 樓梯開關 Countdown l1"}, "last_changed": "2025-08-25T10:32:21.295424+00:00", "last_reported": "2025-08-25T10:32:21.295424+00:00", "last_updated": "2025-08-25T10:32:21.295424+00:00", "context": {"id": "01K3GCJF1FRJT3WBHVHMDB8CFW", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 43200.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "s", "native_value": 0}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0x70c59cfffe264997_countdown_l2", "state": "0", "attributes": {"min": 0.0, "max": 43200.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "s", "friendly_name": "4F 樓梯開關 Countdown l2"}, "last_changed": "2025-08-25T10:32:21.295845+00:00", "last_reported": "2025-08-25T10:32:21.295845+00:00", "last_updated": "2025-08-25T10:32:21.295845+00:00", "context": {"id": "01K3GCJF1FDM7HVXD2YG1Y4YW3", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 43200.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "s", "native_value": 0}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x6cfd22fffe1c2f31_power_on_behavior", "state": "previous", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "2F 廁所開關 Power-on behavior"}, "last_changed": "2025-08-25T10:32:21.197127+00:00", "last_reported": "2025-08-25T10:32:21.197127+00:00", "last_updated": "2025-08-25T10:32:21.197127+00:00", "context": {"id": "01K3GCJEYD6YPE6E0Y03GEATZZ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x6cfd22fffe1c98f6_power_on_behavior", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "3F 壁燈開關 Power-on behavior"}, "last_changed": "2025-08-25T10:32:21.197726+00:00", "last_reported": "2025-08-25T10:32:21.197726+00:00", "last_updated": "2025-08-25T10:32:21.197726+00:00", "context": {"id": "01K3GCJEYD1KHDJ1BGCF8P0PPZ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x6cfd22fffe19abea_power_on_behavior_l1", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "車庫燈開關 Power-on behavior l1"}, "last_changed": "2025-08-25T10:32:21.236427+00:00", "last_reported": "2025-08-25T10:32:21.236427+00:00", "last_updated": "2025-08-25T10:32:21.236427+00:00", "context": {"id": "01K3GCJEZM1P1D2ZDQTF21DRD5", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x6cfd22fffe19abea_power_on_behavior_l2", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "車庫燈開關 Power-on behavior l2"}, "last_changed": "2025-08-25T10:32:21.236086+00:00", "last_reported": "2025-08-25T10:32:21.236086+00:00", "last_updated": "2025-08-25T10:32:21.236086+00:00", "context": {"id": "01K3GCJEZM9Z197M0N2J60S32G", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x6cfd22fffe19abea_switch_type", "state": "unknown", "attributes": {"options": ["toggle", "state", "momentary"], "icon": "mdi:tune", "friendly_name": "車庫燈開關 Switch type"}, "last_changed": "2025-08-25T10:31:42.109607+00:00", "last_reported": "2025-08-25T10:31:42.109607+00:00", "last_updated": "2025-08-25T10:31:42.109607+00:00", "context": {"id": "01K3GCH8RXR2V5F2GDV1R2KH03", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x6cfd22fffe19abea_indicator_mode", "state": "on/off", "attributes": {"options": ["off", "off/on", "on/off", "on"], "friendly_name": "車庫燈開關 Indicator mode"}, "last_changed": "2025-08-25T10:32:21.236268+00:00", "last_reported": "2025-08-25T10:32:21.236268+00:00", "last_updated": "2025-08-25T10:32:21.236268+00:00", "context": {"id": "01K3GCJEZM8D3GG8K7VR9HJ9J6", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xa4c13840bdea388c_switch_type", "state": "state", "attributes": {"options": ["toggle", "state", "momentary"], "icon": "mdi:tune", "friendly_name": "車庫門 Switch type"}, "last_changed": "2025-08-25T10:32:21.237560+00:00", "last_reported": "2025-08-25T10:32:21.237560+00:00", "last_updated": "2025-08-25T10:32:21.237560+00:00", "context": {"id": "01K3GCJEZNSN0KEQ6Q0EF2N40T", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x4c97a1fffe5487e3_power_on_behavior", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "3F 浴室燈開關 Power-on behavior"}, "last_changed": "2025-08-25T10:32:21.244752+00:00", "last_reported": "2025-08-25T10:32:21.244752+00:00", "last_updated": "2025-08-25T10:32:21.244752+00:00", "context": {"id": "01K3GCJEZWVAJK9866ABEB8DAH", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x4c97a1fffe548a6d_power_on_behavior", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "更衣室開關 Power-on behavior"}, "last_changed": "2025-08-25T10:32:21.247140+00:00", "last_reported": "2025-08-25T10:32:21.247140+00:00", "last_updated": "2025-08-25T10:32:21.247140+00:00", "context": {"id": "01K3GCJEZZ1H4XJ5Z6XN4TC9Z5", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x70c59cfffe264997_power_on_behavior_l1", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "4F 樓梯開關 Power-on behavior l1"}, "last_changed": "2025-08-25T10:32:21.295558+00:00", "last_reported": "2025-08-25T10:32:21.295558+00:00", "last_updated": "2025-08-25T10:32:21.295558+00:00", "context": {"id": "01K3GCJF1FN94Z81R258BG1NW3", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x70c59cfffe264997_power_on_behavior_l2", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "4F 樓梯開關 Power-on behavior l2"}, "last_changed": "2025-08-25T10:32:21.295895+00:00", "last_reported": "2025-08-25T10:32:21.295895+00:00", "last_updated": "2025-08-25T10:32:21.295895+00:00", "context": {"id": "01K3GCJF1FRDJNYGW395192V1E", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x70c59cfffe264997_switch_type", "state": "unknown", "attributes": {"options": ["toggle", "state", "momentary"], "icon": "mdi:tune", "friendly_name": "4F 樓梯開關 Switch type"}, "last_changed": "2025-08-25T10:31:42.110742+00:00", "last_reported": "2025-08-25T10:31:42.110742+00:00", "last_updated": "2025-08-25T10:31:42.110742+00:00", "context": {"id": "01K3GCH8RYDG7TMR93BC8CQKFF", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x70c59cfffe264997_indicator_mode", "state": "on/off", "attributes": {"options": ["off", "off/on", "on/off", "on"], "friendly_name": "4F 樓梯開關 Indicator mode"}, "last_changed": "2025-08-25T10:32:21.295675+00:00", "last_reported": "2025-08-25T10:32:21.295675+00:00", "last_updated": "2025-08-25T10:32:21.295675+00:00", "context": {"id": "01K3GCJF1FYATE52NPBJBX8200", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x6cfd22fffe1c9c6a_power_on_behavior", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "4F 陽台燈開關 Power-on behavior"}, "last_changed": "2025-08-25T10:32:21.297999+00:00", "last_reported": "2025-08-25T10:32:21.297999+00:00", "last_updated": "2025-08-25T10:32:21.297999+00:00", "context": {"id": "01K3GCJF1HAD2QZWCMZRE3E6JG", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x6cfd22fffe1cb10f_power_on_behavior", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "臥室燈門邊開關 Power-on behavior"}, "last_changed": "2025-08-25T10:32:21.299471+00:00", "last_reported": "2025-08-25T10:32:21.299471+00:00", "last_updated": "2025-08-25T10:32:21.299471+00:00", "context": {"id": "01K3GCJF1KQAJYRCSEPH9XAMJS", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x6cfd22fffe1a034e_power_on_behavior", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "臥室燈牆面開關 Power-on behavior"}, "last_changed": "2025-08-25T10:32:21.316589+00:00", "last_reported": "2025-08-25T10:32:21.316589+00:00", "last_updated": "2025-08-25T10:32:21.316589+00:00", "context": {"id": "01K3GCJF24TRDQEEBMF8PE5FWW", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x4c97a1fffe548970_power_on_behavior", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "4F 浴室燈開關 Power-on behavior"}, "last_changed": "2025-08-25T10:32:21.321550+00:00", "last_reported": "2025-08-25T10:32:21.321550+00:00", "last_updated": "2025-08-25T10:32:21.321550+00:00", "context": {"id": "01K3GCJF299F3NZJ77ZA56M1X7", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xa4c138a6ff2209eb_color_power_on_behavior", "state": "previous", "attributes": {"options": ["initial", "previous", "customized"], "icon": "mdi:palette", "friendly_name": "4F 浴室燈 Color power on behavior"}, "last_changed": "2025-08-25T10:32:21.330552+00:00", "last_reported": "2025-08-25T10:32:21.330552+00:00", "last_updated": "2025-08-25T10:32:21.330552+00:00", "context": {"id": "01K3GCJF2JV6YAQ1ZWVZ5A3DNR", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xa4c138ec6626cadd_color_power_on_behavior", "state": "unknown", "attributes": {"options": ["initial", "previous", "customized"], "icon": "mdi:palette", "friendly_name": "餐廳中間崁燈 Color power on behavior"}, "last_changed": "2025-08-25T10:31:42.114173+00:00", "last_reported": "2025-08-25T10:31:42.114173+00:00", "last_updated": "2025-08-25T10:31:42.114173+00:00", "context": {"id": "01K3GCH8S2NBQYBJ2GBEWN3BGY", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xa4c13844a96c99c6_color_power_on_behavior", "state": "unknown", "attributes": {"options": ["initial", "previous", "customized"], "icon": "mdi:palette", "friendly_name": "餐廳右崁燈 Color power on behavior"}, "last_changed": "2025-08-25T10:31:42.114340+00:00", "last_reported": "2025-08-25T10:31:42.114340+00:00", "last_updated": "2025-08-25T10:31:42.114340+00:00", "context": {"id": "01K3GCH8S285RM6R27EXZMZ076", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x70b3d52b601209fa_power_on_behavior", "state": "off", "attributes": {"options": ["off", "on", "toggle", "previous"], "icon": "mdi:power-settings", "friendly_name": "客廳左吊燈 Power-on behavior"}, "last_changed": "2025-08-25T10:32:21.334116+00:00", "last_reported": "2025-08-25T10:32:21.334116+00:00", "last_updated": "2025-08-25T10:32:21.334116+00:00", "context": {"id": "01K3GCJF2P549CFGC2X6TCNTA5", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xa4c13814b628056b_color_power_on_behavior", "state": "unknown", "attributes": {"options": ["initial", "previous", "customized"], "icon": "mdi:palette", "friendly_name": "客廳右吊燈 Color power on behavior"}, "last_changed": "2025-08-25T10:31:42.118833+00:00", "last_reported": "2025-08-25T10:31:42.118833+00:00", "last_updated": "2025-08-25T10:31:42.118833+00:00", "context": {"id": "01K3GCH8S6AKRT6C8961D4B92Y", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xa4c138a6755b559d_color_power_on_behavior", "state": "unknown", "attributes": {"options": ["initial", "previous", "customized"], "icon": "mdi:palette", "friendly_name": "餐廳左崁燈 Color power on behavior"}, "last_changed": "2025-08-25T10:31:42.117826+00:00", "last_reported": "2025-08-25T10:31:42.117826+00:00", "last_updated": "2025-08-25T10:31:42.117826+00:00", "context": {"id": "01K3GCH8S5EXGV9P3XYG27YACV", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xa4c138385587e524_color_power_on_behavior", "state": "unknown", "attributes": {"options": ["initial", "previous", "customized"], "icon": "mdi:palette", "friendly_name": "客廳左下崁燈 Color power on behavior"}, "last_changed": "2025-08-25T10:31:42.116057+00:00", "last_reported": "2025-08-25T10:31:42.116057+00:00", "last_updated": "2025-08-25T10:31:42.116057+00:00", "context": {"id": "01K3GCH8S47Q73DR85VJTHKZVE", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xa4c13893935668b4_color_power_on_behavior", "state": "unknown", "attributes": {"options": ["initial", "previous", "customized"], "icon": "mdi:palette", "friendly_name": "客廳左上崁燈 Color power on behavior"}, "last_changed": "2025-08-25T10:31:42.118191+00:00", "last_reported": "2025-08-25T10:31:42.118191+00:00", "last_updated": "2025-08-25T10:31:42.118191+00:00", "context": {"id": "01K3GCH8S684TEYRSWH77WMYNQ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xa4c1380a172e5a6a_color_power_on_behavior", "state": "unknown", "attributes": {"options": ["initial", "previous", "customized"], "icon": "mdi:palette", "friendly_name": "客廳右上崁燈 Color power on behavior"}, "last_changed": "2025-08-25T10:31:42.111725+00:00", "last_reported": "2025-08-25T10:31:42.111725+00:00", "last_updated": "2025-08-25T10:31:42.111725+00:00", "context": {"id": "01K3GCH8RZ7QC5YEAYV5BEV1KJ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xa4c138d449095da3_color_power_on_behavior", "state": "unknown", "attributes": {"options": ["initial", "previous", "customized"], "icon": "mdi:palette", "friendly_name": "客廳右下崁燈 Color power on behavior"}, "last_changed": "2025-08-25T10:31:42.108334+00:00", "last_reported": "2025-08-25T10:31:42.108334+00:00", "last_updated": "2025-08-25T10:31:42.108334+00:00", "context": {"id": "01K3GCH8RW4CW3NTTPTSZA8DJF", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xf84477fffe4d19bd_opening_mode", "state": "lift", "attributes": {"options": ["tilt", "lift"], "friendly_name": "窗簾右 Opening mode"}, "last_changed": "2025-08-25T10:32:21.348189+00:00", "last_reported": "2025-08-25T10:32:21.348189+00:00", "last_updated": "2025-08-25T10:32:21.348189+00:00", "context": {"id": "01K3GCJF34V6NVHE5A577G3W4Z", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xf84477fffe4d19bd_motor_direction", "state": "right", "attributes": {"options": ["left", "right"], "friendly_name": "窗簾右 Motor direction"}, "last_changed": "2025-08-25T10:32:21.346613+00:00", "last_reported": "2025-08-25T10:32:21.346613+00:00", "last_updated": "2025-08-25T10:32:21.346613+00:00", "context": {"id": "01K3GCJF32JJK7FGQX5CCD27GE", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xf84477fffe4d19bd_set_upper_limit", "state": "stop", "attributes": {"options": ["start", "stop"], "friendly_name": "窗簾右 Set upper limit"}, "last_changed": "2025-08-25T10:32:21.346485+00:00", "last_reported": "2025-08-25T10:32:21.346485+00:00", "last_updated": "2025-08-25T10:32:21.346485+00:00", "context": {"id": "01K3GCJF32H8JPT82DG3KGWKXR", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xf84477fffe40747a_opening_mode", "state": "lift", "attributes": {"options": ["tilt", "lift"], "friendly_name": "窗簾左 Opening mode"}, "last_changed": "2025-08-25T10:32:21.350420+00:00", "last_reported": "2025-08-25T10:32:21.350420+00:00", "last_updated": "2025-08-25T10:32:21.350420+00:00", "context": {"id": "01K3GCJF367HSJCZ6MA2BV0E7Z", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xf84477fffe40747a_motor_direction", "state": "right", "attributes": {"options": ["left", "right"], "friendly_name": "窗簾左 Motor direction"}, "last_changed": "2025-08-25T10:32:21.350732+00:00", "last_reported": "2025-08-25T10:32:21.350732+00:00", "last_updated": "2025-08-25T10:32:21.350732+00:00", "context": {"id": "01K3GCJF36CB60WS57M36BB6MS", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xf84477fffe40747a_set_upper_limit", "state": "stop", "attributes": {"options": ["start", "stop"], "friendly_name": "窗簾左 Set upper limit"}, "last_changed": "2025-08-25T10:32:21.350670+00:00", "last_reported": "2025-08-25T10:32:21.350670+00:00", "last_updated": "2025-08-25T10:32:21.350670+00:00", "context": {"id": "01K3GCJF3673S8VPNC2WW6TJ62", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0xa4c138d20c4e154d_illuminance", "state": "1896", "attributes": {"state_class": "measurement", "unit_of_measurement": "lx", "device_class": "illuminance", "friendly_name": "玄關人在感應 Illuminance"}, "last_changed": "2025-08-25T10:32:21.215318+00:00", "last_reported": "2025-08-25T10:32:21.215318+00:00", "last_updated": "2025-08-25T10:32:21.215318+00:00", "context": {"id": "01K3GCJEYZBGXWGVCKZ5GDN6N3", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "1896", "native_unit_of_measurement": "lx"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0xa4c138d20c4e154d_motion_state", "state": "large", "attributes": {"friendly_name": "玄關人在感應 Motion state"}, "last_changed": "2025-08-25T10:32:21.215457+00:00", "last_reported": "2025-08-25T10:32:21.215457+00:00", "last_updated": "2025-08-25T10:32:21.215457+00:00", "context": {"id": "01K3GCJEYZ2TXPNXZR5J2BFF7W", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "large", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0x842712fffe7cec33_illumination", "state": "dim", "attributes": {"friendly_name": "玄關動作感應 Illumination"}, "last_changed": "2025-08-26T08:56:38.461061+00:00", "last_reported": "2025-08-26T08:56:38.461061+00:00", "last_updated": "2025-08-26T08:56:38.461061+00:00", "context": {"id": "01K3JSFXSXFGX0TMA81CN1J07N", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "dim", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0x842712fffe7cec33_battery", "state": "100", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "玄關動作感應 Battery"}, "last_changed": "2025-08-25T10:32:21.230433+00:00", "last_reported": "2025-08-25T10:32:21.230433+00:00", "last_updated": "2025-08-25T10:32:21.230433+00:00", "context": {"id": "01K3GCJEZEJG4RGGM3ERDFRY48", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "100", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0x842712fffe7cec33_voltage", "state": "3000", "attributes": {"state_class": "measurement", "unit_of_measurement": "mV", "device_class": "voltage", "friendly_name": "玄關動作感應 Voltage"}, "last_changed": "2025-08-25T10:32:21.230942+00:00", "last_reported": "2025-08-25T10:32:21.230942+00:00", "last_updated": "2025-08-25T10:32:21.230942+00:00", "context": {"id": "01K3GCJEZEB0JDQEHWTAV94BCB", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "3000", "native_unit_of_measurement": "mV"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0x00158d00073a5cae_battery", "state": "100", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "小米無線開關 Battery"}, "last_changed": "2025-08-25T10:32:21.274036+00:00", "last_reported": "2025-08-25T10:32:21.274036+00:00", "last_updated": "2025-08-25T10:32:21.274036+00:00", "context": {"id": "01K3GCJF0TQRF8M59XGBXFWC7N", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "100", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0x00158d00073a5cae_voltage", "state": "3062", "attributes": {"state_class": "measurement", "unit_of_measurement": "mV", "device_class": "voltage", "friendly_name": "小米無線開關 Voltage"}, "last_changed": "2025-08-25T10:32:21.273875+00:00", "last_reported": "2025-08-25T10:32:21.273875+00:00", "last_updated": "2025-08-25T10:32:21.273875+00:00", "context": {"id": "01K3GCJF0SZS1F167NWZS5T5H6", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "3062", "native_unit_of_measurement": "mV"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0xa4c138ba2d3d0757_illuminance", "state": "0", "attributes": {"state_class": "measurement", "unit_of_measurement": "lx", "device_class": "illuminance", "friendly_name": "信箱門感應 Illuminance"}, "last_changed": "2025-08-25T10:32:21.277312+00:00", "last_reported": "2025-08-25T10:32:21.277312+00:00", "last_updated": "2025-08-25T10:32:21.277312+00:00", "context": {"id": "01K3GCJF0XQ4X6KQXJAWZTDKR5", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "0", "native_unit_of_measurement": "lx"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0xa4c138ba2d3d0757_battery", "state": "100", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "信箱門感應 Battery"}, "last_changed": "2025-08-25T10:32:21.277627+00:00", "last_reported": "2025-08-25T10:32:21.277627+00:00", "last_updated": "2025-08-25T10:32:21.277627+00:00", "context": {"id": "01K3GCJF0XTKQK2KX0E6AFCN6K", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "100", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0xa4c13870139ddef7_illuminance", "state": "0", "attributes": {"state_class": "measurement", "unit_of_measurement": "lx", "device_class": "illuminance", "friendly_name": "車庫門感應 Illuminance"}, "last_changed": "2025-08-26T12:52:31.993305+00:00", "last_reported": "2025-08-26T12:52:31.993305+00:00", "last_updated": "2025-08-26T12:52:31.993305+00:00", "context": {"id": "01K3K6ZVKSNGGKZT8TJQWRVG9Z", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "0", "native_unit_of_measurement": "lx"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0xa4c13870139ddef7_battery", "state": "100", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "車庫門感應 Battery"}, "last_changed": "2025-08-25T10:32:21.279048+00:00", "last_reported": "2025-08-25T10:32:21.279048+00:00", "last_updated": "2025-08-25T10:32:21.279048+00:00", "context": {"id": "01K3GCJF0ZP3XV96PCMDFN9Q1G", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "100", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0xa4c138591f9083d0_illuminance", "state": "0", "attributes": {"state_class": "measurement", "unit_of_measurement": "lx", "device_class": "illuminance", "friendly_name": "車庫側門感應 Illuminance"}, "last_changed": "2025-08-26T16:36:53.822163+00:00", "last_reported": "2025-08-26T16:36:53.822163+00:00", "last_updated": "2025-08-26T16:36:53.822163+00:00", "context": {"id": "01K3KKTNXYD6PYCX0WDBAYT1BZ", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "0", "native_unit_of_measurement": "lx"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0xa4c138591f9083d0_battery", "state": "100", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "車庫側門感應 Battery"}, "last_changed": "2025-08-25T10:32:21.285685+00:00", "last_reported": "2025-08-25T10:32:21.285685+00:00", "last_updated": "2025-08-25T10:32:21.285685+00:00", "context": {"id": "01K3GCJF15MSDXK9GVXN86J95P", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "100", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0x00158d000708748f_battery", "state": "100", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "信箱感應 Battery"}, "last_changed": "2025-08-25T10:32:21.289854+00:00", "last_reported": "2025-08-25T10:32:21.289854+00:00", "last_updated": "2025-08-25T10:32:21.289854+00:00", "context": {"id": "01K3GCJF19NVYTP1PGADNTBXCV", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "100", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0x00158d000708748f_device_temperature", "state": "33", "attributes": {"state_class": "measurement", "unit_of_measurement": "°C", "device_class": "temperature", "friendly_name": "信箱感應 Temperature"}, "last_changed": "2025-08-26T14:57:15.104690+00:00", "last_reported": "2025-08-26T14:57:15.104690+00:00", "last_updated": "2025-08-26T14:57:15.104690+00:00", "context": {"id": "01K3KE47B02JCZ4322S7G8H1C3", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "33", "native_unit_of_measurement": "°C"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0x00158d000708748f_voltage", "state": "3045", "attributes": {"state_class": "measurement", "unit_of_measurement": "mV", "device_class": "voltage", "friendly_name": "信箱感應 Voltage"}, "last_changed": "2025-08-26T15:47:28.215691+00:00", "last_reported": "2025-08-26T15:47:28.215691+00:00", "last_updated": "2025-08-26T15:47:28.215691+00:00", "context": {"id": "01K3KH05TQHS19AK7WNCTQGA5B", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "3045", "native_unit_of_measurement": "mV"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0x842712fffe7ce6d3_illumination", "state": "dim", "attributes": {"friendly_name": "3F 動作感應 Illumination"}, "last_changed": "2025-08-26T15:00:18.288643+00:00", "last_reported": "2025-08-26T15:00:18.288643+00:00", "last_updated": "2025-08-26T15:00:18.288643+00:00", "context": {"id": "01K3KE9T7GZWZ31MRG5FDDZR6Z", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "dim", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0x842712fffe7ce6d3_battery", "state": "100", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "3F 動作感應 Battery"}, "last_changed": "2025-08-25T10:32:21.292520+00:00", "last_reported": "2025-08-25T10:32:21.292520+00:00", "last_updated": "2025-08-25T10:32:21.292520+00:00", "context": {"id": "01K3GCJF1C1EW7CGEREDVJ2J3A", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "100", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0x842712fffe7ce6d3_voltage", "state": "3000", "attributes": {"state_class": "measurement", "unit_of_measurement": "mV", "device_class": "voltage", "friendly_name": "3F 動作感應 Voltage"}, "last_changed": "2025-08-25T10:32:21.292288+00:00", "last_reported": "2025-08-25T10:32:21.292288+00:00", "last_updated": "2025-08-25T10:32:21.292288+00:00", "context": {"id": "01K3GCJF1C5E265RV156PYPMJR", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "3000", "native_unit_of_measurement": "mV"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0xf84477fffe4d19bd_work_state", "state": "standby", "attributes": {"friendly_name": "窗簾右 Work state"}, "last_changed": "2025-08-25T10:32:21.346559+00:00", "last_reported": "2025-08-25T10:32:21.346559+00:00", "last_updated": "2025-08-25T10:32:21.346559+00:00", "context": {"id": "01K3GCJF32XJRAM8GAP5ZRH8TP", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "standby", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0xf84477fffe4d19bd_battery", "state": "100", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "窗簾右 Battery"}, "last_changed": "2025-08-25T10:32:21.346334+00:00", "last_reported": "2025-08-25T10:32:21.346334+00:00", "last_updated": "2025-08-25T10:32:21.346334+00:00", "context": {"id": "01K3GCJF3259RS7B7H8M7CQK5F", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "100", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0xf84477fffe40747a_work_state", "state": "learning", "attributes": {"friendly_name": "窗簾左 Work state"}, "last_changed": "2025-08-25T10:32:21.350876+00:00", "last_reported": "2025-08-25T10:32:21.350876+00:00", "last_updated": "2025-08-25T10:32:21.350876+00:00", "context": {"id": "01K3GCJF36TDJB51K2NVCA4PD7", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "learning", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0xf84477fffe40747a_battery", "state": "50", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "窗簾左 Battery"}, "last_changed": "2025-08-25T10:32:21.350592+00:00", "last_reported": "2025-08-25T10:32:21.350592+00:00", "last_updated": "2025-08-25T10:32:21.350592+00:00", "context": {"id": "01K3GCJF36D83E1B31PX7AXXQX", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "50", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.0xa4c138d20c4e154d_indicator", "state": "on", "attributes": {"friendly_name": "玄關人在感應 Indicator"}, "last_changed": "2025-08-25T10:32:21.215852+00:00", "last_reported": "2025-08-25T10:32:21.215852+00:00", "last_updated": "2025-08-25T10:32:21.215852+00:00", "context": {"id": "01K3GCJEYZEXWNWNBEEF3B4RAG", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.0x6cfd22fffe19abea_backlight_mode", "state": "on", "attributes": {"friendly_name": "車庫燈開關 Backlight mode"}, "last_changed": "2025-08-25T10:32:21.236152+00:00", "last_reported": "2025-08-25T10:32:21.236152+00:00", "last_updated": "2025-08-25T10:32:21.236152+00:00", "context": {"id": "01K3GCJEZMDKQXZ4G4QJV3WWDD", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.0x70c59cfffe264997_l1", "state": "off", "attributes": {"friendly_name": "4F 樓梯開關 L1"}, "last_changed": "2025-08-25T10:32:21.295726+00:00", "last_reported": "2025-08-25T10:32:21.295726+00:00", "last_updated": "2025-08-25T10:32:21.295726+00:00", "context": {"id": "01K3GCJF1FZ2ZM25QVV7KKX7S5", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.0x70c59cfffe264997_backlight_mode", "state": "on", "attributes": {"friendly_name": "4F 樓梯開關 Backlight mode"}, "last_changed": "2025-08-25T10:32:21.295624+00:00", "last_reported": "2025-08-25T10:32:21.295624+00:00", "last_updated": "2025-08-25T10:32:21.295624+00:00", "context": {"id": "01K3GCJF1FM39AA6NT7X91FAVJ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.0xa4c138a6ff2209eb_do_not_disturb", "state": "off", "attributes": {"friendly_name": "4F 浴室燈 Do not disturb"}, "last_changed": "2025-08-25T10:32:21.330682+00:00", "last_reported": "2025-08-25T10:32:21.330682+00:00", "last_updated": "2025-08-25T10:32:21.330682+00:00", "context": {"id": "01K3GCJF2JJG6N7FHA003Y25AZ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.0xa4c138ec6626cadd_do_not_disturb", "state": "on", "attributes": {"friendly_name": "餐廳中間崁燈 Do not disturb"}, "last_changed": "2025-08-25T10:32:21.332284+00:00", "last_reported": "2025-08-25T10:32:21.332284+00:00", "last_updated": "2025-08-25T10:32:21.332284+00:00", "context": {"id": "01K3GCJF2MB9XESZCZATSSYED3", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.0xa4c13844a96c99c6_do_not_disturb", "state": "on", "attributes": {"friendly_name": "餐廳右崁燈 Do not disturb"}, "last_changed": "2025-08-25T10:32:21.333179+00:00", "last_reported": "2025-08-25T10:32:21.333179+00:00", "last_updated": "2025-08-25T10:32:21.333179+00:00", "context": {"id": "01K3GCJF2NMNV7N9FCF74JM6WG", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.0xa4c13814b628056b_do_not_disturb", "state": "on", "attributes": {"friendly_name": "客廳右吊燈 Do not disturb"}, "last_changed": "2025-08-25T10:32:21.335385+00:00", "last_reported": "2025-08-25T10:32:21.335385+00:00", "last_updated": "2025-08-25T10:32:21.335385+00:00", "context": {"id": "01K3GCJF2QP0MJB17CT9BX7NY8", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.0xa4c138a6755b559d_do_not_disturb", "state": "on", "attributes": {"friendly_name": "餐廳左崁燈 Do not disturb"}, "last_changed": "2025-08-25T10:32:21.337423+00:00", "last_reported": "2025-08-25T10:32:21.337423+00:00", "last_updated": "2025-08-25T10:32:21.337423+00:00", "context": {"id": "01K3GCJF2SR6Z0292Q0GJ8RKGS", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.0xa4c138385587e524_do_not_disturb", "state": "on", "attributes": {"friendly_name": "客廳左下崁燈 Do not disturb"}, "last_changed": "2025-08-25T10:32:21.343185+00:00", "last_reported": "2025-08-25T10:32:21.343185+00:00", "last_updated": "2025-08-25T10:32:21.343185+00:00", "context": {"id": "01K3GCJF2ZGVPTCPREY2EW2X0G", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.0xa4c13893935668b4_do_not_disturb", "state": "off", "attributes": {"friendly_name": "客廳左上崁燈 Do not disturb"}, "last_changed": "2025-08-25T10:32:21.340438+00:00", "last_reported": "2025-08-25T10:32:21.340438+00:00", "last_updated": "2025-08-25T10:32:21.340438+00:00", "context": {"id": "01K3GCJF2W30SD6RR43S7WKXEQ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.0xa4c1380a172e5a6a_do_not_disturb", "state": "on", "attributes": {"friendly_name": "客廳右上崁燈 Do not disturb"}, "last_changed": "2025-08-25T10:32:21.339662+00:00", "last_reported": "2025-08-25T10:32:21.339662+00:00", "last_updated": "2025-08-25T10:32:21.339662+00:00", "context": {"id": "01K3GCJF2VZM16YBX6W78X9MW4", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.0xa4c138d449095da3_do_not_disturb", "state": "on", "attributes": {"friendly_name": "客廳右下崁燈 Do not disturb"}, "last_changed": "2025-08-25T10:32:21.338598+00:00", "last_reported": "2025-08-25T10:32:21.338598+00:00", "last_updated": "2025-08-25T10:32:21.338598+00:00", "context": {"id": "01K3GCJF2TZWWAD52RZY0VE2BQ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "update.0x842712fffe7cec33", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "8705", "in_progress": false, "latest_version": "8705", "release_summary": null, "release_url": null, "skipped_version": null, "title": null, "update_percentage": null, "device_class": "firmware", "entity_picture": "https://github.com/Koenkk/zigbee2mqtt/raw/master/images/logo.png", "friendly_name": "玄關動作感應", "supported_features": 5}, "last_changed": "2025-08-25T10:32:21.230739+00:00", "last_reported": "2025-08-25T10:32:21.230739+00:00", "last_updated": "2025-08-25T10:32:21.230739+00:00", "context": {"id": "01K3GCJEZE7BTKD1SPJGQT7273", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "update.0x842712fffe7ce6d3", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "8705", "in_progress": false, "latest_version": "8705", "release_summary": null, "release_url": null, "skipped_version": null, "title": null, "update_percentage": null, "device_class": "firmware", "entity_picture": "https://github.com/Koenkk/zigbee2mqtt/raw/master/images/logo.png", "friendly_name": "3F 動作感應", "supported_features": 5}, "last_changed": "2025-08-25T10:32:21.292087+00:00", "last_reported": "2025-08-25T10:32:21.292087+00:00", "last_updated": "2025-08-25T10:32:21.292087+00:00", "context": {"id": "01K3GCJF1CTKWKT5PNGSSY47PP", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "light.livingroom_celing", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "客廳吊燈", "supported_features": 44}, "last_changed": "2025-08-27T10:29:06.363348+00:00", "last_reported": "2025-08-27T10:29:06.363348+00:00", "last_updated": "2025-08-27T10:29:06.363348+00:00", "context": {"id": "01K3NH5YNV556TE33B1AQQ4FEY", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T10:29:39.008339+00:00"}, {"state": {"entity_id": "light.livingroom_downlight", "state": "on", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": "color_temp", "brightness": 33, "color_temp_kelvin": 2000, "color_temp": 500, "hs_color": [30.601, 94.547], "rgb_color": [255, 137, 14], "xy_color": [0.598, 0.383], "friendly_name": "客廳崁燈", "supported_features": 44}, "last_changed": "2025-08-27T10:23:27.005378+00:00", "last_reported": "2025-08-27T10:23:27.005378+00:00", "last_updated": "2025-08-27T10:23:27.005378+00:00", "context": {"id": "01K3NGVK8XGFG31Z2REA8MWXKB", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T10:38:17.156822+00:00"}, {"state": {"entity_id": "light.livingroom_ceiling", "state": "unavailable", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "friendly_name": "客廳吊燈", "supported_features": 44}, "last_changed": "2025-08-27T10:38:22.514324+00:00", "last_reported": "2025-08-27T10:38:22.514324+00:00", "last_updated": "2025-08-27T10:38:22.514324+00:00", "context": {"id": "01K3NHPXSJ0QFSMHSQZEF8NMDR", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-27T10:38:31.359064+00:00"}]}