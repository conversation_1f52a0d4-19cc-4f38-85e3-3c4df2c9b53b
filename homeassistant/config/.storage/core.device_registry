{"version": 1, "minor_version": 11, "key": "core.device_registry", "data": {"devices": [{"area_id": null, "config_entries": ["01JMMV41ZT20GXGE71FM3YDD1K"], "config_entries_subentries": {"01JMMV41ZT20GXGE71FM3YDD1K": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-02-21T18:08:08.191004+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "224fb598df617a94050d0f9017f3b99d", "identifiers": [["sun", "01JMMV41ZT20GXGE71FM3YDD1K"]], "labels": [], "manufacturer": null, "model": null, "model_id": null, "modified_at": "2025-02-21T18:08:08.191071+00:00", "name_by_user": null, "name": "Sun", "primary_config_entry": "01JMMV41ZT20GXGE71FM3YDD1K", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "living_room", "config_entries": ["01JMMV4BDHQM8D07E8FB4XW83N"], "config_entries_subentries": {"01JMMV4BDHQM8D07E8FB4XW83N": [null]}, "configuration_url": "http://192.168.1.133:1400/support/review", "connections": [["upnp", "uuid:RINCON_7828CA06C23E01400"], ["mac", "78:28:ca:06:c2:3e"]], "created_at": "2025-02-21T18:08:18.936612+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "fec17cde2354e6b75761074c54c03401", "identifiers": [["sonos", "RINCON_7828CA06C23E01400"]], "labels": [], "manufacturer": "Sonos", "model": "One", "model_id": "S13", "modified_at": "2025-05-26T07:41:40.412544+00:00", "name_by_user": "Sonos One", "name": "Sonos One", "primary_config_entry": "01JMMV4BDHQM8D07E8FB4XW83N", "serial_number": null, "sw_version": "17.0", "via_device_id": null}, {"area_id": null, "config_entries": ["01JMNT8RYCD1W54KZNXHSKAB5M"], "config_entries_subentries": {"01JMNT8RYCD1W54KZNXHSKAB5M": [null]}, "configuration_url": "https://www.met.no/en", "connections": [], "created_at": "2025-02-22T03:12:29.812521+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "858d6a1f1c83bfaa5c41d6c48e569bf5", "identifiers": [["met", "01JMNT8RYCD1W54KZNXHSKAB5M"]], "labels": [], "manufacturer": "Met.no", "model": "Forecast", "model_id": null, "modified_at": "2025-02-22T03:12:29.812588+00:00", "name_by_user": null, "name": "Forecast", "primary_config_entry": "01JMNT8RYCD1W54KZNXHSKAB5M", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01JMNXGBY4P6VCMHFQ3A1EHDSF"], "config_entries_subentries": {"01JMNXGBY4P6VCMHFQ3A1EHDSF": [null]}, "configuration_url": "homeassistant://hacs", "connections": [], "created_at": "2025-02-22T04:09:06.319358+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "cb4cfedd5381a4a025822187b4d938d2", "identifiers": [["hacs", "0717a0cd-745c-48fd-9b16-c8534c9704f9-bc944b0f-fd42-4a58-a072-ade38d1444cd"]], "labels": [], "manufacturer": "hacs.xyz", "model": "", "model_id": null, "modified_at": "2025-02-22T04:09:06.319450+00:00", "name_by_user": null, "name": "HACS", "primary_config_entry": "01JMNXGBY4P6VCMHFQ3A1EHDSF", "serial_number": null, "sw_version": "2.0.5", "via_device_id": null}, {"area_id": null, "config_entries": ["01JMNXGBY4P6VCMHFQ3A1EHDSF"], "config_entries_subentries": {"01JMNXGBY4P6VCMHFQ3A1EHDSF": [null]}, "configuration_url": "homeassistant://hacs/repository/444350375", "connections": [], "created_at": "2025-02-22T04:09:37.421216+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "e2ce133463d594028e1ec7233598bac8", "identifiers": [["hacs", "444350375"]], "labels": [], "manufacturer": "piitaya", "model": "plugin", "model_id": null, "modified_at": "2025-02-22T04:09:37.421268+00:00", "name_by_user": null, "name": "Mushroom", "primary_config_entry": "01JMNXGBY4P6VCMHFQ3A1EHDSF", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01JMNXGBY4P6VCMHFQ3A1EHDSF"], "config_entries_subentries": {"01JMNXGBY4P6VCMHFQ3A1EHDSF": [null]}, "configuration_url": "homeassistant://hacs/repository/250022973", "connections": [], "created_at": "2025-02-22T04:10:34.810488+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "ffbdc09726e61fa856f7f9686b0ba222", "identifiers": [["hacs", "250022973"]], "labels": [], "manufacturer": "ollo69", "model": "integration", "model_id": null, "modified_at": "2025-02-22T04:10:34.810543+00:00", "name_by_user": null, "name": "SmartThinQ LGE Sensors", "primary_config_entry": "01JMNXGBY4P6VCMHFQ3A1EHDSF", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01JMNXGBY4P6VCMHFQ3A1EHDSF"], "config_entries_subentries": {"01JMNXGBY4P6VCMHFQ3A1EHDSF": [null]}, "configuration_url": "homeassistant://hacs/repository/323923603", "connections": [], "created_at": "2025-02-22T04:10:53.278514+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "91fcec662b0ce2c30f080714fc4e1e99", "identifiers": [["hacs", "323923603"]], "labels": [], "manufacturer": "petret<PERSON><PERSON>a", "model": "integration", "model_id": null, "modified_at": "2025-02-22T04:10:53.278579+00:00", "name_by_user": null, "name": "Tapo Controller", "primary_config_entry": "01JMNXGBY4P6VCMHFQ3A1EHDSF", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01JMNXGBY4P6VCMHFQ3A1EHDSF"], "config_entries_subentries": {"01JMNXGBY4P6VCMHFQ3A1EHDSF": [null]}, "configuration_url": "homeassistant://hacs/repository/362700564", "connections": [], "created_at": "2025-02-22T04:11:17.834254+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "3f48465f33f391046a2624c3966f133d", "identifiers": [["hacs", "362700564"]], "labels": [], "manufacturer": "alandtse", "model": "integration", "model_id": null, "modified_at": "2025-02-22T04:11:17.834305+00:00", "name_by_user": null, "name": "Tesla", "primary_config_entry": "01JMNXGBY4P6VCMHFQ3A1EHDSF", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01JMNXGBY4P6VCMHFQ3A1EHDSF"], "config_entries_subentries": {"01JMNXGBY4P6VCMHFQ3A1EHDSF": [null]}, "configuration_url": "homeassistant://hacs/repository/291484700", "connections": [], "created_at": "2025-02-22T04:15:27.813310+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "1a7364d915325b3513b860b804bfa49d", "identifiers": [["hacs", "291484700"]], "labels": [], "manufacturer": "AlexxIT", "model": "integration", "model_id": null, "modified_at": "2025-02-22T04:15:27.813359+00:00", "name_by_user": null, "name": "Xiaomi Gateway 3", "primary_config_entry": "01JMNXGBY4P6VCMHFQ3A1EHDSF", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01JMPBZ9Q234X0N4FJ20CS3Y9C"], "config_entries_subentries": {"01JMPBZ9Q234X0N4FJ20CS3Y9C": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-02-22T08:21:52.482619+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "04f94b477e869402cdf3b0fc19364b62", "identifiers": [["mobile_app", "22FAF99C-5EC1-4491-BE3B-4285ADF6F7F2"]], "labels": [], "manufacturer": "Apple", "model": "iPhone17,1", "model_id": null, "modified_at": "2025-08-23T20:20:37.521043+00:00", "name_by_user": null, "name": "<PERSON><PERSON>s iPhone 16 Pro", "primary_config_entry": "01JMPBZ9Q234X0N4FJ20CS3Y9C", "serial_number": null, "sw_version": "18.6.2", "via_device_id": null}, {"area_id": null, "config_entries": ["01JMNXGBY4P6VCMHFQ3A1EHDSF"], "config_entries_subentries": {"01JMNXGBY4P6VCMHFQ3A1EHDSF": [null]}, "configuration_url": "homeassistant://hacs/repository/300358676", "connections": [], "created_at": "2025-02-22T15:49:04.212086+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "20eef0b6f59a609a6cce4d81048d05ef", "identifiers": [["hacs", "300358676"]], "labels": [], "manufacturer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model": "integration", "model_id": null, "modified_at": "2025-02-22T15:49:04.212147+00:00", "name_by_user": null, "name": "Tapo: Cameras Control", "primary_config_entry": "01JMNXGBY4P6VCMHFQ3A1EHDSF", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "living_room", "config_entries": ["01JMMV4BDHQM8D07E8FB4XW83N"], "config_entries_subentries": {"01JMMV4BDHQM8D07E8FB4XW83N": [null]}, "configuration_url": "http://192.168.1.152:1400/support/review", "connections": [["mac", "80:4a:f2:43:41:f6"], ["upnp", "uuid:RINCON_804AF24341F601400"]], "created_at": "2025-02-24T09:50:50.369958+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "2352e0293b0eba01d337c1085140aef7", "identifiers": [["sonos", "RINCON_804AF24341F601400"]], "labels": [], "manufacturer": "Sonos", "model": "<PERSON><PERSON>", "model_id": "S31", "modified_at": "2025-08-07T09:45:00.229157+00:00", "name_by_user": "Son<PERSON> Beam", "name": "Son<PERSON> Beam", "primary_config_entry": "01JMMV4BDHQM8D07E8FB4XW83N", "serial_number": null, "sw_version": "17.1.1", "via_device_id": null}, {"area_id": "living_room", "config_entries": ["01JMVQVK1MHFN095DDPB261EDG"], "config_entries_subentries": {"01JMVQVK1MHFN095DDPB261EDG": [null]}, "configuration_url": null, "connections": [["mac", "8c:ea:48:6a:97:5c"], ["upnp", "uuid:fc19a986-2249-4793-93cd-dc0d5b3625bb"]], "created_at": "2025-02-24T10:25:46.620231+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "8fe510650bcc6af920d86b2f1c03ffbb", "identifiers": [["samsungtv", "0ee3f5b9-6802-43ec-851b-31445b32ec72"], ["samsungtv_smart", "0ee3f5b9-6802-43ec-851b-31445b32ec72"]], "labels": [], "manufacturer": "Samsung", "model": "QA65Q70TAWXZW", "model_id": "QA65Q70TAWXZW", "modified_at": "2025-03-18T08:22:28.987927+00:00", "name_by_user": null, "name": "Samsung Q70 Series (65)", "primary_config_entry": "01JMVQVK1MHFN095DDPB261EDG", "serial_number": null, "sw_version": "Tizen", "via_device_id": null}, {"area_id": null, "config_entries": ["01JMNXGBY4P6VCMHFQ3A1EHDSF"], "config_entries_subentries": {"01JMNXGBY4P6VCMHFQ3A1EHDSF": [null]}, "configuration_url": "homeassistant://hacs/repository/249381778", "connections": [], "created_at": "2025-02-27T10:19:46.565788+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "19f718e1af7eecfa0779fba0677c4874", "identifiers": [["hacs", "249381778"]], "labels": [], "manufacturer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, postlund", "model": "integration", "model_id": null, "modified_at": "2025-02-27T10:19:46.565851+00:00", "name_by_user": null, "name": "Local Tuya", "primary_config_entry": "01JMNXGBY4P6VCMHFQ3A1EHDSF", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "kitchen", "config_entries": ["01JN5C7B8J158YAXXKZPBQ2M20"], "config_entries_subentries": {"01JN5C7B8J158YAXXKZPBQ2M20": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-02-28T04:14:58.418466+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "5838a9e5766ae00e7d6fb54f691b2621", "identifiers": [["smartthinq_sensors", "9cd5ebb3-1b1b-11c5-85e2-1c3929c5bcb1"]], "labels": [], "manufacturer": "LG", "model": "2RES1VE41PFC2-SS (REFRIGERATOR)", "model_id": null, "modified_at": "2025-05-23T08:28:52.910875+00:00", "name_by_user": null, "name": "冰箱", "primary_config_entry": "01JN5C7B8J158YAXXKZPBQ2M20", "serial_number": null, "sw_version": "clip_hna_v1.9.218", "via_device_id": null}, {"area_id": "balcony", "config_entries": ["01JN5C7B8J158YAXXKZPBQ2M20"], "config_entries_subentries": {"01JN5C7B8J158YAXXKZPBQ2M20": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-02-28T04:14:58.419889+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "14691893e3e299a6a17be27c9c58ccee", "identifiers": [["smartthinq_sensors", "84ba5cde-aa4b-1591-88fb-34e6e6a39996"]], "labels": [], "manufacturer": "LG", "model": "SDH_WT4106_TW-Dryer (TOWER_DRYER)", "model_id": null, "modified_at": "2025-04-12T05:48:14.948433+00:00", "name_by_user": null, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "primary_config_entry": "01JN5C7B8J158YAXXKZPBQ2M20", "serial_number": null, "sw_version": "clip_ble_v1.9.215", "via_device_id": null}, {"area_id": "balcony", "config_entries": ["01JN5C7B8J158YAXXKZPBQ2M20"], "config_entries_subentries": {"01JN5C7B8J158YAXXKZPBQ2M20": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-02-28T04:14:58.422858+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "28f5141649045f82736be341067f0c0a", "identifiers": [["smartthinq_sensors", "0a895ad7-cde3-1ceb-9006-34e6e6a399e8"]], "labels": [], "manufacturer": "LG", "model": "T_VA_Y___W.B__BTAT-FL (TOWER_WASHER)", "model_id": null, "modified_at": "2025-04-12T05:48:14.949835+00:00", "name_by_user": null, "name": "洗衣機", "primary_config_entry": "01JN5C7B8J158YAXXKZPBQ2M20", "serial_number": null, "sw_version": "clip_ble_v1.9.215", "via_device_id": null}, {"area_id": "garage", "config_entries": ["01JN5C7B8J158YAXXKZPBQ2M20"], "config_entries_subentries": {"01JN5C7B8J158YAXXKZPBQ2M20": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-02-28T04:14:58.427418+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "d4377a8c6842a4fbf84e2d5576427284", "identifiers": [["smartthinq_sensors", "8c4268b0-0e9f-11f5-a1b3-203dbd7185af"]], "labels": [], "manufacturer": "LG", "model": "FH0D7DDMK0_WIFI-FL (WASHER)", "model_id": null, "modified_at": "2025-02-28T04:15:12.304600+00:00", "name_by_user": null, "name": "洗機", "primary_config_entry": "01JN5C7B8J158YAXXKZPBQ2M20", "serial_number": null, "sw_version": "QC_Modem_1.2.80", "via_device_id": null}, {"area_id": "kitchen", "config_entries": ["01JN6W64VFF71GFGRK5RRY3SH2"], "config_entries_subentries": {"01JN6W64VFF71GFGRK5RRY3SH2": [null]}, "configuration_url": null, "connections": [["mac", "b0:4a:39:88:7b:e0"]], "created_at": "2025-02-28T18:13:08.709742+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "8dad38d0a2498b75f53e58220e1f1116", "identifiers": [["roborock", "3Xarg2kA4zmSRCDShTwmsM"]], "labels": [], "manufacturer": "<PERSON><PERSON><PERSON>", "model": "roborock.vacuum.a70", "model_id": "roborock.vacuum.a70", "modified_at": "2025-07-12T18:01:49.511644+00:00", "name_by_user": null, "name": "S8 Pro Ultra", "primary_config_entry": "01JN6W64VFF71GFGRK5RRY3SH2", "serial_number": null, "sw_version": "02.20.30", "via_device_id": null}, {"area_id": null, "config_entries": ["01JMNXGBY4P6VCMHFQ3A1EHDSF"], "config_entries_subentries": {"01JMNXGBY4P6VCMHFQ3A1EHDSF": [null]}, "configuration_url": "homeassistant://hacs/repository/612978245", "connections": [], "created_at": "2025-03-01T06:31:00.849645+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "d5dd9afa0426c28e9200fd878ba39192", "identifiers": [["hacs", "612978245"]], "labels": [], "manufacturer": "libdyson-wg, dot<PERSON>z, cmgrayb", "model": "integration", "model_id": null, "modified_at": "2025-08-19T08:29:31.445458+00:00", "name_by_user": null, "name": "<PERSON><PERSON><PERSON>", "primary_config_entry": "01JMNXGBY4P6VCMHFQ3A1EHDSF", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01JMNXGBY4P6VCMHFQ3A1EHDSF"], "config_entries_subentries": {"01JMNXGBY4P6VCMHFQ3A1EHDSF": [null]}, "configuration_url": "homeassistant://hacs/repository/366332990", "connections": [], "created_at": "2025-03-02T08:50:23.432820+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "04078ff88c0faedf10fe929d5982c596", "identifiers": [["hacs", "366332990"]], "labels": [], "manufacturer": "<PERSON><PERSON><PERSON><PERSON>", "model": "integration", "model_id": null, "modified_at": "2025-03-02T08:50:23.432891+00:00", "name_by_user": null, "name": "Electrolux Wellbeing", "primary_config_entry": "01JMNXGBY4P6VCMHFQ3A1EHDSF", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "bedroom", "config_entries": ["01JNB11GB9FM7DH4XM5WPF3H81"], "config_entries_subentries": {"01JNB11GB9FM7DH4XM5WPF3H81": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-03-02T08:54:53.883341+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "14c66717bc0e1373dc56e5691388548d", "identifiers": [["dyson_local", "A1N-TW-NHA0167A"]], "labels": [], "manufacturer": "<PERSON><PERSON><PERSON>", "model": "527", "model_id": null, "modified_at": "2025-03-02T08:54:59.500494+00:00", "name_by_user": null, "name": "A1N-TW-NHA0167A", "primary_config_entry": "01JNB11GB9FM7DH4XM5WPF3H81", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01JMNXGBY4P6VCMHFQ3A1EHDSF"], "config_entries_subentries": {"01JMNXGBY4P6VCMHFQ3A1EHDSF": [null]}, "configuration_url": "homeassistant://hacs/repository/256928684", "connections": [], "created_at": "2025-03-02T09:28:46.382641+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "1e3e1166c914504ba382f14042577c60", "identifiers": [["hacs", "256928684"]], "labels": [], "manufacturer": "make-all", "model": "integration", "model_id": null, "modified_at": "2025-03-02T09:28:46.382698+00:00", "name_by_user": null, "name": "Tuya Local", "primary_config_entry": "01JMNXGBY4P6VCMHFQ3A1EHDSF", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01JMNXGBY4P6VCMHFQ3A1EHDSF"], "config_entries_subentries": {"01JMNXGBY4P6VCMHFQ3A1EHDSF": [null]}, "configuration_url": "homeassistant://hacs/repository/247070270", "connections": [], "created_at": "2025-03-03T15:36:24.956815+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "025819e9f0bb77eff37c5c722c0a045f", "identifiers": [["hacs", "247070270"]], "labels": [], "manufacturer": "<PERSON><PERSON>, ollo69, screw<PERSON>h", "model": "integration", "model_id": null, "modified_at": "2025-03-03T15:36:24.956882+00:00", "name_by_user": null, "name": "SamsungTV Smart", "primary_config_entry": "01JMNXGBY4P6VCMHFQ3A1EHDSF", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01JNEFSD8DD5F66YQ0NH3ACJ8T"], "config_entries_subentries": {"01JNEFSD8DD5F66YQ0NH3ACJ8T": [null]}, "configuration_url": null, "connections": [["mac", "ae:4b:bc:e6:06:c1"]], "created_at": "2025-03-03T17:10:35.560506+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "28cd15a74dec73a2a6d242efbf2a1f88", "identifiers": [["homekit", "01JNEFSD8DD5F66YQ0NH3ACJ8T", "homekit.bridge"]], "labels": [], "manufacturer": "Home Assistant", "model": "HomeBridge", "model_id": null, "modified_at": "2025-03-03T17:10:35.560574+00:00", "name_by_user": null, "name": "Garage Bridge:21063", "primary_config_entry": "01JNEFSD8DD5F66YQ0NH3ACJ8T", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01JNEGP1PBGC9JK41DQP1ZERZY"], "config_entries_subentries": {"01JNEGP1PBGC9JK41DQP1ZERZY": [null]}, "configuration_url": null, "connections": [["mac", "55:85:ed:28:a5:58"]], "created_at": "2025-03-03T17:26:16.727973+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "05ce23f33773fa1413b1eab7e3f7fe25", "identifiers": [["homekit", "01JNEGP1PBGC9JK41DQP1ZERZY", "homekit.bridge"]], "labels": [], "manufacturer": "Home Assistant", "model": "HomeBridge", "model_id": null, "modified_at": "2025-03-03T17:26:16.728041+00:00", "name_by_user": null, "name": "Kitchen Bridge:21065", "primary_config_entry": "01JNEGP1PBGC9JK41DQP1ZERZY", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01JNEGP1PAKQY9BDABX5QBRJY4"], "config_entries_subentries": {"01JNEGP1PAKQY9BDABX5QBRJY4": [null]}, "configuration_url": null, "connections": [["mac", "c2:6e:e0:0e:1a:a2"]], "created_at": "2025-03-03T17:26:16.739649+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "2eae1841cb7a9bc3f538d405d6d43f32", "identifiers": [["homekit", "01JNEGP1PAKQY9BDABX5QBRJY4", "homekit.bridge"]], "labels": [], "manufacturer": "Home Assistant", "model": "HomeBridge", "model_id": null, "modified_at": "2025-03-03T17:26:16.739718+00:00", "name_by_user": null, "name": "Living Room Bridge:21064", "primary_config_entry": "01JNEGP1PAKQY9BDABX5QBRJY4", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01JNFJNHHGHGGMYY9AKP0JMRFW"], "config_entries_subentries": {"01JNFJNHHGHGGMYY9AKP0JMRFW": [null]}, "configuration_url": null, "connections": [["mac", "21:0a:cb:cf:4a:9f"]], "created_at": "2025-03-04T03:20:10.514954+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "2381c1d3bd69adf13fb872e4af00e448", "identifiers": [["homekit", "01JNFJNHHGHGGMYY9AKP0JMRFW", "homekit.bridge"]], "labels": [], "manufacturer": "Home Assistant", "model": "HomeBridge", "model_id": null, "modified_at": "2025-03-04T03:20:10.515020+00:00", "name_by_user": null, "name": "Balcony Bridge:21066", "primary_config_entry": "01JNFJNHHGHGGMYY9AKP0JMRFW", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "studio", "config_entries": ["01JN5C7B8J158YAXXKZPBQ2M20"], "config_entries_subentries": {"01JN5C7B8J158YAXXKZPBQ2M20": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-03-05T08:27:54.270263+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "b7584a471293c20481bc6b6c4fe280b2", "identifiers": [["smartthinq_sensors", "5d9c8d56-81b1-18f6-9065-4cbad7ff2103"]], "labels": [], "manufacturer": "LG", "model": "DHUM_056905_WW- (DEHUMIDIFIER)", "model_id": null, "modified_at": "2025-05-23T08:28:52.910177+00:00", "name_by_user": null, "name": "除濕機", "primary_config_entry": "01JN5C7B8J158YAXXKZPBQ2M20", "serial_number": null, "sw_version": "clip_hna_v1.9.218", "via_device_id": null}, {"area_id": "dress_room", "config_entries": ["01JN5C7B8J158YAXXKZPBQ2M20"], "config_entries_subentries": {"01JN5C7B8J158YAXXKZPBQ2M20": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-03-05T08:27:54.271522+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "6cd139a963a42d96973565b6f7003fbe", "identifiers": [["smartthinq_sensors", "ef0d7d89-0b42-12f1-8c5b-d48d265f2fd1"]], "labels": [], "manufacturer": "LG", "model": "S5MB_C41_XXR-ST (STYLER)", "model_id": null, "modified_at": "2025-04-15T14:40:39.778580+00:00", "name_by_user": null, "name": "電子衣櫥", "primary_config_entry": "01JN5C7B8J158YAXXKZPBQ2M20", "serial_number": null, "sw_version": "clip_hna_v1.9.215", "via_device_id": null}, {"area_id": null, "config_entries": ["01JNJSHDZ0HGGMYY9AKP0JMRFW"], "config_entries_subentries": {"01JNJSHDZ0HGGMYY9AKP0JMRFW": [null]}, "configuration_url": null, "connections": [["mac", "95:cf:22:1f:1b:8f"]], "created_at": "2025-03-05T09:18:03.420412+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "f2c17390dda6d249c6bfcba25b05c394", "identifiers": [["homekit", "01JNJSHDZ0HGGMYY9AKP0JMRFW", "homekit.bridge"]], "labels": [], "manufacturer": "Home Assistant", "model": "HomeBridge", "model_id": null, "modified_at": "2025-03-05T09:18:03.420489+00:00", "name_by_user": null, "name": "Bedroom Bridge:21067", "primary_config_entry": "01JNJSHDZ0HGGMYY9AKP0JMRFW", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01JNJSHDZ019VH2CCE3CF7V55K"], "config_entries_subentries": {"01JNJSHDZ019VH2CCE3CF7V55K": [null]}, "configuration_url": null, "connections": [["mac", "be:3a:25:38:29:23"]], "created_at": "2025-03-05T09:18:03.423538+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "898cf09bc578eddc71d030bbfcb982a2", "identifiers": [["homekit", "01JNJSHDZ019VH2CCE3CF7V55K", "homekit.bridge"]], "labels": [], "manufacturer": "Home Assistant", "model": "HomeBridge", "model_id": null, "modified_at": "2025-03-05T09:18:03.423631+00:00", "name_by_user": null, "name": "Dress Room Bridge:21069", "primary_config_entry": "01JNJSHDZ019VH2CCE3CF7V55K", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01JNJSHDZ0D5F66YQ0NH3ACJ8T"], "config_entries_subentries": {"01JNJSHDZ0D5F66YQ0NH3ACJ8T": [null]}, "configuration_url": null, "connections": [["mac", "76:14:bb:ea:62:ca"]], "created_at": "2025-03-05T09:18:03.430691+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "484b5568bc052f8280896647e74b8265", "identifiers": [["homekit", "01JNJSHDZ0D5F66YQ0NH3ACJ8T", "homekit.bridge"]], "labels": [], "manufacturer": "Home Assistant", "model": "HomeBridge", "model_id": null, "modified_at": "2025-03-05T09:18:03.430759+00:00", "name_by_user": null, "name": "Studio Bridge:21068", "primary_config_entry": "01JNJSHDZ0D5F66YQ0NH3ACJ8T", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01JNJSHDZ0S5HFNA1VR1BF41VJ"], "config_entries_subentries": {"01JNJSHDZ0S5HFNA1VR1BF41VJ": [null]}, "configuration_url": null, "connections": [["mac", "3b:b7:9e:a5:2b:6e"]], "created_at": "2025-03-05T09:18:03.433986+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "847abbcf41ff4fd3798a0a21c949dc1e", "identifiers": [["homekit", "01JNJSHDZ0S5HFNA1VR1BF41VJ", "homekit.bridge"]], "labels": [], "manufacturer": "Home Assistant", "model": "HomeBridge", "model_id": null, "modified_at": "2025-03-05T09:18:03.434034+00:00", "name_by_user": null, "name": "Stairs Bridge:21071", "primary_config_entry": "01JNJSHDZ0S5HFNA1VR1BF41VJ", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01JMNXGBY4P6VCMHFQ3A1EHDSF"], "config_entries_subentries": {"01JMNXGBY4P6VCMHFQ3A1EHDSF": [null]}, "configuration_url": "homeassistant://hacs/repository/356778495", "connections": [], "created_at": "2025-03-07T08:54:00.115778+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "da312100b3e6285c4d945bc7f1903cba", "identifiers": [["hacs", "356778495"]], "labels": [], "manufacturer": "AlexxIT", "model": "integration", "model_id": null, "modified_at": "2025-03-07T08:54:00.115838+00:00", "name_by_user": null, "name": "WebRTC Camera", "primary_config_entry": "01JMNXGBY4P6VCMHFQ3A1EHDSF", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "living_room", "config_entries": ["01JNQX6SQV4H932G788M8N2S7Q"], "config_entries_subentries": {"01JNQX6SQV4H932G788M8N2S7Q": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-03-07T08:58:03.821220+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "99b662d7a087785732aff657822beb9a", "identifiers": [["tuya", "eb421e0edbba6969a9uvbk"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "<PERSON>  (unsupported)", "model_id": "tbrdiazy05vzj6tf", "modified_at": "2025-04-21T09:39:55.917057+00:00", "name_by_user": null, "name": "Pet Marvel 全自動智慧貓砂盆", "primary_config_entry": "01JNQX6SQV4H932G788M8N2S7Q", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "studio", "config_entries": ["01JNQX6SQV4H932G788M8N2S7Q"], "config_entries_subentries": {"01JNQX6SQV4H932G788M8N2S7Q": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-03-07T08:58:03.821991+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "ea99f089dbb1c81a7e439717b8b98e64", "identifiers": [["tuya", "eb993efb2f5dc32ea0mzv6"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "smart camera", "model_id": "6vxt8qnsmaooxsxy", "modified_at": "2025-08-29T15:04:40.379349+00:00", "name_by_user": null, "name": "書房監控", "primary_config_entry": "01JNQX6SQV4H932G788M8N2S7Q", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "living_room", "config_entries": ["01JNREM9RFVV4ZR3Y7TRRKFVSA"], "config_entries_subentries": {"01JNREM9RFVV4ZR3Y7TRRKFVSA": [null]}, "configuration_url": null, "connections": [["mac", "a8:51:ab:d0:b3:83"], ["mac", "9a:89:85:8e:10:c4"]], "created_at": "2025-03-07T14:02:29.730678+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "13159b4d95697471e1deb919615541aa", "identifiers": [["apple_tv", "A8:51:AB:D0:B3:83"]], "labels": [], "manufacturer": "Apple", "model": "Apple TV 4K (gen 3)", "model_id": null, "modified_at": "2025-08-13T21:55:31.435267+00:00", "name_by_user": null, "name": "Apple TV 4K", "primary_config_entry": "01JNREM9RFVV4ZR3Y7TRRKFVSA", "serial_number": null, "sw_version": "18.6", "via_device_id": null}, {"area_id": "living_room", "config_entries": ["01JNRF0S2KD83Y8TSD6HVBGYNC"], "config_entries_subentries": {"01JNRF0S2KD83Y8TSD6HVBGYNC": [null]}, "configuration_url": null, "connections": [["mac", "7e:d8:e1:f6:4e:85"]], "created_at": "2025-03-07T14:09:17.708281+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "d9b0316b2c47c1992c5e211f14208dbe", "identifiers": [["apple_tv", "7E:D8:E1:F6:4E:85"]], "labels": [], "manufacturer": "Apple", "model": "HomePod Mini", "model_id": null, "modified_at": "2025-08-04T03:01:28.562898+00:00", "name_by_user": null, "name": "客廳", "primary_config_entry": "01JNRF0S2KD83Y8TSD6HVBGYNC", "serial_number": null, "sw_version": "18.6", "via_device_id": null}, {"area_id": null, "config_entries": ["01JMNXGBY4P6VCMHFQ3A1EHDSF"], "config_entries_subentries": {"01JMNXGBY4P6VCMHFQ3A1EHDSF": [null]}, "configuration_url": "homeassistant://hacs/repository/290261325", "connections": [], "created_at": "2025-03-12T01:33:20.040089+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "896fa566b51efebd357ff993a81e4c0b", "identifiers": [["hacs", "290261325"]], "labels": [], "manufacturer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, th3w1zard1, protyposis", "model": "integration", "model_id": null, "modified_at": "2025-03-12T01:33:20.040151+00:00", "name_by_user": null, "name": "Adaptive Lighting", "primary_config_entry": "01JMNXGBY4P6VCMHFQ3A1EHDSF", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "kitchen", "config_entries": ["01JP4042D95AJFJW4N4QTHH1V2"], "config_entries_subentries": {"01JP4042D95AJFJW4N4QTHH1V2": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-03-03T07:34:06.057563+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "0494d9b62d41f56cf19a43ca7e90ec43", "identifiers": [["tuya_local", "eb402982bd9005e9edelog"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": null, "model_id": null, "modified_at": "2025-03-12T01:41:17.965287+00:00", "name_by_user": null, "name": "QK-CBLC5 Left", "primary_config_entry": "01JP4042D95AJFJW4N4QTHH1V2", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "kitchen", "config_entries": ["01JP408B5RE83QQS8M22MTJC7A"], "config_entries_subentries": {"01JP408B5RE83QQS8M22MTJC7A": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-03-03T07:38:57.959803+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "1ea0712b271ba118db5ceb4abb03d563", "identifiers": [["tuya_local", "eb472ce7e2274fc6befaei"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": null, "model_id": null, "modified_at": "2025-03-12T01:42:11.854780+00:00", "name_by_user": null, "name": "QK-CBLC5 Right", "primary_config_entry": "01JP408B5RE83QQS8M22MTJC7A", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "kitchen", "config_entries": ["01JNQX6SQV4H932G788M8N2S7Q"], "config_entries_subentries": {"01JNQX6SQV4H932G788M8N2S7Q": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-03-07T08:58:03.822131+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "281ef18da7f12368f3715878d1710575", "identifiers": [["tuya", "eb402982bd9005e9edelog"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "QK-CBLC5", "model_id": "ljhiptnq36rpymse", "modified_at": "2025-08-29T15:04:40.380916+00:00", "name_by_user": null, "name": "QK-CBLC5 LEFT", "primary_config_entry": "01JNQX6SQV4H932G788M8N2S7Q", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "kitchen", "config_entries": ["01JNQX6SQV4H932G788M8N2S7Q"], "config_entries_subentries": {"01JNQX6SQV4H932G788M8N2S7Q": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-03-07T08:58:03.822298+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "3ef5b4ef5c371ffd4d9907f4fc765c51", "identifiers": [["tuya", "eb472ce7e2274fc6befaei"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "QK-CBLC5", "model_id": "ljhiptnq36rpymse", "modified_at": "2025-08-29T15:04:40.381296+00:00", "name_by_user": null, "name": "QK-CBLC5 RIGHT", "primary_config_entry": "01JNQX6SQV4H932G788M8N2S7Q", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "kitchen", "config_entries": ["01JP43GYFG9Q6HNEX1HJPXW3DX"], "config_entries_subentries": {"01JP43GYFG9Q6HNEX1HJPXW3DX": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-03-12T02:39:17.491803+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "55b5ceed2c945bd25f980f1bafab4ce9", "identifiers": [["adaptive_lighting", "QK-CBLC5"]], "labels": [], "manufacturer": null, "model": null, "model_id": null, "modified_at": "2025-08-29T15:04:26.696939+00:00", "name_by_user": null, "name": "QK-CBLC5", "primary_config_entry": "01JP43GYFG9Q6HNEX1HJPXW3DX", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01JMNXGBY4P6VCMHFQ3A1EHDSF"], "config_entries_subentries": {"01JMNXGBY4P6VCMHFQ3A1EHDSF": [null]}, "configuration_url": "homeassistant://hacs/repository/390073284", "connections": [], "created_at": "2025-03-12T05:12:32.929375+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "643981fb9631a758b4576f432f45f3bb", "identifiers": [["hacs", "390073284"]], "labels": [], "manufacturer": "j<PERSON><PERSON><PERSON>", "model": "integration", "model_id": null, "modified_at": "2025-03-12T05:12:32.929436+00:00", "name_by_user": null, "name": "Sonos Cloud", "primary_config_entry": "01JMNXGBY4P6VCMHFQ3A1EHDSF", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "bedroom", "config_entries": ["01JP6KQGGSMS0THTR2GQQWMGHP"], "config_entries_subentries": {"01JP6KQGGSMS0THTR2GQQWMGHP": [null]}, "configuration_url": null, "connections": [["mac", "00:7c:2d:f7:ca:e8"]], "created_at": "2025-03-13T02:00:59.031901+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "36745693dc798511854c2228b8e5212c", "identifiers": [["samsungtv", "75b69464-8987-48bc-a0ad-ff1abea87d6d"]], "labels": [], "manufacturer": "Samsung", "model": "UA55RU7400WXZW", "model_id": "UA55RU7400WXZW", "modified_at": "2025-03-13T02:01:08.821870+00:00", "name_by_user": null, "name": "Samsung 7 Series (55)", "primary_config_entry": "01JP6KQGGSMS0THTR2GQQWMGHP", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01JMNXGBY4P6VCMHFQ3A1EHDSF"], "config_entries_subentries": {"01JMNXGBY4P6VCMHFQ3A1EHDSF": [null]}, "configuration_url": "homeassistant://hacs/repository/331597657", "connections": [], "created_at": "2025-02-22T04:20:50.672983+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "76b48491cfddadb58c310eccbd81dfd4", "identifiers": [["hacs", "331597657"]], "labels": [], "manufacturer": "niceboy<PERSON><PERSON>b", "model": "integration", "model_id": null, "modified_at": "2025-03-13T10:13:29.169169+00:00", "name_by_user": null, "name": "Aqara Gateway", "primary_config_entry": "01JMNXGBY4P6VCMHFQ3A1EHDSF", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01JMNXGBY4P6VCMHFQ3A1EHDSF"], "config_entries_subentries": {"01JMNXGBY4P6VCMHFQ3A1EHDSF": [null]}, "configuration_url": "homeassistant://hacs/repository/680112919", "connections": [], "created_at": "2025-03-13T17:10:00.252730+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "5bc7ec0c841966557a358c009d5e63e4", "identifiers": [["hacs", "680112919"]], "labels": [], "manufacturer": "C<PERSON><PERSON>", "model": "plugin", "model_id": null, "modified_at": "2025-03-13T17:10:00.252794+00:00", "name_by_user": null, "name": "Bubble Card", "primary_config_entry": "01JMNXGBY4P6VCMHFQ3A1EHDSF", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01JPEQJY67HJZP0MR3VAGMJ5GB"], "config_entries_subentries": {"01JPEQJY67HJZP0MR3VAGMJ5GB": [null]}, "configuration_url": null, "connections": [["mac", "0a:2e:14:fc:62:86"]], "created_at": "2025-03-16T05:42:53.780014+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "71111f11760db7479a2d22100d29e4ff", "identifiers": [["homekit", "01JPEQJY67HJZP0MR3VAGMJ5GB", "homekit.bridge"]], "labels": [], "manufacturer": "Home Assistant", "model": "HomeBridge", "model_id": null, "modified_at": "2025-03-16T05:42:53.780093+00:00", "name_by_user": null, "name": "Restroom Bridge:21072", "primary_config_entry": "01JPEQJY67HJZP0MR3VAGMJ5GB", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01JMNXGBY4P6VCMHFQ3A1EHDSF"], "config_entries_subentries": {"01JMNXGBY4P6VCMHFQ3A1EHDSF": [null]}, "configuration_url": "homeassistant://hacs/repository/588734673", "connections": [], "created_at": "2025-03-18T08:10:06.653224+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "2971e20c1e3fd2d4a017c2f096e0f277", "identifiers": [["hacs", "588734673"]], "labels": [], "manufacturer": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Adrian<PERSON>", "model": "integration", "model_id": null, "modified_at": "2025-03-18T08:10:06.653300+00:00", "name_by_user": null, "name": "Bambu Lab", "primary_config_entry": "01JMNXGBY4P6VCMHFQ3A1EHDSF", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "studio", "config_entries": ["01JPRXF7GWYH8QBAFVJMFZN7YJ"], "config_entries_subentries": {"01JPRXF7GWYH8QBAFVJMFZN7YJ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-03-20T04:37:32.924910+00:00", "disabled_by": null, "entry_type": null, "hw_version": "AP07", "id": "e178e3d65157d3b9da90ba7078a05792", "identifiers": [["bambu_lab", "0309CA4C1901915"]], "labels": [], "manufacturer": "Bambu Lab", "model": "A1MINI", "model_id": null, "modified_at": "2025-08-29T15:04:45.095094+00:00", "name_by_user": null, "name": "A1MINI_0309CA4C1901915", "primary_config_entry": "01JPRXF7GWYH8QBAFVJMFZN7YJ", "serial_number": null, "sw_version": "01.06.00.00", "via_device_id": null}, {"area_id": "studio", "config_entries": ["01JPRXF7GWYH8QBAFVJMFZN7YJ"], "config_entries_subentries": {"01JPRXF7GWYH8QBAFVJMFZN7YJ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-03-20T04:37:32.949865+00:00", "disabled_by": null, "entry_type": null, "hw_version": "", "id": "dc474606484bfc34696337b0da52d156", "identifiers": [["bambu_lab", "0309CA4C1901915_ExternalSpool"]], "labels": [], "manufacturer": "Bambu Lab", "model": "External Spool", "model_id": null, "modified_at": "2025-04-03T11:06:22.437060+00:00", "name_by_user": null, "name": "A1MINI_0309CA4C1901915_ExternalSpool", "primary_config_entry": "01JPRXF7GWYH8QBAFVJMFZN7YJ", "serial_number": null, "sw_version": "", "via_device_id": "e178e3d65157d3b9da90ba7078a05792"}, {"area_id": "living_room", "config_entries": ["01JQ12RTREKYB5<PERSON>B21HTTYREP"], "config_entries_subentries": {"01JQ12RTREKYB5NSB21HTTYREP": [null]}, "configuration_url": "http://192.168.2.107:80", "connections": [["mac", "18:8b:0e:91:e0:ec"]], "created_at": "2025-03-23T08:44:06.173272+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "23e31d46b1fdd8ad2113bba4b67d3873", "identifiers": [], "labels": [], "manufacturer": "TaiSEIA-Hitachi", "model": "Climate", "model_id": null, "modified_at": "2025-03-23T08:44:09.651637+00:00", "name_by_user": null, "name": "Climate-H 91e0ec", "primary_config_entry": "01JQ12RTREKYB5<PERSON>B21HTTYREP", "serial_number": null, "sw_version": "ESP32C3 (ESPHome 2024.12.2)", "via_device_id": null}, {"area_id": "living_room", "config_entries": ["01JNQX6SQV4H932G788M8N2S7Q"], "config_entries_subentries": {"01JNQX6SQV4H932G788M8N2S7Q": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-03-30T09:09:26.246636+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "a162aa427eb8240d541473d61b8e81f5", "identifiers": [["tuya", "eb2d3b523e05ca7e67b1am"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "smart camera", "model_id": "6vxt8qnsmaooxsxy", "modified_at": "2025-08-29T15:04:40.381513+00:00", "name_by_user": null, "name": "客廳監控", "primary_config_entry": "01JNQX6SQV4H932G788M8N2S7Q", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "kitchen", "config_entries": ["01JNQX6SQV4H932G788M8N2S7Q"], "config_entries_subentries": {"01JNQX6SQV4H932G788M8N2S7Q": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-03-30T09:20:11.688967+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "481bf2150aa1d3a9e286e93332307214", "identifiers": [["tuya", "eb89eebbc679712d88tdtz"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "smart camera", "model_id": "6vxt8qnsmaooxsxy", "modified_at": "2025-08-29T15:04:40.381636+00:00", "name_by_user": null, "name": "廚房監控", "primary_config_entry": "01JNQX6SQV4H932G788M8N2S7Q", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "bedroom", "config_entries": ["01JNQX6SQV4H932G788M8N2S7Q"], "config_entries_subentries": {"01JNQX6SQV4H932G788M8N2S7Q": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-03-30T15:21:35.313618+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "bc070ddbe4e25df8c5eda8d4d7fba833", "identifiers": [["tuya", "eb5ab9d10d5e018bb94pl7"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "smart camera", "model_id": "6vxt8qnsmaooxsxy", "modified_at": "2025-08-29T15:04:40.382062+00:00", "name_by_user": null, "name": "臥室監控", "primary_config_entry": "01JNQX6SQV4H932G788M8N2S7Q", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "bedroom", "config_entries": ["01JQXN5DGZMSATWC7N9206D7D5"], "config_entries_subentries": {"01JQXN5DGZMSATWC7N9206D7D5": [null]}, "configuration_url": null, "connections": [["mac", "de:69:0f:a4:79:24"]], "created_at": "2025-04-03T11:04:17.108831+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "40bc2b6b1ff6ccdf13a69def52e9887f", "identifiers": [["apple_tv", "DE:69:0F:A4:79:24"]], "labels": [], "manufacturer": "Apple", "model": "HomePod Mini", "model_id": null, "modified_at": "2025-08-05T18:45:34.315202+00:00", "name_by_user": null, "name": "臥室", "primary_config_entry": "01JQXN5DGZMSATWC7N9206D7D5", "serial_number": null, "sw_version": "18.6", "via_device_id": null}, {"area_id": "garage", "config_entries": ["01JQXN7B0Y57FFWG0XXV2NWKC0"], "config_entries_subentries": {"01JQXN7B0Y57FFWG0XXV2NWKC0": [null]}, "configuration_url": null, "connections": [["mac", "9c:a2:f4:38:17:02"]], "created_at": "2025-04-03T11:05:21.457193+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1.0", "id": "b22060e976ba406d29b4e81b7af7e05a", "identifiers": [["tapo_control", "9c_a2_f4_38_17_02_tapo_control"]], "labels": [], "manufacturer": "TP-Link", "model": "C210", "model_id": null, "modified_at": "2025-07-29T09:43:06.986979+00:00", "name_by_user": null, "name": "Tapo_Camera", "primary_config_entry": "01JQXN7B0Y57FFWG0XXV2NWKC0", "serial_number": null, "sw_version": "1.4.7 Build 250625 Rel.58841n", "via_device_id": null}, {"area_id": "balcony", "config_entries": ["01JQXNCE2NHPTM976GPZYQ9MA7"], "config_entries_subentries": {"01JQXNCE2NHPTM976GPZYQ9MA7": [null]}, "configuration_url": null, "connections": [["mac", "e4:fa:c4:f6:bc:79"]], "created_at": "2025-03-02T12:52:16.845403+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1.0", "id": "00579451ba1a18c081869a9c6438d54d", "identifiers": [["tapo_control", "e4_fa_c4_f6_bc_79_tapo_control"]], "labels": [], "manufacturer": "TP-Link", "model": "C220", "model_id": null, "modified_at": "2025-08-08T13:23:33.389166+00:00", "name_by_user": null, "name": "Tapo_C220_BC79", "primary_config_entry": "01JQXNCE2NHPTM976GPZYQ9MA7", "serial_number": null, "sw_version": "1.3.2 Build 250610 Rel.61762n", "via_device_id": null}, {"area_id": "kitchen", "config_entries": ["01JN6W64VFF71GFGRK5RRY3SH2"], "config_entries_subentries": {"01JN6W64VFF71GFGRK5RRY3SH2": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-04-03T15:40:47.753153+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "22cffb8153b734925469c8cdb893c526", "identifiers": [["roborock", "3Xarg2kA4zmSRCDShTwmsM_dock"]], "labels": [], "manufacturer": "<PERSON><PERSON><PERSON>", "model": "roborock.vacuum.a70 Dock", "model_id": "7", "modified_at": "2025-07-12T18:01:49.511105+00:00", "name_by_user": null, "name": "S8 Pro Ultra Dock", "primary_config_entry": "01JN6W64VFF71GFGRK5RRY3SH2", "serial_number": null, "sw_version": "02.20.30", "via_device_id": null}, {"area_id": null, "config_entries": ["01JQY50H1CGJW3FZKK6RQV5155"], "config_entries_subentries": {"01JQY50H1CGJW3FZKK6RQV5155": [null]}, "configuration_url": "homeassistant://config/backup", "connections": [], "created_at": "2025-04-03T15:41:13.667046+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "cfb6773b3fc54dfade8875d1db89de07", "identifiers": [["backup", "backup_manager"]], "labels": [], "manufacturer": "Home Assistant", "model": "Home Assistant Backup", "model_id": null, "modified_at": "2025-08-26T17:08:45.176568+00:00", "name_by_user": null, "name": "Backup", "primary_config_entry": "01JQY50H1CGJW3FZKK6RQV5155", "serial_number": null, "sw_version": "2025.8.3", "via_device_id": null}, {"area_id": null, "config_entries": ["01JSW4RMT9F4TGKK0WKHYRPKFK"], "config_entries_subentries": {"01JSW4RMT9F4TGKK0WKHYRPKFK": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-04-27T17:29:50.178072+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "1f5e08cf1cc921587f1da95fbff3ffd1", "identifiers": [["proximity", "01JSW4RMT9F4TGKK0WKHYRPKFK"]], "labels": [], "manufacturer": null, "model": null, "model_id": null, "modified_at": "2025-04-29T10:01:03.250823+00:00", "name_by_user": null, "name": "PKDKLF", "primary_config_entry": "01JSW4RMT9F4TGKK0WKHYRPKFK", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "living_room", "config_entries": ["01JTMK1M36Y0HCAGPBV82YJATB"], "config_entries_subentries": {"01JTMK1M36Y0HCAGPBV82YJATB": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-03-02T08:52:57.686054+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "5296078f7a68eb5b0252daa8592fc948", "identifiers": [["wellbeing", "900943148051000755087076"]], "labels": [], "manufacturer": "ELECTROLUX", "model": "PUREA9", "model_id": null, "modified_at": "2025-05-07T05:21:19.025088+00:00", "name_by_user": null, "name": "Pure A9", "primary_config_entry": "01JTMK1M36Y0HCAGPBV82YJATB", "serial_number": null, "sw_version": "VM185_A_04.00.00_ELYSIAN", "via_device_id": null}, {"area_id": "dress_room", "config_entries": ["01JTSWG2Q0GKZDYBFSJK0VMBCS"], "config_entries_subentries": {"01JTSWG2Q0GKZDYBFSJK0VMBCS": [null]}, "configuration_url": null, "connections": [["mac", "9c:a2:f4:38:18:a8"]], "created_at": "2025-05-09T06:42:35.533283+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1.0", "id": "a8957999189768fec9853cf2fbf31aab", "identifiers": [["tapo_control", "9c_a2_f4_38_18_a8_tapo_control"]], "labels": [], "manufacturer": "TP-Link", "model": "C210", "model_id": null, "modified_at": "2025-07-29T09:43:07.022817+00:00", "name_by_user": null, "name": "Tapo_C210_18A8", "primary_config_entry": "01JTSWG2Q0GKZDYBFSJK0VMBCS", "serial_number": null, "sw_version": "1.4.7 Build 250625 Rel.58841n", "via_device_id": null}, {"area_id": "garage", "config_entries": ["01JVRPQ1T2WCTJWTZCN3P6M4SK"], "config_entries_subentries": {"01JVRPQ1T2WCTJWTZCN3P6M4SK": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-02-24T10:10:13.411686+00:00", "disabled_by": "user", "entry_type": null, "hw_version": null, "id": "02d57c72bbe21a3c7d569adca470719c", "identifiers": [["tesla_custom", 1493075524044065]], "labels": [], "manufacturer": "Tesla", "model": "Model 3", "model_id": null, "modified_at": "2025-08-23T17:40:42.764566+00:00", "name_by_user": null, "name": "大拉拉", "primary_config_entry": "01JVRPQ1T2WCTJWTZCN3P6M4SK", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01JMNXGBY4P6VCMHFQ3A1EHDSF"], "config_entries_subentries": {"01JMNXGBY4P6VCMHFQ3A1EHDSF": [null]}, "configuration_url": "homeassistant://hacs/repository/340596609", "connections": [], "created_at": "2025-05-23T08:25:16.857863+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "5f11fc6bebd51cb224463907959eb220", "identifiers": [["hacs", "340596609"]], "labels": [], "manufacturer": "osk2", "model": "integration", "model_id": null, "modified_at": "2025-05-23T08:25:16.857918+00:00", "name_by_user": null, "name": "Panasonic Smart App", "primary_config_entry": "01JMNXGBY4P6VCMHFQ3A1EHDSF", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "studio", "config_entries": ["01JVYVQ1W492E97Z26X37DXNKH"], "config_entries_subentries": {"01JVYVQ1W492E97Z26X37DXNKH": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-05-23T08:29:25.557320+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "d318ebca1a1363497d9fa8fadf00ce50", "identifiers": [["panasonic_smart_app", "282E893E627199254D4E9165FAF3195C53BD17BC88A5"]], "labels": [], "manufacturer": "Panasonic", "model": "CS-UX36BA2", "model_id": null, "modified_at": "2025-05-23T15:32:42.415012+00:00", "name_by_user": null, "name": "CS-UX36BA2", "primary_config_entry": "01JVYVQ1W492E97Z26X37DXNKH", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "bedroom", "config_entries": ["01JY8RVWYP6QH01D4PPP60CX0Z"], "config_entries_subentries": {"01JY8RVWYP6QH01D4PPP60CX0Z": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-04-25T09:29:51.560486+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "992032a2a6618aed056b297b4d2f3c58", "identifiers": [["aqara_gateway", "0x54ef441000df34e2"]], "labels": [], "manufacturer": "<PERSON><PERSON><PERSON>", "model": "lumi.gateway.iragl8 ZHWG19LM", "model_id": null, "modified_at": "2025-06-21T08:14:56.674239+00:00", "name_by_user": null, "name": "Aqara Gateway M2 2022", "primary_config_entry": "01JY8RVWYP6QH01D4PPP60CX0Z", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01JY8RVWYP6QH01D4PPP60CX0Z"], "config_entries_subentries": {"01JY8RVWYP6QH01D4PPP60CX0Z": [null]}, "configuration_url": null, "connections": [["zigbee", "0x54ef441001387177"]], "created_at": "2025-06-21T14:59:07.947578+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "f131fe2edccd5c36564cd6d1ca8adec0", "identifiers": [["aqara_gateway", "0x54ef441001387177"]], "labels": [], "manufacturer": "<PERSON><PERSON><PERSON>", "model": "aqara.lock.acn004 ZNMS23LM", "model_id": null, "modified_at": "2025-06-21T14:59:07.947679+00:00", "name_by_user": null, "name": "Aqara Smart Door Lock D200", "primary_config_entry": "01JY8RVWYP6QH01D4PPP60CX0Z", "serial_number": null, "sw_version": 1, "via_device_id": "992032a2a6618aed056b297b4d2f3c58"}, {"area_id": "bedroom", "config_entries": ["01JZFCSZRKR0H1467B4QDKGQR7"], "config_entries_subentries": {"01JZFCSZRKR0H1467B4QDKGQR7": [null]}, "configuration_url": null, "connections": [["mac", "3c:52:a1:bf:aa:2e"]], "created_at": "2025-05-09T07:00:23.321139+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1.0.0", "id": "221592e56240322976cef0e0e9421a33", "identifiers": [["tplink", "80228D60232B3D6A313697C6931E7BD021B6DC36"]], "labels": [], "manufacturer": "TP-Link", "model": "P100", "model_id": null, "modified_at": "2025-07-29T09:56:12.750743+00:00", "name_by_user": null, "name": "Daisy 香氛", "primary_config_entry": "01JZFCSZRKR0H1467B4QDKGQR7", "serial_number": null, "sw_version": "1.4.4 Build 20240514 Rel. 35017", "via_device_id": null}, {"area_id": "bedroom", "config_entries": ["01JZFCVGYSKBB85ZVS8G9F5YRJ"], "config_entries_subentries": {"01JZFCVGYSKBB85ZVS8G9F5YRJ": [null]}, "configuration_url": null, "connections": [["mac", "3c:52:a1:bf:c0:69"]], "created_at": "2025-03-14T03:02:42.131405+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1.0.0", "id": "08052569b095cac3236dd82fdc4b3775", "identifiers": [["tplink", "802202559BC4F8F91484C91B89C12D0621B6B177"]], "labels": [], "manufacturer": "TP-Link", "model": "P100", "model_id": null, "modified_at": "2025-07-06T08:15:25.501900+00:00", "name_by_user": null, "name": "融燭燈", "primary_config_entry": "01JZFCVGYSKBB85ZVS8G9F5YRJ", "serial_number": null, "sw_version": "1.4.4 Build 20240514 Rel. 35017", "via_device_id": null}, {"area_id": null, "config_entries": ["01JNQX6SQV4H932G788M8N2S7Q"], "config_entries_subentries": {"01JNQX6SQV4H932G788M8N2S7Q": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-03-07T08:58:03.821684+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "cf9463f5c845979b870304e3cb78c0de", "identifiers": [["tuya", "eb092e662635214127an1r"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "PD65W排插", "model_id": "4edaizwjjw69po2y", "modified_at": "2025-08-29T15:04:40.382926+00:00", "name_by_user": null, "name": "PD65W排插", "primary_config_entry": "01JNQX6SQV4H932G788M8N2S7Q", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01K015J2DWTWMP7YFQTZBRR1WH"], "config_entries_subentries": {"01K015J2DWTWMP7YFQTZBRR1WH": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-07-13T05:54:06.396558+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "4bceccba2d25770c2ea8ef8d33d7f75d", "identifiers": [["mobile_app", "834705A2-A5F1-46C4-85A6-E464485D5564"]], "labels": [], "manufacturer": "Apple", "model": "iPhone15,4", "model_id": null, "modified_at": "2025-08-12T09:46:25.521670+00:00", "name_by_user": null, "name": "Phibz iPhone15", "primary_config_entry": "01K015J2DWTWMP7YFQTZBRR1WH", "serial_number": null, "sw_version": "18.6", "via_device_id": null}, {"area_id": "unnamed_room", "config_entries": ["01JMMV4BDHQM8D07E8FB4XW83N"], "config_entries_subentries": {"01JMMV4BDHQM8D07E8FB4XW83N": [null]}, "configuration_url": "http://*************:1400/support/review", "connections": [["mac", "38:42:0b:56:fc:b4"], ["upnp", "uuid:RINCON_38420B56FCB401400"]], "created_at": "2025-07-23T07:19:47.794748+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "d267b70d3dfc3e7c74035e3d72335135", "identifiers": [["sonos", "RINCON_38420B56FCB401400"]], "labels": [], "manufacturer": "Sonos", "model": "One SL", "model_id": "S38", "modified_at": "2025-07-23T07:19:47.795096+00:00", "name_by_user": null, "name": "Unnamed Room", "primary_config_entry": "01JMMV4BDHQM8D07E8FB4XW83N", "serial_number": null, "sw_version": "13.4", "via_device_id": null}, {"area_id": "living_room", "config_entries": ["01K1SZWYKFCR09SBQ9JCT50NEY"], "config_entries_subentries": {"01K1SZWYKFCR09SBQ9JCT50NEY": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-08-04T07:32:00.803390+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "ea468220600c47972e879dfce53cd510", "identifiers": [["playstation_network", "965706123255233259_PS5"]], "labels": [], "manufacturer": "Sony Interactive Entertainment", "model": "PlayStation 5", "model_id": null, "modified_at": "2025-08-05T01:55:52.808127+00:00", "name_by_user": null, "name": "PlayStation 5", "primary_config_entry": "01K1SZWYKFCR09SBQ9JCT50NEY", "serial_number": null, "sw_version": null, "via_device_id": "5eaeb7b4094f1e8c1c16f22d8d2d7768"}, {"area_id": null, "config_entries": ["01K1SZWYKFCR09SBQ9JCT50NEY"], "config_entries_subentries": {"01K1SZWYKFCR09SBQ9JCT50NEY": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-08-04T07:32:00.871674+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "5eaeb7b4094f1e8c1c16f22d8d2d7768", "identifiers": [["playstation_network", "965706123255233259"]], "labels": [], "manufacturer": "Sony Interactive Entertainment", "model": null, "model_id": null, "modified_at": "2025-08-04T07:32:00.871730+00:00", "name_by_user": null, "name": "killtw", "primary_config_entry": "01K1SZWYKFCR09SBQ9JCT50NEY", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01K1T83FRY42VDSA4NR53KRYW8"], "config_entries_subentries": {"01K1T83FRY42VDSA4NR53KRYW8": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-08-04T09:55:16.894814+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "bca806b9a773017aaabe5dfd5ad55b95", "identifiers": [["mobile_app", "26720809-0987-4ABD-A361-D7514E3E6F94"]], "labels": [], "manufacturer": "Apple", "model": "iPad14,1", "model_id": null, "modified_at": "2025-08-15T07:08:51.776129+00:00", "name_by_user": null, "name": "iPad mini", "primary_config_entry": "01K1T83FRY42VDSA4NR53KRYW8", "serial_number": null, "sw_version": "18.6", "via_device_id": null}, {"area_id": null, "config_entries": ["01JMNXGBY4P6VCMHFQ3A1EHDSF"], "config_entries_subentries": {"01JMNXGBY4P6VCMHFQ3A1EHDSF": [null]}, "configuration_url": "homeassistant://hacs/repository/497319128", "connections": [], "created_at": "2025-08-04T10:02:26.498275+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "74df3e95cc2e463df4a4ed3a329d2b63", "identifiers": [["hacs", "497319128"]], "labels": [], "manufacturer": "NemesisRE", "model": "plugin", "model_id": null, "modified_at": "2025-08-04T10:02:26.498324+00:00", "name_by_user": null, "name": "Kiosk Mode", "primary_config_entry": "01JMNXGBY4P6VCMHFQ3A1EHDSF", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "garage", "config_entries": ["01JVRPQ1T2WCTJWTZCN3P6M4SK"], "config_entries_subentries": {"01JVRPQ1T2WCTJWTZCN3P6M4SK": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-08-23T17:33:19.421321+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "5262de5e414ad477d2ec1158b4e6ee00", "identifiers": [["tesla_custom", 3744115232034631]], "labels": [], "manufacturer": "Tesla", "model": "Model Y", "model_id": null, "modified_at": "2025-08-29T09:56:20.259505+00:00", "name_by_user": null, "name": "老鴨號", "primary_config_entry": "01JVRPQ1T2WCTJWTZCN3P6M4SK", "serial_number": null, "sw_version": "2025.26.7 3fca5d8d011d", "via_device_id": null}, {"area_id": "living_room", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-02-27T04:47:24.931978+00:00", "disabled_by": null, "entry_type": null, "hw_version": "ZStack3x0 20240710", "id": "6e19d0da92921ebde624434bc16fc18c", "identifiers": [["mqtt", "zigbee2mqtt_bridge_0x00124b002e112d47"]], "labels": [], "manufacturer": "Zigbee2MQTT", "model": "Bridge", "model_id": null, "modified_at": "2025-08-27T08:41:35.288338+00:00", "name_by_user": null, "name": "Zigbee2MQTT Bridge", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": "2.6.0", "via_device_id": null}, {"area_id": "entrance", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-05-29T03:21:09.928301+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "538607e7d6dc8a5972e5f1a55de839da", "identifiers": [["mqtt", "zigbee2mqtt_0xa4c138d20c4e154d"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "5.8Ghz/24Ghz Human presence sensor", "model_id": "ZG-205Z/A", "modified_at": "2025-08-27T08:41:35.294638+00:00", "name_by_user": null, "name": "玄關人在感應", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": "0122052017", "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "entrance", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-05-29T03:22:01.897176+00:00", "disabled_by": null, "entry_type": null, "hw_version": "0", "id": "5ce3c7964819d902d8442a1ab63fd600", "identifiers": [["mqtt", "zigbee2mqtt_0x842712fffe7cec33"]], "labels": [], "manufacturer": "SONOFF", "model": "Zigbee PIR sensor", "model_id": "SNZB-03P", "modified_at": "2025-08-27T08:41:35.296490+00:00", "name_by_user": null, "name": "玄關動作感應", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": "2.2.1", "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "entrance", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-03-10T14:42:18.439169+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "b9e0ca2d876c36ecbca5c57b844fccd3", "identifiers": [["mqtt", "zigbee2mqtt_0xa4c138ba2d3d0757"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "Luminance door sensor", "model_id": "ZG-102ZL", "modified_at": "2025-08-27T08:41:35.298438+00:00", "name_by_user": null, "name": "信箱門感應", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": "0122052017", "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "garage", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-02-28T05:22:57.509774+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "fe1966a71254886045c23f489d1fc898", "identifiers": [["mqtt", "zigbee2mqtt_0xa4c13870139ddef7"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "Luminance door sensor", "model_id": "ZG-102ZL", "modified_at": "2025-08-27T08:41:35.300060+00:00", "name_by_user": null, "name": "車庫門感應", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": "0122052017", "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "garage", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-02-28T05:33:56.000208+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "3e2fcf73cf0cd9c577818845540855e3", "identifiers": [["mqtt", "zigbee2mqtt_0xa4c138591f9083d0"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "Luminance door sensor", "model_id": "ZG-102ZL", "modified_at": "2025-08-27T08:41:35.300757+00:00", "name_by_user": null, "name": "車庫側門感應", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": "0122052017", "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "entrance", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-03-03T08:05:09.958135+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "10bdd060f906e59a717165a78402a666", "identifiers": [["mqtt", "zigbee2mqtt_0x00158d000708748f"]], "labels": [], "manufacturer": "<PERSON><PERSON><PERSON>", "model": "Door and window sensor", "model_id": "MCCGQ11LM", "modified_at": "2025-08-27T08:41:35.301437+00:00", "name_by_user": null, "name": "信箱感應", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "stairs", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-05-29T06:41:38.525955+00:00", "disabled_by": null, "entry_type": null, "hw_version": "0", "id": "5930d242ca62595089a627c7650f261a", "identifiers": [["mqtt", "zigbee2mqtt_0x842712fffe7ce6d3"]], "labels": [], "manufacturer": "SONOFF", "model": "Zigbee PIR sensor", "model_id": "SNZB-03P", "modified_at": "2025-08-27T08:41:35.302109+00:00", "name_by_user": null, "name": "3F 動作感應", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": "2.2.1", "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "stairs", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-02-27T10:51:25.455776+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "076d6ef00e97cdf9bab5f469a66589cc", "identifiers": [["mqtt", "zigbee2mqtt_0xa4c138851b4ddae3"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "5.8Ghz/24Ghz Human presence sensor", "model_id": "ZG-205Z/A", "modified_at": "2025-08-27T08:41:35.303279+00:00", "name_by_user": null, "name": "3F 人在感應", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": "0122052017", "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": null, "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-06-13T10:08:49.008665+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "0c3d51224e5312e52f94529523ef4217", "identifiers": [["mqtt", "zigbee2mqtt_0xa4c138b04b602c88"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "PIR 24Ghz human presence sensor", "model_id": "ZG-204ZM", "modified_at": "2025-08-27T08:41:35.303802+00:00", "name_by_user": null, "name": "0xa4c138b04b602c88", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": "0122052017", "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "living_room", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-06-12T03:48:33.326063+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "abd4ca8aa72365c9d878f5447501e3ff", "identifiers": [["mqtt", "zigbee2mqtt_0xf84477fffe4d19bd"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "Cover motor", "model_id": "TS0601_cover_6", "modified_at": "2025-08-27T08:41:51.283605+00:00", "name_by_user": null, "name": "窗簾右", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "living_room", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-06-12T04:42:27.528877+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "3bec86ab681d6eca641a278eda636128", "identifiers": [["mqtt", "zigbee2mqtt_0xf84477fffe40747a"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "Cover motor", "model_id": "TS0601_cover_6", "modified_at": "2025-08-27T08:41:51.284955+00:00", "name_by_user": null, "name": "窗簾左", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "studio", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-03-03T08:44:32.605177+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "75bc4b62e7f2bce2748779786b20fc69", "identifiers": [["mqtt", "zigbee2mqtt_0x00158d00073a5cae"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "Mi wireless switch", "model_id": "WXKG01LM", "modified_at": "2025-08-27T08:41:35.323889+00:00", "name_by_user": null, "name": "小米無線開關", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "bedroom", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-05-29T06:46:54.758645+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "7528c0c1783e2a8c40a8475d40c3a230", "identifiers": [["mqtt", "zigbee2mqtt_0xa4c138a6ff2209eb"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "Zigbee RGB+CCT light", "model_id": "TS0505B_1", "modified_at": "2025-08-27T08:41:35.341224+00:00", "name_by_user": null, "name": "4F 浴室燈", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "kitchen", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-03-24T08:59:40.163187+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "f86e5f30aecfddeee439f56ca4258a22", "identifiers": [["mqtt", "zigbee2mqtt_0xa4c138ec6626cadd"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "Zigbee RGB+CCT light", "model_id": "TS0505B_1", "modified_at": "2025-08-27T08:41:35.342170+00:00", "name_by_user": null, "name": "餐廳中間崁燈", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "kitchen", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-03-24T09:04:57.327492+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "48c7f1bdbd923233f906ea17c5851965", "identifiers": [["mqtt", "zigbee2mqtt_0xa4c13844a96c99c6"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "Zigbee RGB+CCT light", "model_id": "TS0505B_1", "modified_at": "2025-08-27T08:41:35.342932+00:00", "name_by_user": null, "name": "餐廳右崁燈", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "living_room", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-02-27T06:21:26.223369+00:00", "disabled_by": null, "entry_type": null, "hw_version": "0", "id": "e06295c4a83aa23213f965f99464e001", "identifiers": [["mqtt", "zigbee2mqtt_0x70b3d52b601209fa"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": " GU10 zbeacon Zigbee LED bulb", "model_id": "TS0505", "modified_at": "2025-08-27T08:41:35.343958+00:00", "name_by_user": null, "name": "客廳左吊燈", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": "z.1.0", "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "living_room", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-05-29T07:03:23.760095+00:00", "disabled_by": null, "entry_type": null, "hw_version": "0", "id": "03fd166ff4211d36fecd0fa529845328", "identifiers": [["mqtt", "zigbee2mqtt_0xa4c13814b628056b"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "Zigbee 3.0 18W led light bulb E27 RGBCW", "model_id": "TS0505B_1_1", "modified_at": "2025-08-27T08:41:35.344618+00:00", "name_by_user": null, "name": "客廳右吊燈", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": "z.1.0", "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "kitchen", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-03-09T13:25:13.626084+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "fcbd61166d33b3be8ad3e97b2d3c7aa9", "identifiers": [["mqtt", "zigbee2mqtt_0xa4c138a6755b559d"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "Zigbee RGB+CCT light", "model_id": "TS0505B_1", "modified_at": "2025-08-27T08:41:35.345265+00:00", "name_by_user": null, "name": "餐廳左崁燈", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "living_room", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-02-27T06:34:39.213199+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "ca8ac9e75564df21f9a5332a7fe0367f", "identifiers": [["mqtt", "zigbee2mqtt_0xa4c138385587e524"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "Zigbee RGB+CCT light", "model_id": "TS0505B_1", "modified_at": "2025-08-27T08:41:35.345855+00:00", "name_by_user": null, "name": "客廳左下崁燈", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "living_room", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-02-27T06:34:26.608439+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "e3f9bd6f856938946adac4898aa5d7a6", "identifiers": [["mqtt", "zigbee2mqtt_0xa4c13893935668b4"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "Zigbee RGB+CCT light", "model_id": "TS0505B_1", "modified_at": "2025-08-27T08:41:35.346777+00:00", "name_by_user": null, "name": "客廳左上崁燈", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "living_room", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-02-27T06:34:29.201441+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "01a57b627da2d4b671959f372a007a8c", "identifiers": [["mqtt", "zigbee2mqtt_0xa4c1380a172e5a6a"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "Zigbee RGB+CCT light", "model_id": "TS0505B_1", "modified_at": "2025-08-27T08:41:35.347613+00:00", "name_by_user": null, "name": "客廳右上崁燈", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "living_room", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-02-27T06:34:39.951795+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "b84d7095533a85e6270cbc374b666c9b", "identifiers": [["mqtt", "zigbee2mqtt_0xa4c138d449095da3"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "Zigbee RGB+CCT light", "model_id": "TS0505B_1", "modified_at": "2025-08-27T08:41:35.348392+00:00", "name_by_user": null, "name": "客廳右下崁燈", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "living_room", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-04-15T15:39:22.468797+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "8aa0f743e2d8574d6f1be6bef9ff7d2d", "identifiers": [["mqtt", "zigbee2mqtt_1221051039810110150109113116116_1"]], "labels": [], "manufacturer": "Zigbee2MQTT", "model": "Group", "model_id": null, "modified_at": "2025-08-27T08:41:35.349449+00:00", "name_by_user": null, "name": "客廳吊燈", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": "Zigbee2MQTT 2.6.0", "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "kitchen", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-05-29T08:16:28.673237+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "95729d8fd37df58094a6638f781cd524", "identifiers": [["mqtt", "zigbee2mqtt_1221051039810110150109113116116_4"]], "labels": [], "manufacturer": "Zigbee2MQTT", "model": "Group", "model_id": null, "modified_at": "2025-08-27T08:41:35.350145+00:00", "name_by_user": null, "name": "餐廳燈", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": "Zigbee2MQTT 2.6.0", "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "living_room", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-06-09T14:35:48.570046+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "0c3f901a8928f338e5308540a1858be1", "identifiers": [["mqtt", "zigbee2mqtt_1221051039810110150109113116116_8"]], "labels": [], "manufacturer": "Zigbee2MQTT", "model": "Group", "model_id": null, "modified_at": "2025-08-27T08:41:35.350960+00:00", "name_by_user": null, "name": "客廳崁燈", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": "Zigbee2MQTT 2.6.0", "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "garage", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-05-29T03:23:39.470962+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "3c5ad5b383e79a84a9f8fcaee18bc059", "identifiers": [["mqtt", "zigbee2mqtt_0x6cfd22fffe19abea"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "2-Gang switch with backlight, countdown and inching", "model_id": "TS0002", "modified_at": "2025-08-27T08:41:35.375968+00:00", "name_by_user": null, "name": "車庫燈開關", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "stairs", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-05-29T06:43:00.093399+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "de27361aa8b1d9832a3c1147936d9015", "identifiers": [["mqtt", "zigbee2mqtt_0x70c59cfffe264997"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "2-Gang switch with backlight, countdown and inching", "model_id": "TS0002", "modified_at": "2025-08-27T08:41:35.384572+00:00", "name_by_user": null, "name": "4F 樓梯開關", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "restroom", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-05-29T03:19:02.132441+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "19f6e5120d08f36d6d8347a767c611de", "identifiers": [["mqtt", "zigbee2mqtt_0x6cfd22fffe1c2f31"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "1 gang switch", "model_id": "TS0001", "modified_at": "2025-08-27T08:41:35.394241+00:00", "name_by_user": null, "name": "2F 廁所開關", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "stairs", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-05-29T03:19:24.223754+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "0dca74ef813ff50352769fdbdf8c9926", "identifiers": [["mqtt", "zigbee2mqtt_0x6cfd22fffe1c98f6"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "1 gang switch", "model_id": "TS0001", "modified_at": "2025-08-27T08:41:35.395063+00:00", "name_by_user": null, "name": "3F 壁燈開關", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "garage", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-02-27T06:40:33.719211+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "a7b62555a75828dc3a2b8030813b4f93", "identifiers": [["mqtt", "zigbee2mqtt_0xa4c13840bdea388c"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "Three mode Zigbee Switch", "model_id": "ZG-2002-RF", "modified_at": "2025-08-27T08:41:35.399698+00:00", "name_by_user": null, "name": "車庫門", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "studio", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-05-29T03:30:33.751198+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "aa6fef24989595d653f89a492883b1e5", "identifiers": [["mqtt", "zigbee2mqtt_0x4c97a1fffe5489aa"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "1 gang switch", "model_id": "TS0001", "modified_at": "2025-08-27T08:41:35.401038+00:00", "name_by_user": null, "name": "書房燈開關", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "restroom", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-05-29T03:30:46.150511+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "8fea768a3365055ee42764fdc7e189c6", "identifiers": [["mqtt", "zigbee2mqtt_0x4c97a1fffe5487e3"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "1 gang switch", "model_id": "TS0001", "modified_at": "2025-08-27T08:41:35.401628+00:00", "name_by_user": null, "name": "3F 浴室燈開關", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "dress_room", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-05-29T03:31:24.746224+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "f6dd78cc0862f0003ceb3d3b2b68932d", "identifiers": [["mqtt", "zigbee2mqtt_0x4c97a1fffe548a6d"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "1 gang switch", "model_id": "TS0001", "modified_at": "2025-08-27T08:41:35.402237+00:00", "name_by_user": null, "name": "更衣室開關", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "bedroom", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-05-29T06:43:41.316261+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "bcb525fc6986d9bdb67b923dceea11a9", "identifiers": [["mqtt", "zigbee2mqtt_0x6cfd22fffe1c9c6a"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "1 gang switch", "model_id": "TS0001", "modified_at": "2025-08-27T08:41:35.405288+00:00", "name_by_user": null, "name": "4F 陽台燈開關", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "bedroom", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-02-27T04:57:25.343273+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "df5043868fe7b81ed732d6cd6af67d91", "identifiers": [["mqtt", "zigbee2mqtt_0x6cfd22fffe1cb10f"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "1 gang switch", "model_id": "TS0001", "modified_at": "2025-08-27T08:41:35.407254+00:00", "name_by_user": null, "name": "臥室燈門邊開關", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "bedroom", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-05-29T06:44:56.734598+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "33ff44e9e6cb5d1d775f041fb562e23d", "identifiers": [["mqtt", "zigbee2mqtt_0x6cfd22fffe1a034e"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "1 gang switch", "model_id": "TS0001", "modified_at": "2025-08-27T08:41:35.407845+00:00", "name_by_user": null, "name": "臥室燈牆面開關", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "bedroom", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-05-29T06:45:39.139744+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "ff4d8eae7414d7331cb6545d0a6e3a20", "identifiers": [["mqtt", "zigbee2mqtt_0x4c97a1fffe548970"]], "labels": [], "manufacturer": "<PERSON><PERSON>", "model": "1 gang switch", "model_id": "TS0001", "modified_at": "2025-08-27T08:41:35.408718+00:00", "name_by_user": null, "name": "4F 浴室燈開關", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "living_room", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-02-27T06:20:45.858734+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "f16512482f9cfa5229a190bffd2dd1e3", "identifiers": [["mqtt", "zigbee2mqtt_0x6cfd22fffee80f9c"]], "labels": [], "manufacturer": "<PERSON><PERSON><PERSON><PERSON>", "model": "3 gang switch with neutral", "model_id": "ZM-L03E-Z", "modified_at": "2025-08-27T08:41:35.424064+00:00", "name_by_user": null, "name": "客廳開關", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "stairs", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-05-29T03:18:33.275668+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "df9edd147768f9b7d1cfed4888213b43", "identifiers": [["mqtt", "zigbee2mqtt_0x6cfd22fffee86cd8"]], "labels": [], "manufacturer": "<PERSON><PERSON><PERSON><PERSON>", "model": "3 gang switch with neutral", "model_id": "ZM-L03E-Z", "modified_at": "2025-08-27T08:41:35.427115+00:00", "name_by_user": null, "name": "2F 樓梯開關", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "kitchen", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-05-29T03:20:22.137990+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "94965ff39973a7ae11094f59a87d5ab8", "identifiers": [["mqtt", "zigbee2mqtt_0x6cfd22fffee7f99e"]], "labels": [], "manufacturer": "<PERSON><PERSON><PERSON><PERSON>", "model": "3 gang switch with neutral", "model_id": "ZM-L03E-Z", "modified_at": "2025-08-27T08:41:35.428853+00:00", "name_by_user": null, "name": "廚房開關", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "entrance", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-05-29T03:22:51.906993+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "ebc537038aac081777c99738553cc1a4", "identifiers": [["mqtt", "zigbee2mqtt_0x6cfd22fffee7f78b"]], "labels": [], "manufacturer": "<PERSON><PERSON><PERSON><PERSON>", "model": "3 gang switch with neutral", "model_id": "ZM-L03E-Z", "modified_at": "2025-08-27T08:41:35.430435+00:00", "name_by_user": null, "name": "玄關開關", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "stairs", "config_entries": ["01K3NB126B1305QQ45J8MVA5SQ"], "config_entries_subentries": {"01K3NB126B1305QQ45J8MVA5SQ": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-05-29T03:32:11.768589+00:00", "disabled_by": null, "entry_type": null, "hw_version": "1", "id": "dfb318e1b584b719bac32aa692c7af70", "identifiers": [["mqtt", "zigbee2mqtt_0x6cfd22fffee80636"]], "labels": [], "manufacturer": "<PERSON><PERSON><PERSON><PERSON>", "model": "3 gang switch with neutral", "model_id": "ZM-L03E-Z", "modified_at": "2025-08-27T08:41:35.431977+00:00", "name_by_user": null, "name": "3F 樓梯開關", "primary_config_entry": "01K3NB126B1305QQ45J8MVA5SQ", "serial_number": null, "sw_version": null, "via_device_id": "6e19d0da92921ebde624434bc16fc18c"}, {"area_id": "balcony", "config_entries": ["01K3NGDF3WXPMGMRGY72W510FE"], "config_entries_subentries": {"01K3NGDF3WXPMGMRGY72W510FE": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-07-31T14:04:57.344302+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "2f6129c5bb942eb99edae64fbbdb9aa4", "identifiers": [["lg_thinq", "0d48301ec54c0ed69e79bf699499fea232e42f2e276f68529e47482adf79fb21"]], "labels": [], "manufacturer": "LGE", "model": "SDH_WT4106_TW (DEVICE_WASHTOWER_DRYER)", "model_id": null, "modified_at": "2025-08-27T10:15:48.583175+00:00", "name_by_user": "烘衣機", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "primary_config_entry": "01K3NGDF3WXPMGMRGY72W510FE", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "balcony", "config_entries": ["01K3NGDF3WXPMGMRGY72W510FE"], "config_entries_subentries": {"01K3NGDF3WXPMGMRGY72W510FE": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-07-31T14:04:57.345534+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "2a48b1e999180a283dfb6d32256f697e", "identifiers": [["lg_thinq", "78e20a926e704c7823ea9ad1c68e1b06400ff3ee626e1415306c0845c7e67de4"]], "labels": [], "manufacturer": "LGE", "model": "T_VA_Y___W.B__BTAT (DEVICE_WASHTOWER_WASHER)", "model_id": null, "modified_at": "2025-08-27T10:15:48.586256+00:00", "name_by_user": null, "name": "洗衣機", "primary_config_entry": "01K3NGDF3WXPMGMRGY72W510FE", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "kitchen", "config_entries": ["01K3NGDF3WXPMGMRGY72W510FE"], "config_entries_subentries": {"01K3NGDF3WXPMGMRGY72W510FE": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-07-31T14:04:57.346232+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "8b681e6457c94f817d45958cae9cc3df", "identifiers": [["lg_thinq", "2646248e5270f24bcffdcde890cc10afc9472ee3d18407e82b2c7333dc067335"]], "labels": [], "manufacturer": "LGE", "model": "2RES1VE41PFC2 (DEVICE_REFRIGERATOR)", "model_id": null, "modified_at": "2025-08-27T10:15:48.586691+00:00", "name_by_user": null, "name": "冰箱", "primary_config_entry": "01K3NGDF3WXPMGMRGY72W510FE", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "garage", "config_entries": ["01K3NGDF3WXPMGMRGY72W510FE"], "config_entries_subentries": {"01K3NGDF3WXPMGMRGY72W510FE": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-07-31T14:04:57.346942+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "71580b6a2bf3e5c6a235a08817ba2427", "identifiers": [["lg_thinq", "095ab94995bba665113c13404faa609c6ca4235aa1fec7b4b1a3d4c3ab165fad"]], "labels": [], "manufacturer": "LGE", "model": "FH0D7DDMK0_WIFI (DEVICE_WASHER)", "model_id": null, "modified_at": "2025-08-27T10:15:48.586996+00:00", "name_by_user": null, "name": "洗機", "primary_config_entry": "01K3NGDF3WXPMGMRGY72W510FE", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "dress_room", "config_entries": ["01K3NGDF3WXPMGMRGY72W510FE"], "config_entries_subentries": {"01K3NGDF3WXPMGMRGY72W510FE": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-07-31T14:04:57.347609+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "73cf66f37e56d5df8c3e6313e72272bc", "identifiers": [["lg_thinq", "2e8655818be2f3297cfa71f21ed62bfab9c6dfdb098560a1852ace72d65fa0f0"]], "labels": [], "manufacturer": "LGE", "model": "S5MB_C41_XXR (DEVICE_STYLER)", "model_id": null, "modified_at": "2025-08-27T10:15:48.587271+00:00", "name_by_user": null, "name": "電子衣櫥", "primary_config_entry": "01K3NGDF3WXPMGMRGY72W510FE", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "dress_room", "config_entries": ["01K3NGDF3WXPMGMRGY72W510FE"], "config_entries_subentries": {"01K3NGDF3WXPMGMRGY72W510FE": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-07-31T14:04:57.357777+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "1dfc525d7ca2d30ca0f8946dd354ba29", "identifiers": [["lg_thinq", "bf583d6be42ebfd9b11d8a65e0e387485dd0fc2f5dcbc7872935845d03b8d2c5"]], "labels": [], "manufacturer": "LGE", "model": "DHUM_056905_WW (DEVICE_DEHUMIDIFIER)", "model_id": null, "modified_at": "2025-08-27T10:15:48.591136+00:00", "name_by_user": null, "name": "除濕機", "primary_config_entry": "01K3NGDF3WXPMGMRGY72W510FE", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "dress_room", "config_entries": ["01K3R1P2TF0W58Z3BEP3GYT144"], "config_entries_subentries": {"01K3R1P2TF0W58Z3BEP3GYT144": [null]}, "configuration_url": "http://192.168.1.99:5000", "connections": [], "created_at": "2025-02-22T03:14:16.384089+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "d3ca822365554f0a0a4a7937fe04b3d3", "identifiers": [["synology_dsm", "2150SBR8MJ728"]], "labels": [], "manufacturer": "Synology", "model": "DS920+", "model_id": null, "modified_at": "2025-08-28T09:56:04.464849+00:00", "name_by_user": null, "name": "DSM", "primary_config_entry": "01K3R1P2TF0W58Z3BEP3GYT144", "serial_number": null, "sw_version": "DSM 7.2.2-72806 Update 4", "via_device_id": null}, {"area_id": "dress_room", "config_entries": ["01K3R1P2TF0W58Z3BEP3GYT144"], "config_entries_subentries": {"01K3R1P2TF0W58Z3BEP3GYT144": [null]}, "configuration_url": "http://192.168.1.99:5000", "connections": [], "created_at": "2025-02-22T03:14:16.385469+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "1fac404c85dce5d8c8647290f0cfb06f", "identifiers": [["synology_dsm", "2150SBR8MJ728_sata1"]], "labels": [], "manufacturer": "Seagate", "model": "ST8000VN004-2M2101", "model_id": null, "modified_at": "2025-08-28T09:56:04.466240+00:00", "name_by_user": null, "name": "DSM (Drive 3)", "primary_config_entry": "01K3R1P2TF0W58Z3BEP3GYT144", "serial_number": null, "sw_version": "SC60", "via_device_id": "d3ca822365554f0a0a4a7937fe04b3d3"}, {"area_id": "dress_room", "config_entries": ["01K3R1P2TF0W58Z3BEP3GYT144"], "config_entries_subentries": {"01K3R1P2TF0W58Z3BEP3GYT144": [null]}, "configuration_url": "http://192.168.1.99:5000", "connections": [], "created_at": "2025-02-22T03:14:16.387011+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "ce9cf710369e0f6419f5eff081cb85b4", "identifiers": [["synology_dsm", "2150SBR8MJ728_sata2"]], "labels": [], "manufacturer": "Seagate", "model": "ST8000VN004-2M2101", "model_id": null, "modified_at": "2025-08-28T09:56:04.466934+00:00", "name_by_user": null, "name": "DSM (Drive 4)", "primary_config_entry": "01K3R1P2TF0W58Z3BEP3GYT144", "serial_number": null, "sw_version": "SC60", "via_device_id": "d3ca822365554f0a0a4a7937fe04b3d3"}, {"area_id": "dress_room", "config_entries": ["01K3R1P2TF0W58Z3BEP3GYT144"], "config_entries_subentries": {"01K3R1P2TF0W58Z3BEP3GYT144": [null]}, "configuration_url": "http://192.168.1.99:5000", "connections": [], "created_at": "2025-02-22T03:14:16.388411+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "b719d6cb0697e6d0ef38131aa539c53b", "identifiers": [["synology_dsm", "2150SBR8MJ728_sata3"]], "labels": [], "manufacturer": "Seagate", "model": "ST8000VN004-3CP101", "model_id": null, "modified_at": "2025-08-28T09:56:04.467432+00:00", "name_by_user": null, "name": "DSM (Drive 1)", "primary_config_entry": "01K3R1P2TF0W58Z3BEP3GYT144", "serial_number": null, "sw_version": "SC60", "via_device_id": "d3ca822365554f0a0a4a7937fe04b3d3"}, {"area_id": "dress_room", "config_entries": ["01K3R1P2TF0W58Z3BEP3GYT144"], "config_entries_subentries": {"01K3R1P2TF0W58Z3BEP3GYT144": [null]}, "configuration_url": "http://192.168.1.99:5000", "connections": [], "created_at": "2025-02-22T03:14:16.391675+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "3bbe081e795e41b77e359c1671c817d5", "identifiers": [["synology_dsm", "2150SBR8MJ728_sata4"]], "labels": [], "manufacturer": "WDC     ", "model": "WD60EFRX-68MYMN1", "model_id": null, "modified_at": "2025-08-28T09:56:04.467914+00:00", "name_by_user": null, "name": "DSM (Drive 2)", "primary_config_entry": "01K3R1P2TF0W58Z3BEP3GYT144", "serial_number": null, "sw_version": "82.00A82", "via_device_id": "d3ca822365554f0a0a4a7937fe04b3d3"}, {"area_id": "dress_room", "config_entries": ["01K3R1P2TF0W58Z3BEP3GYT144"], "config_entries_subentries": {"01K3R1P2TF0W58Z3BEP3GYT144": [null]}, "configuration_url": "http://192.168.1.99:5000", "connections": [], "created_at": "2025-02-22T03:14:16.413754+00:00", "disabled_by": null, "entry_type": null, "hw_version": null, "id": "c5a247064e7eaefd92a3e724db4d5e18", "identifiers": [["synology_dsm", "2150SBR8MJ728_volume_1"]], "labels": [], "manufacturer": "Synology", "model": "DS920+", "model_id": null, "modified_at": "2025-08-28T09:56:04.475906+00:00", "name_by_user": null, "name": "DSM (Volume 1)", "primary_config_entry": "01K3R1P2TF0W58Z3BEP3GYT144", "serial_number": null, "sw_version": "DSM 7.2.2-72806 Update 4", "via_device_id": "d3ca822365554f0a0a4a7937fe04b3d3"}], "deleted_devices": [{"area_id": null, "config_entries": ["01JMNXGBY4P6VCMHFQ3A1EHDSF"], "config_entries_subentries": {"01JMNXGBY4P6VCMHFQ3A1EHDSF": [null]}, "connections": [], "created_at": "2025-02-22T04:09:51.714387+00:00", "disabled_by": null, "identifiers": [["hacs", "318182014"]], "id": "1e7e57ac34fcccca6f64679ae6e06021", "labels": [], "modified_at": "2025-03-16T12:47:24.101691+00:00", "name_by_user": null, "orphaned_timestamp": null}, {"area_id": null, "config_entries": ["01JNQX6SQV4H932G788M8N2S7Q"], "config_entries_subentries": {"01JNQX6SQV4H932G788M8N2S7Q": [null]}, "connections": [], "created_at": "2025-03-07T08:58:03.822433+00:00", "disabled_by": null, "identifiers": [["tuya", "eb21f2044fe3f9d5d2yhlg"]], "id": "ccf7e92a4e6a20fde65e5368b48d2eca", "labels": [], "modified_at": "2025-03-30T15:15:12.413749+00:00", "name_by_user": null, "orphaned_timestamp": null}, {"area_id": null, "config_entries": [], "config_entries_subentries": {}, "connections": [], "created_at": "2025-05-29T09:24:11.817364+00:00", "disabled_by": null, "identifiers": [["mqtt", "zigbee2mqtt_1221051039810110150109113116116_undefined"]], "id": "27c689ed726fe3d3016f5dd6249674da", "labels": [], "modified_at": "2025-05-29T10:52:19.967258+00:00", "name_by_user": null, "orphaned_timestamp": 1756283775.4311316}, {"area_id": null, "config_entries": [], "config_entries_subentries": {}, "connections": [], "created_at": "2025-05-29T08:33:28.352178+00:00", "disabled_by": null, "identifiers": [["mqtt", "zigbee2mqtt_1221051039810110150109113116116_5"]], "id": "2bbab4f4c29863aecc4834451e454f95", "labels": [], "modified_at": "2025-05-29T10:52:32.245837+00:00", "name_by_user": null, "orphaned_timestamp": 1756283775.4311316}, {"area_id": null, "config_entries": ["01JNQX6SQV4H932G788M8N2S7Q"], "config_entries_subentries": {"01JNQX6SQV4H932G788M8N2S7Q": [null]}, "connections": [], "created_at": "2025-03-07T08:58:03.821851+00:00", "disabled_by": null, "identifiers": [["tuya", "eb6ee38fb5896ac0acna8y"]], "id": "307de21aca7a20dba7b9ad63b16b10b6", "labels": [], "modified_at": "2025-06-03T07:37:44.422906+00:00", "name_by_user": null, "orphaned_timestamp": null}, {"area_id": null, "config_entries": [], "config_entries_subentries": {}, "connections": [], "created_at": "2025-04-15T15:39:54.363538+00:00", "disabled_by": null, "identifiers": [["mqtt", "zigbee2mqtt_1221051039810110150109113116116_2"]], "id": "4c9c60fd9bebdf832067ab2b2d579de4", "labels": [], "modified_at": "2025-06-09T14:34:43.563906+00:00", "name_by_user": null, "orphaned_timestamp": 1756283775.4311316}, {"area_id": null, "config_entries": [], "config_entries_subentries": {}, "connections": [], "created_at": "2025-04-15T15:40:12.632942+00:00", "disabled_by": null, "identifiers": [["mqtt", "zigbee2mqtt_1221051039810110150109113116116_3"]], "id": "b220e9310bda8284f6572feca11f0ff7", "labels": [], "modified_at": "2025-06-09T14:34:46.302908+00:00", "name_by_user": null, "orphaned_timestamp": 1756283775.4311316}, {"area_id": null, "config_entries": ["01JY8RVWYP6QH01D4PPP60CX0Z"], "config_entries_subentries": {"01JY8RVWYP6QH01D4PPP60CX0Z": [null]}, "connections": [["zigbee", "0x54ef441000de95f1"]], "created_at": "2025-04-25T09:29:51.561899+00:00", "disabled_by": null, "identifiers": [["aqara_gateway", "0x54ef441000de95f1"]], "id": "92a9731073d52d0aa62afbc4ffba12f8", "labels": [], "modified_at": "2025-06-21T14:40:28.358906+00:00", "name_by_user": null, "orphaned_timestamp": null}, {"area_id": null, "config_entries": [], "config_entries_subentries": {}, "connections": [], "created_at": "2025-08-04T07:33:52.135283+00:00", "disabled_by": null, "identifiers": [["proximity", "01K1T00HW5TKHRR7BK931F2X8X"]], "id": "3c08b7bcc78a3edc12fc08f8830c318c", "labels": [], "modified_at": "2025-08-04T07:33:58.149173+00:00", "name_by_user": null, "orphaned_timestamp": 1754292838.1491213}]}}